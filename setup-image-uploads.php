<?php
/**
 * Image Upload Setup Script
 * Creates necessary directories and configures image upload functionality
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

define('CMS_INIT', true);
require_once __DIR__ . '/src/includes/init.php';

try {
    echo "<!DOCTYPE html>\n<html><head><title>Image Upload Setup</title>";
    echo "<style>";
    echo "body { font-family: Arial, sans-serif; max-width: 900px; margin: 50px auto; padding: 20px; line-height: 1.6; }";
    echo ".success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }";
    echo ".info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }";
    echo ".warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; }";
    echo ".error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }";
    echo "table { width: 100%; border-collapse: collapse; margin: 20px 0; }";
    echo "th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }";
    echo "th { background: #f8f9fa; font-weight: bold; }";
    echo ".btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }";
    echo ".btn:hover { background: #0056b3; }";
    echo "</style>";
    echo "</head><body>\n";
    
    echo "<h1>🖼️ Image Upload Setup</h1>\n";
    echo "<p>This script will set up image upload functionality for your CMS thumbnails.</p>\n";
    
    // Check PHP version and extensions
    echo "<h2>📋 System Requirements</h2>\n";
    echo "<table>\n";
    echo "<tr><th>Requirement</th><th>Status</th><th>Details</th></tr>\n";
    
    // PHP Version
    $phpVersion = PHP_VERSION;
    $phpOk = version_compare($phpVersion, '7.4.0', '>=');
    echo "<tr><td>PHP Version</td><td>" . ($phpOk ? '✅' : '❌') . "</td><td>{$phpVersion}" . ($phpOk ? ' (OK)' : ' (7.4+ required)') . "</td></tr>\n";
    
    // GD Extension
    $gdAvailable = extension_loaded('gd');
    echo "<tr><td>GD Extension</td><td>" . ($gdAvailable ? '✅' : '❌') . "</td><td>" . ($gdAvailable ? 'Available' : 'Not installed - required for image processing') . "</td></tr>\n";
    
    // File Upload
    $uploadEnabled = ini_get('file_uploads');
    echo "<tr><td>File Uploads</td><td>" . ($uploadEnabled ? '✅' : '❌') . "</td><td>" . ($uploadEnabled ? 'Enabled' : 'Disabled in php.ini') . "</td></tr>\n";
    
    // Upload limits
    $maxUpload = ini_get('upload_max_filesize');
    $maxPost = ini_get('post_max_size');
    $memoryLimit = ini_get('memory_limit');
    echo "<tr><td>Upload Max Size</td><td>ℹ️</td><td>{$maxUpload}</td></tr>\n";
    echo "<tr><td>Post Max Size</td><td>ℹ️</td><td>{$maxPost}</td></tr>\n";
    echo "<tr><td>Memory Limit</td><td>ℹ️</td><td>{$memoryLimit}</td></tr>\n";
    
    echo "</table>\n";
    
    // Check if we can proceed
    if (!$phpOk || !$gdAvailable || !$uploadEnabled) {
        echo "<div class='error'>";
        echo "<h3>❌ Setup cannot continue</h3>";
        echo "<p>Please fix the requirements above before proceeding.</p>";
        echo "</div>";
        echo "</body></html>\n";
        exit;
    }
    
    // Supported formats
    if ($gdAvailable) {
        $formats = ImageUpload::getSupportedFormats();
        echo "<div class='info'>";
        echo "<h3>📷 Supported Image Formats</h3>";
        echo "<p>" . implode(', ', $formats) . "</p>";
        echo "</div>";
    }
    
    // Create directories
    echo "<h2>📁 Directory Setup</h2>\n";
    $directories = [
        'public/uploads' => 'Main uploads directory',
        'public/uploads/thumbnails' => 'Thumbnail images directory'
    ];
    
    $setupResults = [];
    
    foreach ($directories as $dir => $description) {
        $fullPath = __DIR__ . '/' . $dir;
        
        if (!is_dir($fullPath)) {
            if (mkdir($fullPath, 0755, true)) {
                $setupResults[] = "✅ Created {$dir} - {$description}";
            } else {
                $setupResults[] = "❌ Failed to create {$dir}";
            }
        } else {
            $setupResults[] = "ℹ️ {$dir} already exists - {$description}";
        }
        
        // Check permissions
        if (is_dir($fullPath)) {
            if (is_writable($fullPath)) {
                $setupResults[] = "✅ {$dir} is writable";
            } else {
                $setupResults[] = "⚠️ {$dir} exists but is not writable";
            }
        }
    }
    
    // Create .htaccess for security
    $uploadsHtaccess = __DIR__ . '/public/uploads/.htaccess';
    if (!file_exists($uploadsHtaccess)) {
        $htaccessContent = "# Prevent execution of PHP files in uploads directory\n";
        $htaccessContent .= "Options -ExecCGI\n";
        $htaccessContent .= "AddHandler cgi-script .php .pl .py .jsp .asp .sh .cgi\n";
        $htaccessContent .= "RemoveHandler .php\n";
        $htaccessContent .= "<FilesMatch \"\\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$\">\n";
        $htaccessContent .= "    Require all denied\n";
        $htaccessContent .= "</FilesMatch>\n";
        
        if (file_put_contents($uploadsHtaccess, $htaccessContent)) {
            $setupResults[] = "✅ Created security .htaccess in uploads directory";
        } else {
            $setupResults[] = "⚠️ Could not create security .htaccess file";
        }
    } else {
        $setupResults[] = "ℹ️ Security .htaccess already exists in uploads directory";
    }
    
    // Create thumbnails .htaccess
    $thumbnailsHtaccess = __DIR__ . '/public/uploads/thumbnails/.htaccess';
    if (!file_exists($thumbnailsHtaccess)) {
        $htaccessContent = "# Prevent execution of PHP files in thumbnails directory\n";
        $htaccessContent .= "Options -ExecCGI\n";
        $htaccessContent .= "AddHandler cgi-script .php .pl .py .jsp .asp .sh .cgi\n";
        
        if (file_put_contents($thumbnailsHtaccess, $htaccessContent)) {
            $setupResults[] = "✅ Created security .htaccess in thumbnails directory";
        } else {
            $setupResults[] = "⚠️ Could not create security .htaccess in thumbnails directory";
        }
    } else {
        $setupResults[] = "ℹ️ Security .htaccess already exists in thumbnails directory";
    }
    
    // Display setup results
    echo "<ul>\n";
    foreach ($setupResults as $result) {
        echo "<li>{$result}</li>\n";
    }
    echo "</ul>\n";
    
    // Test image upload functionality
    echo "<h2>🧪 Functionality Test</h2>\n";
    
    try {
        $imageUpload = new ImageUpload();
        echo "<div class='success'>✅ ImageUpload class loaded successfully</div>\n";
        
        // Test directory creation method
        $testDimensions = $imageUpload->getImageDimensions('/uploads/test.jpg');
        echo "<div class='success'>✅ Image processing methods available</div>\n";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ ImageUpload class error: " . htmlspecialchars($e->getMessage()) . "</div>\n";
    }
    
    // Configuration recommendations
    echo "<h2>⚙️ Configuration Recommendations</h2>\n";
    echo "<div class='info'>";
    echo "<h3>PHP Configuration (php.ini)</h3>";
    echo "<p>For optimal image upload performance, consider these settings:</p>";
    echo "<ul>";
    echo "<li><strong>upload_max_filesize:</strong> 10M (currently: {$maxUpload})</li>";
    echo "<li><strong>post_max_size:</strong> 12M (currently: {$maxPost})</li>";
    echo "<li><strong>memory_limit:</strong> 256M (currently: {$memoryLimit})</li>";
    echo "<li><strong>max_execution_time:</strong> 300 (for large image processing)</li>";
    echo "</ul>";
    echo "</div>";
    
    // Usage instructions
    echo "<h2>📖 How to Use Image Upload</h2>\n";
    echo "<div class='info'>";
    echo "<h3>For Content Creators:</h3>";
    echo "<ol>";
    echo "<li>Go to <strong>Admin → Posts → Create New Post</strong></li>";
    echo "<li>In the thumbnail section, click the <strong>'Upload & Crop'</strong> tab</li>";
    echo "<li>Click <strong>'Choose File'</strong> and select an image</li>";
    echo "<li>Use the cropping tool to select the desired area</li>";
    echo "<li>Click <strong>'Apply Crop'</strong> to confirm the selection</li>";
    echo "<li>Save your post - the image will be uploaded and processed</li>";
    echo "</ol>";
    echo "</div>";
    
    // Security notes
    echo "<h2>🔒 Security Features</h2>\n";
    echo "<div class='warning'>";
    echo "<h3>Implemented Security Measures:</h3>";
    echo "<ul>";
    echo "<li><strong>File Type Validation:</strong> Only JPEG, PNG, GIF, and WebP images allowed</li>";
    echo "<li><strong>File Size Limits:</strong> Maximum 5MB per upload</li>";
    echo "<li><strong>Directory Protection:</strong> .htaccess files prevent PHP execution in upload directories</li>";
    echo "<li><strong>Unique Filenames:</strong> Prevents file conflicts and direct access guessing</li>";
    echo "<li><strong>Authentication Required:</strong> Only logged-in admin users can upload</li>";
    echo "<li><strong>CSRF Protection:</strong> Prevents unauthorized upload requests</li>";
    echo "<li><strong>Image Validation:</strong> Files are verified as actual images, not disguised executables</li>";
    echo "</ul>";
    echo "</div>";
    
    // Current upload stats
    $uploadsDir = __DIR__ . '/public/uploads/thumbnails/';
    if (is_dir($uploadsDir)) {
        $files = array_diff(scandir($uploadsDir), ['.', '..', '.htaccess']);
        $fileCount = count($files);
        $totalSize = 0;
        
        foreach ($files as $file) {
            $filePath = $uploadsDir . $file;
            if (is_file($filePath)) {
                $totalSize += filesize($filePath);
            }
        }
        
        echo "<h2>📊 Current Upload Statistics</h2>\n";
        echo "<table>\n";
        echo "<tr><th>Metric</th><th>Value</th></tr>\n";
        echo "<tr><td>Uploaded Images</td><td>{$fileCount}</td></tr>\n";
        echo "<tr><td>Total Size</td><td>" . formatBytes($totalSize) . "</td></tr>\n";
        echo "<tr><td>Upload Directory</td><td>" . htmlspecialchars($uploadsDir) . "</td></tr>\n";
        echo "</table>\n";
        
        if ($fileCount > 0) {
            echo "<h3>Recent Uploads</h3>\n";
            echo "<div style='display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 10px; margin-top: 15px;'>\n";
            
            $recentFiles = array_slice(array_reverse($files), 0, 6);
            foreach ($recentFiles as $file) {
                $filePath = '/uploads/thumbnails/' . $file;
                echo "<div style='text-align: center; border: 1px solid #ddd; padding: 10px; border-radius: 4px;'>\n";
                echo "<img src='{$filePath}' style='max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px;' alt='Upload'>\n";
                echo "<div style='font-size: 11px; margin-top: 5px; word-break: break-all;'>" . htmlspecialchars($file) . "</div>\n";
                echo "</div>\n";
            }
            echo "</div>\n";
        }
    }
    
    // Quick actions
    echo "<h2>🚀 Quick Actions</h2>\n";
    echo "<p>";
    echo "<a href='admin/posts.php?action=new' class='btn'>➕ Create Post with Image</a>";
    echo "<a href='admin/posts.php' class='btn'>📝 Manage Posts</a>";
    echo "<a href='index.php' class='btn'>🌐 View Website</a>";
    echo "<a href='admin/' class='btn'>🏠 Admin Dashboard</a>";
    echo "</p>";
    
    // Troubleshooting
    echo "<h2>🔧 Troubleshooting</h2>\n";
    echo "<div class='info'>";
    echo "<h3>Common Issues and Solutions:</h3>";
    echo "<dl>";
    echo "<dt><strong>Upload fails with 'Permission denied'</strong></dt>";
    echo "<dd>Check that the uploads directory is writable by the web server (chmod 755 or 775)</dd>";
    
    echo "<dt><strong>Large images fail to upload</strong></dt>";
    echo "<dd>Increase PHP memory_limit and upload_max_filesize in php.ini</dd>";
    
    echo "<dt><strong>Cropper tool doesn't appear</strong></dt>";
    echo "<dd>Check browser console for JavaScript errors, ensure Cropper.js is loading</dd>";
    
    echo "<dt><strong>Images appear corrupted</strong></dt>";
    echo "<dd>Verify GD extension is properly installed and functioning</dd>";
    echo "</dl>";
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<h3>🎉 Setup Complete!</h3>";
    echo "<p>Image upload functionality is now ready to use. You can start uploading and cropping thumbnail images for your posts!</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Setup Error</h3>";
    echo "<p>Error during setup: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Helper function to format bytes
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB'];
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

echo "</body></html>\n";
?>