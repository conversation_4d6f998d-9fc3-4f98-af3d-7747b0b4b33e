<?php
/**
 * Reset Login Attempts Script
 * This script clears all failed login attempts to reset the rate limiting ban
 */

// Allow direct access for this utility script
define("CMS_INIT", true);

require_once __DIR__ . "/src/includes/config.php";
require_once __DIR__ . "/src/includes/functions.php";
require_once __DIR__ . "/src/classes/Database.php";
require_once __DIR__ . "/src/classes/Auth.php";

echo "Reset Login Attempts Script\n";
echo "============================\n\n";

try {
    $db = Database::getInstance();
    $auth = new Auth();

    // Get current attempt count
    $totalAttempts = $db->fetchOne(
        "SELECT COUNT(*) as count FROM login_attempts"
    );
    $failedAttempts = $db->fetchOne(
        "SELECT COUNT(*) as count FROM login_attempts WHERE success = 0"
    );

    echo "Current login attempts in database:\n";
    echo "- Total attempts: " . ($totalAttempts["count"] ?? 0) . "\n";
    echo "- Failed attempts: " . ($failedAttempts["count"] ?? 0) . "\n\n";

    // Check for locked accounts
    $lockedAccounts = $db->fetchAll(
        "SELECT username, locked_until
         FROM users
         WHERE locked_until IS NOT NULL AND locked_until > datetime('now')"
    );

    if (!empty($lockedAccounts)) {
        echo "Currently locked accounts:\n";
        foreach ($lockedAccounts as $account) {
            echo "- Username: " .
                $account["username"] .
                " - Locked until: " .
                $account["locked_until"] .
                "\n";
        }
        echo "\n";
    }

    // Show recent failed attempts by IP
    $recentFailed = $db->fetchAll(
        "SELECT ip_address, COUNT(*) as count, MAX(attempted_at) as last_attempt
         FROM login_attempts
         WHERE success = 0 AND attempted_at > datetime('now', '-900 seconds')
         GROUP BY ip_address
         ORDER BY count DESC"
    );

    if (!empty($recentFailed)) {
        echo "Recent failed attempts (last 15 minutes):\n";
        foreach ($recentFailed as $attempt) {
            echo "- IP: " .
                $attempt["ip_address"] .
                " - " .
                $attempt["count"] .
                " attempts - Last: " .
                $attempt["last_attempt"] .
                "\n";
        }
        echo "\n";
    }

    // Clear all login attempts
    $result = $db->query("DELETE FROM login_attempts");

    echo "✅ All login attempts cleared successfully!\n";

    // Unlock all locked accounts
    $unlockResult = $db->query(
        "UPDATE users SET locked_until = NULL WHERE locked_until IS NOT NULL"
    );
    $unlockedCount = $unlockResult->rowCount();

    if ($unlockedCount > 0) {
        echo "✅ Unlocked " . $unlockedCount . " account(s) successfully!\n";
    } else {
        echo "ℹ️  No accounts were locked.\n";
    }

    echo "You can now attempt to login again.\n\n";

    // Verify cleanup
    $remainingAttempts = $db->fetchOne(
        "SELECT COUNT(*) as count FROM login_attempts"
    );
    $remainingLocked = $db->fetchOne(
        "SELECT COUNT(*) as count FROM users WHERE locked_until IS NOT NULL AND locked_until > datetime('now')"
    );

    echo "Verification: " .
        ($remainingAttempts["count"] ?? 0) .
        " attempts remaining in database.\n";
    echo "Verification: " .
        ($remainingLocked["count"] ?? 0) .
        " accounts still locked.\n";

    if (
        ($remainingAttempts["count"] ?? 0) === 0 &&
        ($remainingLocked["count"] ?? 0) === 0
    ) {
        echo "✅ Rate limiting and account locks reset successful!\n";
    } else {
        echo "⚠️  Warning: Some attempts or locks may still remain.\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\nLogin credentials:\n";
echo "Username: admin\n";
echo "Password: admin\n\n";

echo "You can now access the admin panel at: http://localhost:8000/admin/\n";
