<?php
/**
 * Session Timeout Debug Script
 * Comprehensive debugging tool to identify session timeout issues
 */

define('CMS_INIT', true);
require_once __DIR__ . '/src/includes/init.php';

// Clear any existing output
if (ob_get_level()) {
    ob_clean();
}

header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>\n<html><head><title>Session Timeout Debug</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.success { color: green; font-weight: bold; }
.section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
.highlight { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; }
</style></head><body>\n";

echo "<h1>🔍 Session Timeout Debug Tool</h1>\n";
echo "<p>This tool will help identify why sessions are timing out early.</p>\n";

// Handle test actions
if (isset($_POST['create_session'])) {
    Session::set('user_id', 999);
    Session::set('username', 'debug_user');
    Session::set('debug_created', time());
    // Don't call storeInDatabase to avoid complications
    echo "<div class='highlight success'>✅ Debug session created!</div>\n";
}

if (isset($_POST['extend_session'])) {
    Session::extend();
    echo "<div class='highlight success'>🔄 Session extended!</div>\n";
}

if (isset($_POST['destroy_session'])) {
    Session::destroy();
    echo "<div class='highlight warning'>🗑️ Session destroyed!</div>\n";
}

// Function to format time differences
function formatTimeDiff($seconds) {
    if ($seconds < 60) return $seconds . 's';
    if ($seconds < 3600) return floor($seconds/60) . 'm ' . ($seconds%60) . 's';
    return floor($seconds/3600) . 'h ' . floor(($seconds%3600)/60) . 'm ' . ($seconds%60) . 's';
}

// Current timestamp
$now = time();
echo "<div class='section'>";
echo "<h2>⏰ Current Time Information</h2>";
echo "<table>";
echo "<tr><th>Property</th><th>Value</th></tr>";
echo "<tr><td>Current Unix Timestamp</td><td>$now</td></tr>";
echo "<tr><td>Current DateTime</td><td>" . date('Y-m-d H:i:s', $now) . "</td></tr>";
echo "<tr><td>PHP Timezone</td><td>" . date_default_timezone_get() . "</td></tr>";
echo "<tr><td>Server Time</td><td>" . date('Y-m-d H:i:s') . "</td></tr>";
echo "</table></div>";

// Session Status
echo "<div class='section'>";
echo "<h2>📊 Current Session Status</h2>";

if (Session::has('user_id')) {
    $sessionStart = Session::get('debug_created', 'Unknown');
    $sessionAge = $sessionStart !== 'Unknown' ? ($now - $sessionStart) : 'Unknown';
    
    echo "<table>";
    echo "<tr><th>Property</th><th>Value</th></tr>";
    echo "<tr><td>User ID</td><td>" . Session::get('user_id') . "</td></tr>";
    echo "<tr><td>Username</td><td>" . Session::get('username', 'N/A') . "</td></tr>";
    echo "<tr><td>Session ID</td><td>" . session_id() . "</td></tr>";
    echo "<tr><td>Session Created</td><td>" . ($sessionStart !== 'Unknown' ? date('Y-m-d H:i:s', $sessionStart) : 'Unknown') . "</td></tr>";
    echo "<tr><td>Session Age</td><td>" . ($sessionAge !== 'Unknown' ? formatTimeDiff($sessionAge) : 'Unknown') . "</td></tr>";
    echo "<tr><td>Last Regeneration</td><td>" . (Session::has('last_regeneration') ? date('Y-m-d H:i:s', Session::get('last_regeneration')) : 'Never') . "</td></tr>";
    echo "</table>";
    
    // Database session info (if exists)
    $db = Database::getInstance();
    $sessionId = session_id();
    $dbSession = $db->fetchOne(
        "SELECT *, 
         datetime(created_at) as created_formatted,
         datetime(last_activity) as last_activity_formatted,
         (strftime('%s', 'now') - strftime('%s', last_activity)) as seconds_since_activity
         FROM sessions WHERE id = ?",
        [$sessionId]
    );
    
    if ($dbSession) {
        echo "<h3>Database Session Info</h3>";
        echo "<table>";
        echo "<tr><th>Property</th><th>Value</th></tr>";
        echo "<tr><td>Created At</td><td>" . $dbSession['created_formatted'] . "</td></tr>";
        echo "<tr><td>Last Activity</td><td>" . $dbSession['last_activity_formatted'] . "</td></tr>";
        echo "<tr><td>Seconds Since Activity</td><td>" . $dbSession['seconds_since_activity'] . "</td></tr>";
        echo "<tr><td>IP Address</td><td>" . $dbSession['ip_address'] . "</td></tr>";
        echo "<tr><td>User Agent</td><td>" . substr($dbSession['user_agent'], 0, 100) . "...</td></tr>";
        echo "</table>";
        
        $timeRemaining = Config::SESSION_LIFETIME - $dbSession['seconds_since_activity'];
        if ($timeRemaining <= 0) {
            echo "<div class='highlight error'>⚠️ Session has EXPIRED according to database!</div>";
        } elseif ($timeRemaining <= Config::SESSION_WARNING_TIME) {
            echo "<div class='highlight warning'>⚠️ Session expires in " . formatTimeDiff($timeRemaining) . "</div>";
        } else {
            echo "<div class='highlight success'>✅ Session valid for " . formatTimeDiff($timeRemaining) . "</div>";
        }
    } else {
        echo "<div class='highlight error'>❌ No database session found!</div>";
    }
} else {
    echo "<p class='error'>❌ No active session</p>";
}
echo "</div>";

// Configuration Analysis
echo "<div class='section'>";
echo "<h2>⚙️ Configuration Analysis</h2>";

echo "<h3>Application Configuration</h3>";
echo "<table>";
echo "<tr><th>Setting</th><th>Value</th><th>Notes</th></tr>";
echo "<tr><td>SESSION_LIFETIME</td><td>" . Config::SESSION_LIFETIME . " seconds (" . formatTimeDiff(Config::SESSION_LIFETIME) . ")</td><td>" . (Config::SESSION_LIFETIME >= 1800 ? "✅ Good" : "⚠️ Short") . "</td></tr>";
echo "<tr><td>SESSION_WARNING_TIME</td><td>" . Config::SESSION_WARNING_TIME . " seconds (" . formatTimeDiff(Config::SESSION_WARNING_TIME) . ")</td><td>Warning threshold</td></tr>";
echo "<tr><td>SESSION_REGENERATE_INTERVAL</td><td>" . Config::SESSION_REGENERATE_INTERVAL . " seconds (" . formatTimeDiff(Config::SESSION_REGENERATE_INTERVAL) . ")</td><td>ID regeneration</td></tr>";
echo "</table>";

echo "<h3>PHP Session Configuration (Before App Settings)</h3>";
$originalMaxLifetime = ini_get('session.gc_maxlifetime');
$originalCookieLifetime = ini_get('session.cookie_lifetime');

echo "<table>";
echo "<tr><th>Setting</th><th>Default Value</th><th>Status</th></tr>";
echo "<tr><td>session.gc_maxlifetime</td><td>$originalMaxLifetime seconds (" . formatTimeDiff($originalMaxLifetime) . ")</td><td>" . ($originalMaxLifetime < Config::SESSION_LIFETIME ? "<span class='error'>❌ PROBLEM: Shorter than app setting!</span>" : "✅ OK") . "</td></tr>";
echo "<tr><td>session.cookie_lifetime</td><td>$originalCookieLifetime seconds</td><td>" . ($originalCookieLifetime == 0 ? "✅ Session cookie (until browser closes)" : "Session cookie with lifetime") . "</td></tr>";
echo "</table>";

// Start a temporary session to check if settings are applied
Session::start();

echo "<h3>PHP Session Configuration (After App Settings)</h3>";
echo "<table>";
echo "<tr><th>Setting</th><th>Current Value</th><th>Status</th></tr>";
echo "<tr><td>session.gc_maxlifetime</td><td>" . ini_get('session.gc_maxlifetime') . " seconds (" . formatTimeDiff(ini_get('session.gc_maxlifetime')) . ")</td><td>" . (ini_get('session.gc_maxlifetime') >= Config::SESSION_LIFETIME ? "✅ OK" : "<span class='error'>❌ Still too short!</span>") . "</td></tr>";
echo "<tr><td>session.cookie_lifetime</td><td>" . ini_get('session.cookie_lifetime') . " seconds</td><td>" . (ini_get('session.cookie_lifetime') == Config::SESSION_LIFETIME ? "✅ Matches app setting" : "<span class='warning'>⚠️ Different from app setting</span>") . "</td></tr>";
echo "<tr><td>session.gc_probability</td><td>" . ini_get('session.gc_probability') . "</td><td>Cleanup probability</td></tr>";
echo "<tr><td>session.gc_divisor</td><td>" . ini_get('session.gc_divisor') . "</td><td>Cleanup divisor (1 in " . ini_get('session.gc_divisor') . " chance)</td></tr>";
echo "<tr><td>session.cookie_httponly</td><td>" . (ini_get('session.cookie_httponly') ? 'Yes' : 'No') . "</td><td>" . (ini_get('session.cookie_httponly') ? "✅ Secure" : "⚠️ Not secure") . "</td></tr>";
echo "<tr><td>session.cookie_secure</td><td>" . (ini_get('session.cookie_secure') ? 'Yes' : 'No') . "</td><td>" . (ini_get('session.cookie_secure') ? "✅ HTTPS only" : "⚠️ HTTP allowed") . "</td></tr>";
echo "<tr><td>session.use_strict_mode</td><td>" . (ini_get('session.use_strict_mode') ? 'Yes' : 'No') . "</td><td>" . (ini_get('session.use_strict_mode') ? "✅ Secure" : "⚠️ Not secure") . "</td></tr>";
echo "</table>";
echo "</div>";

// Session Storage Analysis
echo "<div class='section'>";
echo "<h2>💾 Session Storage Analysis</h2>";

$savePath = session_save_path() ?: sys_get_temp_dir();
$saveHandler = ini_get('session.save_handler');

echo "<table>";
echo "<tr><th>Property</th><th>Value</th><th>Status</th></tr>";
echo "<tr><td>Save Handler</td><td>$saveHandler</td><td>" . ($saveHandler === 'files' ? "File-based storage" : "Custom handler") . "</td></tr>";
echo "<tr><td>Save Path</td><td>$savePath</td><td>" . (is_writable($savePath) ? "✅ Writable" : "❌ Not writable") . "</td></tr>";
echo "</table>";

// Check for session files if using file handler
if ($saveHandler === 'files' && is_dir($savePath)) {
    $sessionFiles = glob($savePath . '/sess_*');
    $sessionCount = count($sessionFiles);
    echo "<p><strong>Session Files:</strong> $sessionCount files found in $savePath</p>";
    
    if ($sessionCount > 0) {
        echo "<p><strong>Recent Session Files:</strong></p>";
        echo "<table>";
        echo "<tr><th>File</th><th>Size</th><th>Modified</th><th>Age</th></tr>";
        
        // Show last 5 session files
        $recentFiles = array_slice($sessionFiles, -5);
        foreach ($recentFiles as $file) {
            $basename = basename($file);
            $size = filesize($file);
            $mtime = filemtime($file);
            $age = $now - $mtime;
            
            echo "<tr>";
            echo "<td>" . $basename . "</td>";
            echo "<td>" . $size . " bytes</td>";
            echo "<td>" . date('Y-m-d H:i:s', $mtime) . "</td>";
            echo "<td>" . formatTimeDiff($age) . " ago</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}
echo "</div>";

// Potential Issues Analysis
echo "<div class='section'>";
echo "<h2>🚨 Potential Issues Detected</h2>";

$issues = [];
$warnings = [];

// Check for critical issues
if (ini_get('session.gc_maxlifetime') < Config::SESSION_LIFETIME) {
    $issues[] = "PHP's session.gc_maxlifetime (" . ini_get('session.gc_maxlifetime') . "s) is shorter than your app's SESSION_LIFETIME (" . Config::SESSION_LIFETIME . "s). This will cause early session cleanup!";
}

if ($originalMaxLifetime < 1800) {
    $issues[] = "Server's default session.gc_maxlifetime is very short ($originalMaxLifetime seconds = " . formatTimeDiff($originalMaxLifetime) . "). This suggests aggressive session cleanup.";
}

// Check for warnings
if (Config::SESSION_LIFETIME > 7200) {
    $warnings[] = "Session lifetime is very long (" . formatTimeDiff(Config::SESSION_LIFETIME) . "). Consider security implications.";
}

if (ini_get('session.gc_probability') > 10) {
    $warnings[] = "Session garbage collection probability is high (" . ini_get('session.gc_probability') . "%). This might cause frequent cleanups.";
}

if (!ini_get('session.cookie_secure') && isset($_SERVER['HTTPS'])) {
    $warnings[] = "Session cookies are not marked as secure despite HTTPS being available.";
}

if (empty($issues) && empty($warnings)) {
    echo "<p class='success'>✅ No obvious issues detected in configuration.</p>";
} else {
    if (!empty($issues)) {
        echo "<h3 class='error'>Critical Issues:</h3>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li class='error'>❌ $issue</li>";
        }
        echo "</ul>";
    }
    
    if (!empty($warnings)) {
        echo "<h3 class='warning'>Warnings:</h3>";
        echo "<ul>";
        foreach ($warnings as $warning) {
            echo "<li class='warning'>⚠️ $warning</li>";
        }
        echo "</ul>";
    }
}
echo "</div>";

// Recommendations
echo "<div class='section'>";
echo "<h2>💡 Recommendations</h2>";

if (!empty($issues)) {
    echo "<h3>To Fix Critical Issues:</h3>";
    echo "<ol>";
    
    if (ini_get('session.gc_maxlifetime') < Config::SESSION_LIFETIME) {
        echo "<li><strong>Update your php.ini:</strong> Set <code>session.gc_maxlifetime = " . Config::SESSION_LIFETIME . "</code> (or higher)</li>";
        echo "<li><strong>Or use .htaccess:</strong> Add <code>php_value session.gc_maxlifetime " . Config::SESSION_LIFETIME . "</code></li>";
        echo "<li><strong>Or set in PHP:</strong> Ensure <code>ini_set('session.gc_maxlifetime', " . Config::SESSION_LIFETIME . ");</code> is called BEFORE <code>session_start()</code></li>";
    }
    
    echo "</ol>";
}

echo "<h3>General Recommendations:</h3>";
echo "<ul>";
echo "<li>Test session timeout by creating a session and checking back after 15-20 minutes</li>";
echo "<li>Monitor server logs for session-related errors</li>";
echo "<li>Consider using database-only sessions if file-based sessions are problematic</li>";
echo "<li>Implement session extension on user activity (AJAX heartbeat)</li>";
echo "<li>Add client-side session timeout warnings</li>";
echo "</ul>";
echo "</div>";

// Test Actions
echo "<div class='section'>";
echo "<h2>🧪 Test Actions</h2>";
echo "<form method='post' style='margin: 10px 0;'>";

if (!Session::has('user_id')) {
    echo "<button type='submit' name='create_session' style='padding: 10px; background: #28a745; color: white; border: none; cursor: pointer; margin-right: 10px;'>🔑 Create Debug Session</button>";
} else {
    echo "<button type='submit' name='extend_session' style='padding: 10px; background: #007bff; color: white; border: none; cursor: pointer; margin-right: 10px;'>🔄 Extend Session</button>";
    echo "<button type='submit' name='destroy_session' style='padding: 10px; background: #dc3545; color: white; border: none; cursor: pointer; margin-right: 10px;'>🗑️ Destroy Session</button>";
}

echo "<button type='button' onclick='window.location.reload()' style='padding: 10px; background: #6c757d; color: white; border: none; cursor: pointer;'>🔄 Refresh</button>";
echo "</form>";

echo "<p><strong>Testing Process:</strong></p>";
echo "<ol>";
echo "<li>Create a debug session</li>";
echo "<li>Note the current time and session details</li>";
echo "<li>Come back in 15-20 minutes and refresh this page</li>";
echo "<li>Check if the session is still active</li>";
echo "<li>If it's gone, check which issue from above is the cause</li>";
echo "</ol>";
echo "</div>";

// Auto-refresh option
echo "<script>";
echo "let autoRefresh = false;";
echo "function toggleAutoRefresh() {";
echo "  autoRefresh = !autoRefresh;";
echo "  const btn = document.getElementById('autoRefreshBtn');";
echo "  if (autoRefresh) {";
echo "    btn.textContent = '⏸️ Stop Auto-Refresh';";
echo "    btn.style.backgroundColor = '#ffc107';";
echo "    setTimeout(function() { if (autoRefresh) window.location.reload(); }, 60000);";
echo "  } else {";
echo "    btn.textContent = '▶️ Start Auto-Refresh';";
echo "    btn.style.backgroundColor = '#28a745';";
echo "  }";
echo "}";
echo "</script>";

echo "<div style='margin: 20px 0;'>";
echo "<button id='autoRefreshBtn' onclick='toggleAutoRefresh()' style='padding: 10px; background: #28a745; color: white; border: none; cursor: pointer;'>▶️ Start Auto-Refresh (every 60s)</button>";
echo "</div>";

echo "<hr>";
echo "<p><small>Generated at " . date('Y-m-d H:i:s') . " | <a href='test-session-timeout.php'>Switch to Simple Test</a></small></p>";

echo "</body></html>";
?>