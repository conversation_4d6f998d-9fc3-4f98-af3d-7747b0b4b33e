#!/bin/bash

# CMS Development Server Startup Script
# Starts a PHP development server for local testing with proper signal handling

set -e  # Exit on any error

# Configuration
DEFAULT_PORT=8000
DEFAULT_HOST="localhost"
DOCUMENT_ROOT="public"
PHP_INI_FILE="php.ini"
PHP_INI_SETTINGS="-d display_errors=1 -d error_reporting=E_ALL -d log_errors=1"
UPLOAD_SETTINGS="-d upload_max_filesize=10M -d post_max_size=10M -d memory_limit=256M -d max_execution_time=300"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1  # Port is in use
    else
        return 0  # Port is available
    fi
}

# Function to find available port
find_available_port() {
    local start_port=$1
    local port=$start_port
    
    while [ $port -le $((start_port + 10)) ]; do
        if check_port $port; then
            echo $port
            return 0
        fi
        port=$((port + 1))
    done
    
    return 1  # No available port found
}

# Function to cleanup on exit
cleanup() {
    print_status "Received interrupt signal (Ctrl+C)"
    if [ ! -z "$SERVER_PID" ]; then
        print_status "Stopping PHP development server (PID: $SERVER_PID)..."
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
        print_success "Server stopped cleanly"
    fi
    exit 0
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -p, --port PORT     Port to run the server on (default: $DEFAULT_PORT)"
    echo "  -h, --host HOST     Host to bind to (default: $DEFAULT_HOST)"
    echo "  -d, --document-root Document root directory (default: $DOCUMENT_ROOT)"
    echo "  --no-ini            Use inline directives instead of php.ini file"
    echo "  --debug             Enable verbose debugging output"
    echo "  --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                  # Start server on localhost:8000"
    echo "  $0 -p 8080          # Start server on localhost:8080"
    echo "  $0 -h 0.0.0.0       # Start server accessible from any IP"
    echo "  $0 --no-ini         # Use inline PHP directives (fixes CSS issues)"
    echo "  $0 --debug          # Show detailed debugging information"
    echo ""
}

# Parse command line arguments
PORT=$DEFAULT_PORT
HOST=$DEFAULT_HOST
DOC_ROOT=$DOCUMENT_ROOT
USE_INI_FILE=true
DEBUG_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -h|--host)
            HOST="$2"
            shift 2
            ;;
        -d|--document-root)
            DOC_ROOT="$2"
            shift 2
            ;;
        --no-ini)
            USE_INI_FILE=false
            shift
            ;;
        --debug)
            DEBUG_MODE=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate port number
if ! [[ "$PORT" =~ ^[0-9]+$ ]] || [ "$PORT" -lt 1 ] || [ "$PORT" -gt 65535 ]; then
    print_error "Invalid port number: $PORT"
    exit 1
fi

# Check if document root exists
if [ ! -d "$DOC_ROOT" ]; then
    print_error "Document root directory '$DOC_ROOT' does not exist"
    exit 1
fi

# Check if PHP is available
if ! command -v php &> /dev/null; then
    print_error "PHP is not installed or not in PATH"
    exit 1
fi

# Check PHP version
PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2)
print_status "Using PHP version: $PHP_VERSION"

# Check if local php.ini exists and use it (use absolute path)
PHP_INI_PATH="$(pwd)/$PHP_INI_FILE"
if [ "$USE_INI_FILE" = true ] && [ -f "$PHP_INI_PATH" ]; then
    print_status "Using local PHP configuration file: $PHP_INI_PATH"
    PHP_CONFIG="-c $PHP_INI_PATH"
    if [ "$DEBUG_MODE" = true ]; then
        echo "PHP INI file contents:"
        head -n 10 "$PHP_INI_PATH" | sed 's/^/  /'
        echo "  ..."
    fi
else
    if [ "$USE_INI_FILE" = false ]; then
        print_status "Using inline PHP directives (--no-ini specified)"
    else
        print_warning "Local php.ini not found, using inline directives for upload settings"
    fi
    PHP_CONFIG=""
    PHP_INI_SETTINGS="$PHP_INI_SETTINGS $UPLOAD_SETTINGS"
    if [ "$DEBUG_MODE" = true ]; then
        print_status "PHP directives: $PHP_INI_SETTINGS $UPLOAD_SETTINGS"
    fi
fi

# Check if requested port is available
if ! check_port $PORT; then
    print_warning "Port $PORT is already in use"
    
    # Try to find an available port
    AVAILABLE_PORT=$(find_available_port $PORT)
    if [ $? -eq 0 ]; then
        print_status "Found available port: $AVAILABLE_PORT"
        PORT=$AVAILABLE_PORT
    else
        print_error "Could not find an available port near $PORT"
        exit 1
    fi
fi

# Set up signal handlers for clean exit
trap cleanup SIGINT SIGTERM

# Display startup information
echo ""
print_status "Starting CMS Development Server..."
print_status "Document Root: $(pwd)/$DOC_ROOT"
print_status "Server Address: http://$HOST:$PORT"
print_status "Press Ctrl+C to stop the server"
echo ""

# Start the PHP development server in background
cd "$DOC_ROOT"
php $PHP_CONFIG $PHP_INI_SETTINGS -S "$HOST:$PORT" router.php &
SERVER_PID=$!

# Check if server started successfully
sleep 1
if ! kill -0 $SERVER_PID 2>/dev/null; then
    print_error "Failed to start PHP development server"
    exit 1
fi

print_success "Server started successfully!"
print_status "Server URL: http://$HOST:$PORT"
print_status "Admin Panel: http://$HOST:$PORT/admin/"
print_status "Debug Upload: http://$HOST:$PORT/debug-upload.php"
print_status "Server PID: $SERVER_PID"
echo ""

# Quick verification that CSS is loading
if command -v curl &> /dev/null; then
    sleep 1
    if [ "$DEBUG_MODE" = true ]; then
        print_status "Testing CSS file loading..."
        curl -I "http://$HOST:$PORT/assets/style.css" 2>&1 | head -n 5
    fi
    
    if curl -s "http://$HOST:$PORT/assets/style.css" -o /dev/null; then
        print_success "CSS file is loading correctly"
    else
        print_warning "CSS file may not be loading - try using --no-ini flag"
        if [ "$DEBUG_MODE" = true ]; then
            print_status "Full curl output for debugging:"
            curl -v "http://$HOST:$PORT/assets/style.css" 2>&1 | head -n 20
        fi
    fi
fi

print_warning "Note: This is a development server. Do not use in production!"
echo ""

# Wait for the server process
wait $SERVER_PID
