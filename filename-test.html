<!DOCTYPE html>
<html><head><title>Filename Uniqueness Test</title><style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
.container { max-width: 1000px; margin: 0 auto; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.success { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.info { color: blue; font-weight: bold; }
.box { margin: 15px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
.highlight { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; border-radius: 3px; }
button { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; color: white; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-danger { background: #dc3545; }
.btn-warning { background: #ffc107; color: black; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
.duplicate { background-color: #ffebee; }
.unique { background-color: #e8f5e8; }
</style></head><body>
<div class='container'><h1>🔧 Filename Uniqueness Test</h1><p>This test verifies that the filename generation always produces unique filenames.</p><div class='box'><h2>📋 Test Overview</h2><p>This tool tests the filename generation system to ensure all generated filenames are unique.</p><h3>The Problem:</h3><ul><li>Original method used <code>time()</code> which only has 1-second precision</li><li>Multiple uploads within the same second could get identical filenames</li><li>No check for existing files on disk</li><li>Could cause files to overwrite each other</li></ul><h3>The Fix:</h3><ul><li>Uses <code>microtime(true)</code> for microsecond precision</li><li>Increased random bytes from 4 to 6 for better collision resistance</li><li>Added file existence check to guarantee uniqueness</li><li>Retry mechanism if collision still occurs</li></ul></div><div class='box'><h2>🧪 Run Tests</h2><form method='post'><h3>Choose a Test:</h3><button type='submit' name='action' value='test_basic' class='btn-primary'>📊 Basic Tests (Different filenames)</button><button type='submit' name='action' value='test_rapid' class='btn-warning'>⚡ Rapid Generation (Stress test)</button><button type='submit' name='action' value='test_old_method' class='btn-danger'>🔍 Old Method Demo (Show problem)</button><button type='submit' name='action' value='test_all' class='btn-success'>🧪 Complete Test Suite</button><button type='button' onclick='window.location.reload()' class='btn-primary' style='background: #6c757d; margin-left: 20px;'>🔄 Refresh</button></form></div><div class='box'><h2>🔧 Code Comparison</h2><h3>❌ Old Method (Problematic):</h3><pre>private function generateUniqueFilename($originalName, $extension) {
    $safeName = preg_replace('/[^a-zA-Z0-9_-]/', '', $originalName);
    $safeName = substr($safeName, 0, 50);
    
    if (empty($safeName)) {
        $safeName = 'image';
    }
    
    $timestamp = time(); // ❌ Only 1-second precision
    $random = bin2hex(random_bytes(4)); // ❌ Only 8 hex chars
    
    return $safeName . '_' . $timestamp . '_' . $random . '.' . $extension;
    // ❌ No existence check!
}</pre><h3>✅ New Method (Fixed):</h3><pre>private function generateUniqueFilename($originalName, $extension) {
    $safeName = preg_replace('/[^a-zA-Z0-9_-]/', '', $originalName);
    $safeName = substr($safeName, 0, 50);
    
    if (empty($safeName)) {
        $safeName = 'image';
    }
    
    $maxAttempts = 100;
    $attempts = 0;
    
    do {
        $timestamp = microtime(true); // ✅ Microsecond precision
        $timestampStr = sprintf('%.3f', $timestamp);
        $timestampStr = str_replace('.', '', $timestampStr);
        $random = bin2hex(random_bytes(6)); // ✅ 12 hex chars
        
        $filename = $safeName . '_' . $timestampStr . '_' . $random . '.' . $extension;
        $filepath = $this->uploadDir . $filename;
        
        $attempts++;
        
    } while (file_exists($filepath) && $attempts < $maxAttempts); // ✅ Existence check!
    
    if ($attempts >= $maxAttempts) {
        throw new Exception('Unable to generate unique filename');
    }
    
    return $filename;
}</pre></div></div><hr><p><small>Generated at 2025-06-09 17:35:57</small></p></body></html>