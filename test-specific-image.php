<?php
/**
 * Test Specific Image Upload
 * Tests the problematic PNG file specifically
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

if (!defined('CMS_INIT')) {
    define('CMS_INIT', true);
    require_once 'src/includes/init.php';
}

// Check if user is authenticated
if (!$auth->isAuthenticated()) {
    die('Authentication required. Please login first.');
}

$testImagePath = __DIR__ . '/df70c661-9b9b-46e0-a13b-d1f948f490c1.png';
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        echo "<h3>Testing Specific PNG Image:</h3>";
        echo "<pre>";
        
        // Check if the test image exists
        if (!file_exists($testImagePath)) {
            throw new Exception('Test image file not found: ' . $testImagePath);
        }
        
        echo "Test image found: " . $testImagePath . "\n";
        echo "File size: " . filesize($testImagePath) . " bytes (" . round(filesize($testImagePath)/1024/1024, 2) . " MB)\n";
        
        // Get image info
        $imageInfo = getimagesize($testImagePath);
        if ($imageInfo) {
            echo "Dimensions: " . $imageInfo[0] . "x" . $imageInfo[1] . "\n";
            echo "MIME type: " . $imageInfo['mime'] . "\n";
            echo "Bits: " . $imageInfo['bits'] . "\n";
        }
        
        // Test GD processing
        echo "\nTesting GD processing...\n";
        $gdImage = imagecreatefrompng($testImagePath);
        if ($gdImage) {
            echo "✓ GD can load PNG\n";
            echo "GD Image width: " . imagesx($gdImage) . "\n";
            echo "GD Image height: " . imagesy($gdImage) . "\n";
            
            // Test memory usage
            echo "Memory usage after loading: " . round(memory_get_usage()/1024/1024, 2) . " MB\n";
            echo "Peak memory usage: " . round(memory_get_peak_usage()/1024/1024, 2) . " MB\n";
            
            imagedestroy($gdImage);
        } else {
            echo "✗ GD cannot load PNG\n";
        }
        
        // Simulate file upload array
        $simulatedUpload = [
            'name' => 'df70c661-9b9b-46e0-a13b-d1f948f490c1.png',
            'type' => 'image/png',
            'size' => filesize($testImagePath),
            'tmp_name' => $testImagePath,
            'error' => UPLOAD_ERR_OK
        ];
        
        echo "\nSimulated upload array:\n";
        print_r($simulatedUpload);
        
        // Test crop data if provided
        $cropData = null;
        if (isset($_POST['crop_data']) && !empty($_POST['crop_data'])) {
            echo "\nCrop data provided: " . $_POST['crop_data'] . "\n";
            $cropData = json_decode($_POST['crop_data'], true);
            if (json_last_error() === JSON_ERROR_NONE) {
                echo "✓ Crop data parsed successfully\n";
                print_r($cropData);
            } else {
                echo "✗ Crop data JSON error: " . json_last_error_msg() . "\n";
            }
        }
        
        // Create temporary copy for testing (since move_uploaded_file won't work with regular file)
        $tempCopy = sys_get_temp_dir() . '/test_upload_' . time() . '.png';
        if (!copy($testImagePath, $tempCopy)) {
            throw new Exception('Failed to create temporary copy');
        }
        
        $simulatedUpload['tmp_name'] = $tempCopy;
        echo "\nTemporary copy created: " . $tempCopy . "\n";
        
        // Test ImageUpload class
        echo "\nTesting ImageUpload class...\n";
        $imageUpload = new ImageUpload();
        
        try {
            $uploadedPath = $imageUpload->uploadImage($simulatedUpload, $cropData);
            echo "✓ Upload successful! Path: " . $uploadedPath . "\n";
            
            // Check if file actually exists
            $fullPath = PUBLIC_PATH . $uploadedPath;
            echo "Full file path: " . $fullPath . "\n";
            echo "File exists after upload: " . (file_exists($fullPath) ? 'YES' : 'NO') . "\n";
            
            if (file_exists($fullPath)) {
                $filesize = filesize($fullPath);
                echo "File size after upload: " . $filesize . " bytes\n";
                
                // Get final image dimensions
                $finalInfo = getimagesize($fullPath);
                if ($finalInfo) {
                    echo "Final dimensions: " . $finalInfo[0] . "x" . $finalInfo[1] . "\n";
                }
                
                $message = "✓ Upload successful! File saved to: " . $uploadedPath;
            } else {
                $error = "✗ File was not found after upload";
            }
            
        } catch (Exception $e) {
            echo "✗ ImageUpload error: " . $e->getMessage() . "\n";
            echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
            $error = "Upload failed: " . $e->getMessage();
        }
        
        // Clean up temp file
        if (file_exists($tempCopy)) {
            unlink($tempCopy);
            echo "\nTemporary file cleaned up\n";
        }
        
        echo "</pre>";
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
        echo "<pre>Exception: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "</pre>";
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Specific PNG Image</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
        form { background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin: 20px 0; }
        input, button { padding: 10px; margin: 5px 0; }
        button { background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .image-preview { max-width: 300px; border: 1px solid #ddd; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Test Specific PNG Image Upload</h1>
    <p>This page tests the problematic PNG file: <strong>df70c661-9b9b-46e0-a13b-d1f948f490c1.png</strong></p>
    
    <?php if (file_exists($testImagePath)): ?>
        <div>
            <h3>Test Image Preview:</h3>
            <img src="df70c661-9b9b-46e0-a13b-d1f948f490c1.png" alt="Test Image" class="image-preview">
            <p>File size: <?php echo round(filesize($testImagePath)/1024/1024, 2); ?> MB</p>
        </div>
    <?php else: ?>
        <div class="error">Test image file not found: <?php echo $testImagePath; ?></div>
    <?php endif; ?>
    
    <?php if ($message): ?>
        <div class="success"><?php echo htmlspecialchars($message); ?></div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="error"><?php echo htmlspecialchars($error); ?></div>
    <?php endif; ?>
    
    <form method="POST">
        <?php echo csrfTokenField(); ?>
        
        <h3>Test Options</h3>
        
        <div>
            <label>Crop Data (JSON - optional):</label><br>
            <input type="text" name="crop_data" placeholder='{"x":100,"y":100,"width":500,"height":300}' style="width: 100%;">
            <small>Example crop data to test cropping functionality</small>
        </div>
        
        <div>
            <button type="submit">Test Upload This Specific Image</button>
        </div>
    </form>
    
    <h3>System Information</h3>
    <pre><?php
        echo "PHP Version: " . PHP_VERSION . "\n";
        echo "Memory Limit: " . ini_get('memory_limit') . "\n";
        echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";
        echo "Post Max Size: " . ini_get('post_max_size') . "\n";
        echo "Max Execution Time: " . ini_get('max_execution_time') . " seconds\n";
        
        if (extension_loaded('gd')) {
            $gdInfo = gd_info();
            echo "\nGD Extension:\n";
            echo "GD Version: " . $gdInfo['GD Version'] . "\n";
            echo "PNG Support: " . ($gdInfo['PNG Support'] ? 'YES' : 'NO') . "\n";
            echo "JPEG Support: " . ($gdInfo['JPEG Support'] ? 'YES' : 'NO') . "\n";
            echo "GIF Support: " . ($gdInfo['GIF Create Support'] ? 'YES' : 'NO') . "\n";
            echo "WebP Support: " . ($gdInfo['WebP Support'] ? 'YES' : 'NO') . "\n";
        } else {
            echo "\nGD Extension: NOT AVAILABLE\n";
        }
        
        $uploadDir = PUBLIC_PATH . '/uploads/thumbnails/';
        echo "\nUpload Directory: " . $uploadDir . "\n";
        echo "Directory Exists: " . (is_dir($uploadDir) ? 'YES' : 'NO') . "\n";
        echo "Directory Writable: " . (is_writable($uploadDir) ? 'YES' : 'NO') . "\n";
        
        if (file_exists($testImagePath)) {
            echo "\nTest Image Analysis:\n";
            echo "File exists: YES\n";
            echo "File readable: " . (is_readable($testImagePath) ? 'YES' : 'NO') . "\n";
            echo "File size: " . filesize($testImagePath) . " bytes\n";
            echo "MIME type: " . mime_content_type($testImagePath) . "\n";
            
            $imageInfo = getimagesize($testImagePath);
            if ($imageInfo) {
                echo "Image dimensions: " . $imageInfo[0] . "x" . $imageInfo[1] . "\n";
                echo "Image MIME: " . $imageInfo['mime'] . "\n";
                echo "Image bits: " . $imageInfo['bits'] . "\n";
                echo "Image channels: " . ($imageInfo['channels'] ?? 'N/A') . "\n";
            }
        } else {
            echo "\nTest Image: NOT FOUND\n";
        }
    ?></pre>
    
    <h3>Recent Uploads</h3>
    <pre><?php
        $uploadDir = PUBLIC_PATH . '/uploads/thumbnails/';
        if (is_dir($uploadDir)) {
            $files = glob($uploadDir . '*');
            if (empty($files)) {
                echo "No files in uploads directory\n";
            } else {
                echo "Files in uploads directory:\n";
                foreach ($files as $file) {
                    if (is_file($file)) {
                        $size = filesize($file);
                        $time = date('Y-m-d H:i:s', filemtime($file));
                        echo basename($file) . " - " . $size . " bytes - " . $time . "\n";
                    }
                }
            }
        } else {
            echo "Upload directory does not exist\n";
        }
    ?></pre>
</body>
</html>