#!/usr/bin/env php
<?php
/**
 * Diagnostic Script: Check for Cleanup Issues
 *
 * This script analyzes the cleanup logic and checks for potential false positive
 * deletions by examining patterns and testing them against sample data.
 *
 * Usage: php diagnose-cleanup-issues.php
 */

// Set error reporting
error_reporting(E_ALL);
ini_set("display_errors", 1);

// Ensure this is running from command line
if (php_sapi_name() !== "cli") {
    echo "This script must be run from the command line.\n";
    exit(1);
}

echo "🔍 Cleanup Logic Diagnostic Script\n";
echo "==================================\n\n";

try {
    // Connect to database
    $dbPath = __DIR__ . "/database/cms.db";
    if (!file_exists($dbPath)) {
        throw new Exception("Database file not found: " . $dbPath);
    }

    $pdo = new PDO("sqlite:" . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "📊 Current Database State:\n";
    echo "========================\n";

    // Show all current posts
    $stmt = $pdo->prepare(
        "SELECT id, title, created_at FROM posts ORDER BY id"
    );
    $stmt->execute();
    $allPosts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "Total posts in database: " . count($allPosts) . "\n\n";

    foreach ($allPosts as $post) {
        echo "   📝 ID {$post["id"]}: {$post["title"]}\n";
        echo "      Created: {$post["created_at"]}\n\n";
    }

    echo "🧪 Testing Current Cleanup Pattern:\n";
    echo "===================================\n";

    // Test current cleanup script pattern
    $currentPattern = "THISISATEST%";
    echo "Current pattern: '$currentPattern'\n";

    $stmt = $pdo->prepare("SELECT id, title FROM posts WHERE title LIKE ?");
    $stmt->execute([$currentPattern]);
    $matchedByCurrentPattern = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "Posts that would be deleted by current cleanup script: " .
        count($matchedByCurrentPattern) .
        "\n";
    foreach ($matchedByCurrentPattern as $post) {
        echo "   🎯 Would delete: ID {$post["id"]} - {$post["title"]}\n";
    }

    if (empty($matchedByCurrentPattern)) {
        echo "   ✅ No posts match current cleanup pattern - database is clean\n";
    }

    echo "\n🕵️ Analyzing Migration Script Patterns:\n";
    echo "======================================\n";

    // Test the patterns that were used in migration
    $migrationPatterns = [
        "Test from Browser Interface",
        "API Test Post for Verification",
        "Test Post via API",
        "API Test Post for Cleanup",
        "Test Post for Cleanup%", // This one is suspicious!
        "Image Download Test",
        "Gaming Post Test",
        "Film Review: Test Movie",
    ];

    echo "Migration patterns that were used:\n";
    foreach ($migrationPatterns as $i => $pattern) {
        echo "   " . ($i + 1) . ". '$pattern'\n";

        if (strpos($pattern, "%") !== false) {
            echo "      ⚠️  WILDCARD PATTERN - matches anything starting with: '" .
                str_replace("%", "", $pattern) .
                "'\n";
        }
    }

    echo "\n🧪 Testing Migration Patterns Against Sample Data:\n";
    echo "=================================================\n";

    // Test sample legitimate post titles to see if they would be caught
    $sampleLegitimatePostTitles = [
        "Test Your Knowledge: Programming Quiz",
        "API Documentation for Beginners",
        "Gaming Post Analysis",
        "Film Review: The Matrix",
        "Test Post for Production Use",
        "Complete Guide to Testing",
        "Important Production Content",
        "Test Drive: New Car Review",
        "Test Results from Lab",
        "Gaming Post: Best Games 2024",
        "API Integration Tutorial",
    ];

    echo "Testing legitimate post titles against migration patterns:\n\n";

    foreach ($sampleLegitimatePostTitles as $title) {
        echo "Testing title: '$title'\n";

        $wouldBeDeleted = false;
        $matchingPattern = "";

        foreach ($migrationPatterns as $pattern) {
            if (strpos($pattern, "%") !== false) {
                // LIKE pattern with wildcard
                $likePattern = str_replace("%", ".*", $pattern);
                $regex =
                    "/^" .
                    preg_quote(str_replace("%", "", $pattern), "/") .
                    "/i";
                if (preg_match($regex, $title)) {
                    $wouldBeDeleted = true;
                    $matchingPattern = $pattern;
                    break;
                }
            } else {
                // Exact match
                if ($title === $pattern) {
                    $wouldBeDeleted = true;
                    $matchingPattern = $pattern;
                    break;
                }
            }
        }

        if ($wouldBeDeleted) {
            echo "   ❌ WOULD BE DELETED by pattern: '$matchingPattern'\n";
        } else {
            echo "   ✅ Safe - would not be deleted\n";
        }
        echo "\n";
    }

    echo "🔍 Checking for Problematic Patterns:\n";
    echo "====================================\n";

    $problematicPatterns = [];

    foreach ($migrationPatterns as $pattern) {
        if (strpos($pattern, "%") !== false) {
            $basePattern = str_replace("%", "", $pattern);
            if (strlen($basePattern) < 10) {
                $problematicPatterns[] = $pattern;
                echo "   ⚠️  RISKY: '$pattern' - very short base pattern: '$basePattern'\n";
            }
        }
    }

    if (empty($problematicPatterns)) {
        echo "   ✅ No obviously problematic patterns found\n";
    }

    echo "\n📋 Recommended Actions:\n";
    echo "======================\n";

    if (!empty($problematicPatterns)) {
        echo "❌ ISSUES FOUND:\n";
        echo "   1. The migration script used overly broad patterns\n";
        echo "   2. Pattern 'Test Post for Cleanup%' could match legitimate posts\n";
        echo "   3. Review what was deleted during migration\n\n";

        echo "🔧 FIXES NEEDED:\n";
        echo "   1. Update migration script to use exact matches only\n";
        echo "   2. Add exclusion patterns for legitimate content\n";
        echo "   3. Consider restoring accidentally deleted posts\n";
    } else {
        echo "✅ Current cleanup logic appears safe\n";
        echo "✅ Only 'THISISATEST%' pattern is used for ongoing cleanup\n";
    }

    echo "\n🎯 Current Status:\n";
    echo "=================\n";
    echo "✅ Current cleanup script is SAFE - only targets 'THISISATEST%'\n";
    echo "⚠️  Previous migration may have been too aggressive\n";
    echo "📊 Current database has " . count($allPosts) . " posts remaining\n";

    // Check if we can find any backup or restore information
    echo "\n💾 Recovery Options:\n";
    echo "===================\n";
    echo "1. Check if database backup exists before migration\n";
    echo "2. Review migration logs for detailed deletion list\n";
    echo "3. If legitimate posts were deleted, they may need manual restoration\n";
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    error_log("Diagnostic Error: " . $e->getMessage());
    exit(1);
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    error_log("Diagnostic Error: " . $e->getMessage());
    exit(1);
}

echo "\nDiagnostic complete! 🔍\n";

?>
