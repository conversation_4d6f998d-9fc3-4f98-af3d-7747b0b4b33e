<?php
/**
 * Test Script: Description Field Feature
 * Verifies that the description field is working correctly
 */

if (!defined('CMS_INIT')) {
    define('CMS_INIT', true);
    require_once 'src/includes/init.php';
}

echo "Testing Description Field Feature\n";
echo "=================================\n\n";

$testResults = [];

try {
    $db = Database::getInstance();
    $postManager = new Post();
    
    // Test 1: Check if description column exists
    echo "Test 1: Checking if description column exists...\n";
    $result = $db->getPdo()->query("PRAGMA table_info(posts)");
    $columns = $result->fetchAll(PDO::FETCH_ASSOC);
    
    $hasDescription = false;
    foreach ($columns as $column) {
        if ($column['name'] === 'description') {
            $hasDescription = true;
            break;
        }
    }
    
    if ($hasDescription) {
        echo "✓ Description column exists\n";
        $testResults['column_exists'] = true;
    } else {
        echo "✗ Description column missing\n";
        $testResults['column_exists'] = false;
    }
    
    // Test 2: Check existing posts have placeholder descriptions
    echo "\nTest 2: Checking existing posts have placeholder descriptions...\n";
    $existingPosts = $db->fetchAll("SELECT id, title, description FROM posts WHERE description IS NOT NULL");
    
    if (!empty($existingPosts)) {
        echo "✓ Found " . count($existingPosts) . " posts with descriptions\n";
        foreach ($existingPosts as $post) {
            $descLength = strlen($post['description']);
            echo "  - Post ID {$post['id']}: \"{$post['title']}\" (Description: {$descLength} chars)\n";
        }
        $testResults['existing_descriptions'] = true;
    } else {
        echo "✗ No posts found with descriptions\n";
        $testResults['existing_descriptions'] = false;
    }
    
    // Test 3: Test creating a new post with description
    echo "\nTest 3: Testing post creation with description...\n";
    
    // Create a test user first if needed
    $testUser = $db->fetchOne("SELECT id FROM users LIMIT 1");
    if (!$testUser) {
        echo "✗ No users found - cannot test post creation\n";
        $testResults['create_post'] = false;
    } else {
        $testTitle = "Test Post with Description " . time();
        $testContent = "This is a test post content to verify the description field functionality.";
        $testDescription = "This is a test description for SEO and social media previews. It should be used instead of auto-generated excerpts.";
        
        try {
            $newPostId = $postManager->createPost(
                $testTitle,
                $testContent,
                false, // not draft
                $testUser['id'],
                'tech',
                null, // no thumbnail
                $testDescription
            );
            
            if ($newPostId) {
                echo "✓ Created test post with ID: {$newPostId}\n";
                
                // Verify the post was created with description
                $createdPost = $postManager->getPost($newPostId);
                if ($createdPost && $createdPost['description'] === $testDescription) {
                    echo "✓ Post description saved correctly\n";
                    $testResults['create_post'] = true;
                } else {
                    echo "✗ Post description not saved correctly\n";
                    $testResults['create_post'] = false;
                }
                
                // Clean up test post
                $postManager->deletePost($newPostId);
                echo "✓ Test post cleaned up\n";
                
            } else {
                echo "✗ Failed to create test post\n";
                $testResults['create_post'] = false;
            }
            
        } catch (Exception $e) {
            echo "✗ Error creating test post: " . $e->getMessage() . "\n";
            $testResults['create_post'] = false;
        }
    }
    
    // Test 4: Test description validation
    echo "\nTest 4: Testing description validation...\n";
    
    if ($testUser) {
        try {
            // Test with description too long (over 500 characters)
            $longDescription = str_repeat("This is a very long description that exceeds the limit. ", 20); // ~1000 chars
            
            $postManager->createPost(
                "Test Validation " . time(),
                "Test content",
                true, // draft
                $testUser['id'],
                'tech',
                null,
                $longDescription
            );
            
            echo "✗ Long description validation failed - should have thrown exception\n";
            $testResults['validation'] = false;
            
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'must not exceed 500 characters') !== false) {
                echo "✓ Description length validation working correctly\n";
                $testResults['validation'] = true;
            } else {
                echo "✗ Unexpected validation error: " . $e->getMessage() . "\n";
                $testResults['validation'] = false;
            }
        }
    } else {
        echo "⚠ Skipped validation test - no test user available\n";
        $testResults['validation'] = 'skipped';
    }
    
    // Test 5: Test SEO meta tag generation (simulate)
    echo "\nTest 5: Testing SEO meta tag generation...\n";
    
    $samplePost = [
        'title' => 'Sample Post Title',
        'description' => 'This is a sample description for testing SEO meta tags.',
        'content' => 'This is the full content of the post which would normally be used for excerpt generation.',
        'category' => 'tech',
        'author_name' => 'Test Author',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s'),
        'thumbnail_url' => '/uploads/sample-image.jpg'
    ];
    
    // Simulate meta description generation
    $metaDescription = !empty($samplePost['description']) 
        ? $samplePost['description'] 
        : generateExcerpt($samplePost['content'], 160);
    
    if ($metaDescription === $samplePost['description']) {
        echo "✓ Description field used for meta tags correctly\n";
        $testResults['meta_tags'] = true;
    } else {
        echo "✗ Description field not used for meta tags\n";
        $testResults['meta_tags'] = false;
    }
    
    // Test Summary
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "TEST SUMMARY\n";
    echo str_repeat("=", 50) . "\n";
    
    $passedTests = 0;
    $totalTests = 0;
    
    foreach ($testResults as $testName => $result) {
        $totalTests++;
        if ($result === true) {
            $passedTests++;
            echo "✓ " . ucwords(str_replace('_', ' ', $testName)) . "\n";
        } elseif ($result === false) {
            echo "✗ " . ucwords(str_replace('_', ' ', $testName)) . "\n";
        } else {
            echo "⚠ " . ucwords(str_replace('_', ' ', $testName)) . " (skipped)\n";
        }
    }
    
    echo "\nResults: {$passedTests}/{$totalTests} tests passed\n";
    
    if ($passedTests === $totalTests) {
        echo "\n🎉 All tests passed! Description field feature is working correctly.\n";
    } else {
        echo "\n⚠️  Some tests failed. Please check the implementation.\n";
    }
    
    echo "\nNext steps:\n";
    echo "1. Visit /admin/posts.php?action=new to test the form interface\n";
    echo "2. Create a new post with a description\n";
    echo "3. Check the public page to see description in use\n";
    echo "4. Verify SEO meta tags in browser developer tools\n";
    
} catch (Exception $e) {
    echo "Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>