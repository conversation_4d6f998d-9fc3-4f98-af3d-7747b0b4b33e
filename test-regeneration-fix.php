<?php
/**
 * Session Regeneration Fix Test
 * Tests the fix for session ID regeneration causing early logout
 */

define('CMS_INIT', true);
require_once __DIR__ . '/src/includes/init.php';

// Clear any existing output
if (ob_get_level()) {
    ob_clean();
}

header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>\n<html><head><title>Session Regeneration Fix Test</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
.container { max-width: 900px; margin: 0 auto; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.success { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.info { color: blue; font-weight: bold; }
.box { margin: 15px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
.highlight { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; border-radius: 3px; }
button { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; color: white; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-danger { background: #dc3545; }
.btn-warning { background: #ffc107; color: black; }
.step { background: #f8f9fa; padding: 10px; margin: 10px 0; border-left: 4px solid #007bff; }
</style></head><body>\n";

echo "<div class='container'>";
echo "<h1>🔧 Session Regeneration Fix Test</h1>";
echo "<p>This test verifies that session ID regeneration no longer causes early logout.</p>";

$now = time();
$nowFormatted = date('Y-m-d H:i:s', $now);

// Handle test actions
$action = $_POST['action'] ?? '';
$message = '';

switch ($action) {
    case 'create_session':
        // Create a test session with database storage
        Session::set('user_id', 99999);
        Session::set('username', 'regeneration_test_user');
        Session::set('test_start_time', $now);
        Session::storeInDatabase(99999);
        $message = "<div class='highlight success'>✅ Test session created with database storage</div>";
        break;
        
    case 'force_regeneration':
        if (Session::has('user_id')) {
            $oldId = session_id();
            // Force regeneration by updating last_regeneration to trigger it
            $_SESSION['last_regeneration'] = $now - Config::SESSION_REGENERATE_INTERVAL - 1;
            
            // Now call start() which will trigger regeneration
            Session::start();
            
            $newId = session_id();
            $message = "<div class='highlight info'>🔄 Forced session regeneration<br>";
            $message .= "Old ID: " . substr($oldId, 0, 16) . "...<br>";
            $message .= "New ID: " . substr($newId, 0, 16) . "...</div>";
        } else {
            $message = "<div class='highlight error'>❌ No session to regenerate</div>";
        }
        break;
        
    case 'test_validation':
        if (Session::has('user_id')) {
            $result = Session::validateFromDatabase();
            if ($result) {
                $message = "<div class='highlight success'>✅ Session validation PASSED - database session found and valid</div>";
            } else {
                $message = "<div class='highlight error'>❌ Session validation FAILED - this should not happen with the fix!</div>";
            }
        } else {
            $message = "<div class='highlight error'>❌ No session to validate</div>";
        }
        break;
        
    case 'destroy_session':
        Session::destroy();
        $message = "<div class='highlight warning'>🗑️ Session destroyed</div>";
        break;
}

echo $message;

// Current Status
echo "<div class='box'>";
echo "<h2>📊 Current Session Status</h2>";
echo "<p><strong>Current Time:</strong> $nowFormatted</p>";

if (Session::has('user_id')) {
    $sessionId = session_id();
    $userId = Session::get('user_id');
    $username = Session::get('username');
    $testStartTime = Session::get('test_start_time', 'Unknown');
    $lastRegen = Session::get('last_regeneration', 'Never');
    
    echo "<table>";
    echo "<tr><th>Property</th><th>Value</th></tr>";
    echo "<tr><td>Current Session ID</td><td>" . substr($sessionId, 0, 20) . "...</td></tr>";
    echo "<tr><td>User ID</td><td>$userId</td></tr>";
    echo "<tr><td>Username</td><td>$username</td></tr>";
    
    if ($testStartTime !== 'Unknown') {
        $age = $now - $testStartTime;
        echo "<tr><td>Test Session Age</td><td>" . formatTime($age) . "</td></tr>";
    }
    
    if ($lastRegen !== 'Never') {
        $regenAge = $now - $lastRegen;
        echo "<tr><td>Last Regeneration</td><td>" . date('Y-m-d H:i:s', $lastRegen) . " (" . formatTime($regenAge) . " ago)</td></tr>";
        
        $nextRegen = $lastRegen + Config::SESSION_REGENERATE_INTERVAL;
        $timeToRegen = $nextRegen - $now;
        if ($timeToRegen <= 0) {
            echo "<tr><td style='background: #fff3cd;'>Regeneration Status</td><td style='background: #fff3cd;'>⚠️ DUE (will trigger on next session_start)</td></tr>";
        } else {
            echo "<tr><td>Next Regeneration</td><td>In " . formatTime($timeToRegen) . "</td></tr>";
        }
    } else {
        echo "<tr><td>Regeneration Status</td><td>Will regenerate on next session_start</td></tr>";
    }
    
    echo "</table>";
    
    // Check database session
    echo "<h3>Database Session Verification</h3>";
    try {
        $db = Database::getInstance();
        $dbSession = $db->fetchOne(
            "SELECT *, 
             datetime(created_at) as created_formatted,
             datetime(last_activity) as last_activity_formatted,
             (strftime('%s', 'now') - strftime('%s', last_activity)) as seconds_since_activity
             FROM sessions WHERE id = ? AND user_id = ?",
            [$sessionId, $userId]
        );
        
        if ($dbSession) {
            echo "<table>";
            echo "<tr><th>Property</th><th>Value</th><th>Status</th></tr>";
            echo "<tr><td>Database Session ID</td><td>" . substr($dbSession['id'], 0, 20) . "...</td><td>" . ($dbSession['id'] === $sessionId ? "✅ MATCHES" : "❌ MISMATCH") . "</td></tr>";
            echo "<tr><td>Created At</td><td>" . $dbSession['created_formatted'] . "</td><td>Database record</td></tr>";
            echo "<tr><td>Last Activity</td><td>" . $dbSession['last_activity_formatted'] . "</td><td>Last validation</td></tr>";
            echo "<tr><td>Seconds Since Activity</td><td>" . $dbSession['seconds_since_activity'] . "</td><td>Activity age</td></tr>";
            echo "</table>";
            
            $timeRemaining = Config::SESSION_LIFETIME - $dbSession['seconds_since_activity'];
            if ($timeRemaining > 0) {
                echo "<div class='highlight success'>✅ Database session is VALID (expires in " . formatTime($timeRemaining) . ")</div>";
            } else {
                echo "<div class='highlight error'>❌ Database session is EXPIRED</div>";
            }
        } else {
            echo "<div class='highlight error'>❌ NO DATABASE SESSION FOUND for current session ID!<br>";
            echo "This indicates the regeneration fix may not be working properly.</div>";
            
            // Check if there are any sessions for this user
            $userSessions = $db->fetchAll(
                "SELECT id, datetime(created_at) as created_formatted, datetime(last_activity) as last_activity_formatted FROM sessions WHERE user_id = ?",
                [$userId]
            );
            
            if (!empty($userSessions)) {
                echo "<h4>Existing sessions for user $userId:</h4>";
                echo "<table>";
                echo "<tr><th>Session ID</th><th>Created</th><th>Last Activity</th></tr>";
                foreach ($userSessions as $session) {
                    echo "<tr>";
                    echo "<td>" . substr($session['id'], 0, 20) . "...</td>";
                    echo "<td>" . $session['created_formatted'] . "</td>";
                    echo "<td>" . $session['last_activity_formatted'] . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                echo "<p class='warning'>⚠️ The database has sessions but none match the current session ID. This suggests regeneration is not updating the database.</p>";
            }
        }
    } catch (Exception $e) {
        echo "<div class='highlight error'>❌ Database error: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<p class='error'>❌ No active session</p>";
}

echo "</div>";

// Test Steps
echo "<div class='box'>";
echo "<h2>🧪 Test the Fix</h2>";

echo "<form method='post'>";

if (!Session::has('user_id')) {
    echo "<div class='step'>";
    echo "<h3>Step 1: Create Test Session</h3>";
    echo "<p>First, create a session with database storage.</p>";
    echo "<button type='submit' name='action' value='create_session' class='btn-success'>🔑 Create Test Session</button>";
    echo "</div>";
} else {
    echo "<div class='step'>";
    echo "<h3>Step 2: Force Session Regeneration</h3>";
    echo "<p>This will trigger session ID regeneration and test if the database gets updated.</p>";
    echo "<button type='submit' name='action' value='force_regeneration' class='btn-warning'>🔄 Force Regeneration</button>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h3>Step 3: Test Session Validation</h3>";
    echo "<p>This tests if the session can still be validated from the database after regeneration.</p>";
    echo "<button type='submit' name='action' value='test_validation' class='btn-primary'>✅ Test Validation</button>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h3>Step 4: Clean Up</h3>";
    echo "<p>Destroy the test session when done.</p>";
    echo "<button type='submit' name='action' value='destroy_session' class='btn-danger'>🗑️ Destroy Session</button>";
    echo "</div>";
}

echo "<button type='button' onclick='window.location.reload()' class='btn-primary' style='background: #6c757d;'>🔄 Refresh</button>";
echo "</form>";
echo "</div>";

// Test Results Interpretation
echo "<div class='box'>";
echo "<h2>📋 How to Interpret Results</h2>";

echo "<h3>✅ Expected Results (Fix Working):</h3>";
echo "<ul>";
echo "<li><strong>After Creating Session:</strong> Database session ID should match current session ID</li>";
echo "<li><strong>After Forced Regeneration:</strong> Database session ID should be updated to new session ID</li>";
echo "<li><strong>After Test Validation:</strong> Validation should pass - session found in database</li>";
echo "</ul>";

echo "<h3>❌ Problem Results (Fix Not Working):</h3>";
echo "<ul>";
echo "<li><strong>After Regeneration:</strong> Database still has old session ID, current session ID is different</li>";
echo "<li><strong>After Validation:</strong> Validation fails - no database session found</li>";
echo "<li><strong>User would be logged out</strong> at this point in normal operation</li>";
echo "</ul>";

echo "<h3>🔧 Configuration Info:</h3>";
echo "<ul>";
echo "<li><strong>Session Lifetime:</strong> " . formatTime(Config::SESSION_LIFETIME) . "</li>";
echo "<li><strong>Regeneration Interval:</strong> " . formatTime(Config::SESSION_REGENERATE_INTERVAL) . " (session ID changes this often)</li>";
echo "<li><strong>The Fix:</strong> When session ID regenerates, the database session record is updated with the new ID</li>";
echo "</ul>";
echo "</div>";

// Show the actual fix
echo "<div class='box'>";
echo "<h2>🔍 The Fix Applied</h2>";
echo "<p>The fix modifies the <code>regenerateId()</code> method in Session.php to update the database when the session ID changes:</p>";
echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
echo "public static function regenerateId() {\n";
echo "    \$oldSessionId = session_id();\n";
echo "    session_regenerate_id(true);\n";
echo "    \$newSessionId = session_id();\n";
echo "    \$_SESSION['last_regeneration'] = time();\n";
echo "    \n";
echo "    // NEW: Update database session ID if user is logged in\n";
echo "    if (self::has('user_id')) {\n";
echo "        \$db = Database::getInstance();\n";
echo "        \$db->query(\n";
echo "            \"UPDATE sessions SET id = ? WHERE id = ?\",\n";
echo "            [\$newSessionId, \$oldSessionId]\n";
echo "        );\n";
echo "    }\n";
echo "}";
echo "</pre>";
echo "<p><strong>What this does:</strong> When the session ID is regenerated for security (every 30 minutes), the database record is updated with the new session ID, preventing the \"session not found\" error that was causing early logout.</p>";
echo "</div>";

echo "</div>"; // container

echo "<hr>";
echo "<p><small>Generated at $nowFormatted</small></p>";

echo "</body></html>";

/**
 * Format seconds into human readable time
 */
function formatTime($seconds) {
    if ($seconds < 0) return "0s";
    if ($seconds < 60) return $seconds . "s";
    if ($seconds < 3600) {
        $mins = floor($seconds / 60);
        $secs = $seconds % 60;
        return $mins . "m" . ($secs > 0 ? " {$secs}s" : "");
    }
    
    $hours = floor($seconds / 3600);
    $mins = floor(($seconds % 3600) / 60);
    $result = $hours . "h";
    if ($mins > 0) $result .= " {$mins}m";
    return $result;
}
?>