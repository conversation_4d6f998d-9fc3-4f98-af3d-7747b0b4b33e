<!DOCTYPE html>
<html><head><title>Session Regeneration Fix Test</title><style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
.container { max-width: 900px; margin: 0 auto; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.success { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.info { color: blue; font-weight: bold; }
.box { margin: 15px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
.highlight { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; border-radius: 3px; }
button { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; color: white; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-danger { background: #dc3545; }
.btn-warning { background: #ffc107; color: black; }
.step { background: #f8f9fa; padding: 10px; margin: 10px 0; border-left: 4px solid #007bff; }
</style></head><body>
<div class='container'><h1>🔧 Session Regeneration Fix Test</h1><p>This test verifies that session ID regeneration no longer causes early logout.</p><div class='box'><h2>📊 Current Session Status</h2><p><strong>Current Time:</strong> 2025-06-09 17:18:57</p><p class='error'>❌ No active session</p></div><div class='box'><h2>🧪 Test the Fix</h2><form method='post'><div class='step'><h3>Step 1: Create Test Session</h3><p>First, create a session with database storage.</p><button type='submit' name='action' value='create_session' class='btn-success'>🔑 Create Test Session</button></div><button type='button' onclick='window.location.reload()' class='btn-primary' style='background: #6c757d;'>🔄 Refresh</button></form></div><div class='box'><h2>📋 How to Interpret Results</h2><h3>✅ Expected Results (Fix Working):</h3><ul><li><strong>After Creating Session:</strong> Database session ID should match current session ID</li><li><strong>After Forced Regeneration:</strong> Database session ID should be updated to new session ID</li><li><strong>After Test Validation:</strong> Validation should pass - session found in database</li></ul><h3>❌ Problem Results (Fix Not Working):</h3><ul><li><strong>After Regeneration:</strong> Database still has old session ID, current session ID is different</li><li><strong>After Validation:</strong> Validation fails - no database session found</li><li><strong>User would be logged out</strong> at this point in normal operation</li></ul><h3>🔧 Configuration Info:</h3><ul><li><strong>Session Lifetime:</strong> 2h</li><li><strong>Regeneration Interval:</strong> 30m (session ID changes this often)</li><li><strong>The Fix:</strong> When session ID regenerates, the database session record is updated with the new ID</li></ul></div><div class='box'><h2>🔍 The Fix Applied</h2><p>The fix modifies the <code>regenerateId()</code> method in Session.php to update the database when the session ID changes:</p><pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;'>public static function regenerateId() {
    $oldSessionId = session_id();
    session_regenerate_id(true);
    $newSessionId = session_id();
    $_SESSION['last_regeneration'] = time();
    
    // NEW: Update database session ID if user is logged in
    if (self::has('user_id')) {
        $db = Database::getInstance();
        $db->query(
            "UPDATE sessions SET id = ? WHERE id = ?",
            [$newSessionId, $oldSessionId]
        );
    }
}</pre><p><strong>What this does:</strong> When the session ID is regenerated for security (every 30 minutes), the database record is updated with the new session ID, preventing the "session not found" error that was causing early logout.</p></div></div><hr><p><small>Generated at 2025-06-09 17:18:57</small></p></body></html>