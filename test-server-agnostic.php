<?php
/**
 * Test Script for Server-Agnostic CMS Setup
 * Run this script to verify that the conversion was successful
 */

define('CMS_INIT', true);
require_once __DIR__ . '/src/includes/init.php';

echo "<!DOCTYPE html>\n<html><head><title>CMS Server-Agnostic Test</title></head><body>\n";
echo "<h1>CMS Server-Agnostic Setup Test</h1>\n";

$tests = [];

// Test 1: Check if new classes are loaded
$tests['Classes Loaded'] = class_exists('Router') && class_exists('Response') && class_exists('Config');

// Test 2: Check security file access
$tests['Security - Block .htaccess'] = !Security::isFileAccessAllowed('/.htaccess');
$tests['Security - Block .db files'] = !Security::isFileAccessAllowed('/database/cms.db');
$tests['Security - Block src directory'] = !Security::isFileAccessAllowed('/src/classes/Database.php');
$tests['Security - Allow public files'] = Security::isFileAccessAllowed('/index.php');

// Test 3: Check configuration
$tests['Config - Valid categories'] = in_array('tech', Config::VALID_CATEGORIES);
$tests['Config - MIME types'] = Config::getMimeType('css') === 'text/css';
$tests['Config - Cache times'] = Config::getCacheTime('css') > 0;

// Test 4: Check router initialization
Router::init();
$tests['Router - Initialized'] = true;

// Test 5: Check response class
Response::setCompression(true);
$tests['Response - Compression'] = true;

// Test 6: Check if security headers would be set
ob_start();
Security::setSecurityHeaders();
$headers = headers_list();
ob_end_clean();
$tests['Security Headers'] = !empty($headers);

// Test 7: Check file type detection
$tests['File Types - CSS'] = Config::getMimeType('css') === 'text/css';
$tests['File Types - JS'] = Config::getMimeType('js') === 'application/javascript';
$tests['File Types - PNG'] = Config::getMimeType('png') === 'image/png';

// Test 8: Check blocked extensions
$tests['Blocked Extensions - .htaccess'] = Config::isBlockedExtension('htaccess');
$tests['Blocked Extensions - .db'] = Config::isBlockedExtension('db');
$tests['Blocked Extensions - .log'] = Config::isBlockedExtension('log');

// Test 9: Check executable detection
$tests['Executable Detection - .php'] = Config::isExecutableExtension('php');
$tests['Executable Detection - .sh'] = Config::isExecutableExtension('sh');
$tests['Executable Detection - .css'] = !Config::isExecutableExtension('css');

// Test 10: Check compression detection
$tests['Compression - CSS'] = Config::isCompressible('text/css');
$tests['Compression - JS'] = Config::isCompressible('application/javascript');
$tests['Compression - PNG'] = !Config::isCompressible('image/png');

echo "<h2>Test Results</h2>\n";
echo "<table border='1' cellpadding='5' cellspacing='0'>\n";
echo "<tr><th>Test</th><th>Result</th><th>Status</th></tr>\n";

$passed = 0;
$total = count($tests);

foreach ($tests as $testName => $result) {
    $status = $result ? '✅ PASS' : '❌ FAIL';
    $color = $result ? 'green' : 'red';
    echo "<tr><td>$testName</td><td>" . ($result ? 'Success' : 'Failed') . "</td><td style='color: $color'>$status</td></tr>\n";
    if ($result) $passed++;
}

echo "</table>\n";

echo "<h2>Summary</h2>\n";
echo "<p><strong>$passed/$total tests passed</strong></p>\n";

if ($passed === $total) {
    echo "<p style='color: green; font-weight: bold;'>🎉 All tests passed! Your CMS is ready for server-agnostic deployment.</p>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>⚠️ Some tests failed. Please check the implementation.</p>\n";
}

echo "<h2>Next Steps</h2>\n";
echo "<ol>\n";
echo "<li>Configure your web server to route requests through <code>app.php</code></li>\n";
echo "<li>Test SEO URLs like <code>/tech/sample-post/</code></li>\n";
echo "<li>Test static file serving (CSS, JS, images)</li>\n";
echo "<li>Test security by trying to access <code>/src/</code> or <code>/.htaccess</code></li>\n";
echo "<li>Test error pages (404, 403)</li>\n";
echo "<li>Review and customize settings in <code>src/includes/config.php</code></li>\n";
echo "</ol>\n";

echo "<h2>Server Configuration</h2>\n";
echo "<p>Choose the appropriate configuration for your web server:</p>\n";
echo "<ul>\n";
echo "<li><strong>Apache:</strong> Use <code>server-configs/apache.conf</code> or update <code>.htaccess</code></li>\n";
echo "<li><strong>Nginx:</strong> Use <code>server-configs/nginx.conf</code></li>\n";
echo "<li><strong>Lighttpd:</strong> Use <code>server-configs/lighttpd.conf</code></li>\n";
echo "<li><strong>PHP Built-in:</strong> <code>php -S localhost:8000 -t public app.php</code></li>\n";
echo "</ul>\n";

echo "<h2>File Structure</h2>\n";
echo "<pre>\n";
echo "New files created:\n";
echo "├── public/app.php (main entry point)\n";
echo "├── public/403.php (access denied page)\n";
echo "├── src/classes/Router.php (URL routing)\n";
echo "├── src/classes/Response.php (HTTP responses)\n";
echo "├── src/includes/config.php (configuration)\n";
echo "├── server-configs/ (web server configurations)\n";
echo "│   ├── apache.conf\n";
echo "│   ├── nginx.conf\n";
echo "│   └── lighttpd.conf\n";
echo "└── SERVER-AGNOSTIC-SETUP.md (documentation)\n";
echo "</pre>\n";

echo "</body></html>\n";
?>
