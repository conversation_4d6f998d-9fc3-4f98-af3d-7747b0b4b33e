#!/usr/bin/env php
<?php
/**
 * Migration Script: Clean Up Old Test Posts
 *
 * This script removes test posts created before the THISISATEST naming convention
 * was implemented. It targets posts with old test naming patterns while preserving
 * legitimate content.
 *
 * Usage: php migrate-old-test-posts.php
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Ensure this is running from command line
if (php_sapi_name() !== 'cli') {
    echo "This script must be run from the command line.\n";
    exit(1);
}

echo "🔄 Old Test Posts Migration Script\n";
echo "==================================\n\n";

try {
    // Connect to database
    $dbPath = __DIR__ . '/database/cms.db';
    if (!file_exists($dbPath)) {
        throw new Exception('Database file not found: ' . $dbPath);
    }

    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "📊 Scanning for old test posts...\n";

    // Define old test patterns that were used before THISISATEST convention
    $oldTestPatterns = [
        "Test from Browser Interface",
        "API Test Post for Verification",
        "Test Post via API",
        "API Test Post for Cleanup",
        "Test Post for Cleanup%",
        "Image Download Test",
        "Gaming Post Test",
        "Film Review: Test Movie"
    ];

    // Build WHERE clause for old test patterns
    $whereConditions = [];
    $params = [];

    foreach ($oldTestPatterns as $pattern) {
        if (strpos($pattern, '%') !== false) {
            $whereConditions[] = "title LIKE ?";
            $params[] = $pattern;
        } else {
            $whereConditions[] = "title = ?";
            $params[] = $pattern;
        }
    }

    // Add condition to exclude posts that already follow new convention
    $whereConditions[] = "title NOT LIKE 'THISISATEST%'";

    $whereClause = "(" . implode(' OR ', array_slice($whereConditions, 0, -1)) . ") AND " . end($whereConditions);

    $selectQuery = "SELECT id, title, created_at FROM posts WHERE $whereClause ORDER BY id";

    $stmt = $pdo->prepare($selectQuery);
    $stmt->execute($params);
    $oldTestPosts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($oldTestPosts)) {
        echo "✅ No old test posts found. Database is already clean!\n";
        exit(0);
    }

    echo "🔍 Found " . count($oldTestPosts) . " old test posts to migrate/remove:\n\n";

    // Display found old test posts
    foreach ($oldTestPosts as $post) {
        echo "   📝 ID {$post['id']}: {$post['title']}\n";
        echo "      Created: {$post['created_at']}\n\n";
    }

    // Ask for confirmation
    echo "⚠️  MIGRATION REQUIRED:\n";
    echo "   These posts were created before the THISISATEST naming convention.\n";
    echo "   They need to be removed to ensure clean test data management.\n\n";
    echo "   Continue with deletion of these " . count($oldTestPosts) . " old test posts? (y/N): ";

    $handle = fopen("php://stdin", "r");
    $confirmation = trim(fgets($handle));
    fclose($handle);

    if (strtolower($confirmation) !== 'y' && strtolower($confirmation) !== 'yes') {
        echo "❌ Migration cancelled.\n";
        echo "💡 Note: These old test posts should be cleaned up manually or you can run this script again.\n";
        exit(0);
    }

    echo "\n🗑️  Migrating (deleting) old test posts...\n";

    // Delete old test posts
    $pdo->beginTransaction();
    $deletedCount = 0;
    $errors = [];

    foreach ($oldTestPosts as $post) {
        try {
            $deleteStmt = $pdo->prepare('DELETE FROM posts WHERE id = ?');
            if ($deleteStmt->execute([$post['id']])) {
                $deletedCount++;
                echo "   ✅ Deleted ID {$post['id']}: " . substr($post['title'], 0, 60) . "...\n";
            } else {
                $errors[] = "Failed to delete ID {$post['id']}";
                echo "   ❌ Failed to delete ID {$post['id']}\n";
            }
        } catch (Exception $e) {
            $errors[] = "Error deleting ID {$post['id']}: " . $e->getMessage();
            echo "   ❌ Error deleting ID {$post['id']}: " . $e->getMessage() . "\n";
        }
    }

    if (empty($errors)) {
        $pdo->commit();
        echo "\n✅ All deletions successful. Transaction committed.\n";
    } else {
        $pdo->rollback();
        echo "\n❌ Some deletions failed. Transaction rolled back.\n";
        echo "Errors encountered:\n";
        foreach ($errors as $error) {
            echo "   - $error\n";
        }
        exit(1);
    }

    // Log migration activity
    $logMessage = date('Y-m-d H:i:s') . " - Migration: Cleaned up $deletedCount old test posts before THISISATEST convention\n";
    $logPath = __DIR__ . '/logs/test-cleanup.log';

    // Create logs directory if it doesn't exist
    $logsDir = dirname($logPath);
    if (!is_dir($logsDir)) {
        mkdir($logsDir, 0755, true);
    }

    file_put_contents($logPath, $logMessage, FILE_APPEND | LOCK_EX);

    echo "\n🎉 Migration completed successfully!\n";
    echo "📊 Deleted $deletedCount old test posts.\n";
    echo "📝 Activity logged to: logs/test-cleanup.log\n\n";

    // Verify migration
    echo "🔍 Verifying migration results...\n";

    $verifyStmt = $pdo->prepare($selectQuery);
    $verifyStmt->execute($params);
    $remainingOldPosts = $verifyStmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($remainingOldPosts)) {
        echo "✅ Verification: All old test posts have been successfully removed.\n";
    } else {
        echo "⚠️  Warning: " . count($remainingOldPosts) . " old test posts still remain:\n";
        foreach ($remainingOldPosts as $post) {
            echo "   - ID {$post['id']}: {$post['title']}\n";
        }
    }

    // Check for any THISISATEST posts
    $newConventionStmt = $pdo->prepare("SELECT COUNT(*) FROM posts WHERE title LIKE 'THISISATEST%'");
    $newConventionStmt->execute();
    $newConventionCount = $newConventionStmt->fetchColumn();

    echo "\n📊 Current database status:\n";
    echo "   - Old test posts: " . count($remainingOldPosts) . "\n";
    echo "   - New convention (THISISATEST) posts: $newConventionCount\n";

    if (count($remainingOldPosts) == 0) {
        echo "\n🌟 Migration complete! Database is now ready for the new THISISATEST naming convention.\n";
        echo "\n📋 Next steps:\n";
        echo "   1. All future test posts should start with 'THISISATEST'\n";
        echo "   2. Use 'php cleanup-test-posts.php' for routine cleanup\n";
        echo "   3. Use '/test-cleanup.php' API endpoint for automated cleanup\n";
    }

} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    error_log("Old Test Posts Migration Error: " . $e->getMessage());
    exit(1);
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    error_log("Old Test Posts Migration Error: " . $e->getMessage());
    exit(1);
}

echo "\nMigration complete! 🚀\n";
?>
