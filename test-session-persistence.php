<?php
/**
 * Session Persistence Test
 * Focused test to identify why sessions are expiring early
 */

define('CMS_INIT', true);
require_once __DIR__ . '/src/includes/init.php';

// Clear any existing output
if (ob_get_level()) {
    ob_clean();
}

header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>\n<html><head><title>Session Persistence Test</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
.container { max-width: 1000px; margin: 0 auto; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.success { color: green; font-weight: bold; }
.info { color: blue; font-weight: bold; }
.box { margin: 15px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
.highlight { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; border-radius: 3px; }
.critical { background-color: #f8d7da; border-color: #f5c6cb; }
button { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
.btn-primary { background: #007bff; color: white; }
.btn-success { background: #28a745; color: white; }
.btn-danger { background: #dc3545; color: white; }
.btn-warning { background: #ffc107; color: black; }
.btn-secondary { background: #6c757d; color: white; }
</style></head><body>\n";

echo "<div class='container'>";
echo "<h1>🔍 Session Persistence Test</h1>";
echo "<p>This test will help identify exactly why your sessions are expiring early.</p>";

// Get current time
$now = time();
$nowFormatted = date('Y-m-d H:i:s', $now);

// Handle test actions
$action = $_POST['action'] ?? $_GET['action'] ?? '';
$message = '';

switch ($action) {
    case 'create_test_session':
        // Create a minimal test session without using database storage
        Session::set('test_user_id', 12345);
        Session::set('test_username', 'test_user');
        Session::set('test_created_at', $now);
        Session::set('test_last_ping', $now);
        $message = "<div class='highlight success'>✅ Test session created at $nowFormatted</div>";
        break;
        
    case 'create_full_session':
        // Create a full session with database storage
        Session::set('user_id', 12345);
        Session::set('username', 'test_user');
        Session::set('test_created_at', $now);
        Session::set('test_last_ping', $now);
        // Store in database
        try {
            Session::storeInDatabase(12345);
            $message = "<div class='highlight success'>✅ Full session with database storage created at $nowFormatted</div>";
        } catch (Exception $e) {
            $message = "<div class='highlight error'>❌ Error creating database session: " . $e->getMessage() . "</div>";
        }
        break;
        
    case 'ping_session':
        if (Session::has('test_user_id') || Session::has('user_id')) {
            Session::set('test_last_ping', $now);
            if (Session::has('user_id')) {
                Session::extend(); // Update database last_activity
            }
            $message = "<div class='highlight info'>🏓 Session pinged at $nowFormatted</div>";
        } else {
            $message = "<div class='highlight error'>❌ No session to ping!</div>";
        }
        break;
        
    case 'destroy_session':
        Session::destroy();
        $message = "<div class='highlight warning'>🗑️ Session destroyed at $nowFormatted</div>";
        break;
        
    case 'regenerate_id':
        if (Session::has('test_user_id') || Session::has('user_id')) {
            $oldId = session_id();
            Session::regenerateId();
            $newId = session_id();
            $message = "<div class='highlight info'>🔄 Session ID regenerated at $nowFormatted<br>Old: $oldId<br>New: $newId</div>";
        } else {
            $message = "<div class='highlight error'>❌ No session to regenerate!</div>";
        }
        break;
}

echo $message;

// Current Session Status
echo "<div class='box'>";
echo "<h2>📊 Current Session Status</h2>";
echo "<p><strong>Current Time:</strong> $nowFormatted</p>";

if (Session::has('test_user_id') || Session::has('user_id')) {
    $sessionId = session_id();
    $isTestSession = Session::has('test_user_id');
    $isFullSession = Session::has('user_id');
    
    $userId = $isFullSession ? Session::get('user_id') : Session::get('test_user_id');
    $username = Session::get('username', Session::get('test_username', 'Unknown'));
    $createdAt = Session::get('test_created_at', 'Unknown');
    $lastPing = Session::get('test_last_ping', 'Unknown');
    $lastRegen = Session::get('last_regeneration', 'Never');
    
    echo "<table>";
    echo "<tr><th>Property</th><th>Value</th></tr>";
    echo "<tr><td>Session Type</td><td>" . ($isFullSession ? "Full Session (with DB)" : "Test Session (memory only)") . "</td></tr>";
    echo "<tr><td>Session ID</td><td>" . substr($sessionId, 0, 20) . "...</td></tr>";
    echo "<tr><td>User ID</td><td>$userId</td></tr>";
    echo "<tr><td>Username</td><td>$username</td></tr>";
    
    if ($createdAt !== 'Unknown') {
        $age = $now - $createdAt;
        echo "<tr><td>Created At</td><td>" . date('Y-m-d H:i:s', $createdAt) . " (" . formatSeconds($age) . " ago)</td></tr>";
    }
    
    if ($lastPing !== 'Unknown') {
        $pingAge = $now - $lastPing;
        echo "<tr><td>Last Ping</td><td>" . date('Y-m-d H:i:s', $lastPing) . " (" . formatSeconds($pingAge) . " ago)</td></tr>";
    }
    
    if ($lastRegen !== 'Never') {
        $regenAge = $now - $lastRegen;
        echo "<tr><td>Last ID Regeneration</td><td>" . date('Y-m-d H:i:s', $lastRegen) . " (" . formatSeconds($regenAge) . " ago)</td></tr>";
        
        // Check if regeneration is due
        $nextRegen = $lastRegen + Config::SESSION_REGENERATE_INTERVAL;
        $timeToRegen = $nextRegen - $now;
        if ($timeToRegen <= 0) {
            echo "<tr><td style='background: #fff3cd;'>Next Regeneration</td><td style='background: #fff3cd;'>⚠️ DUE NOW (next session start will trigger)</td></tr>";
        } else {
            echo "<tr><td>Next Regeneration</td><td>In " . formatSeconds($timeToRegen) . "</td></tr>";
        }
    }
    
    echo "</table>";
    
    // Database session info for full sessions
    if ($isFullSession) {
        echo "<h3>Database Session Info</h3>";
        try {
            $db = Database::getInstance();
            $dbSession = $db->fetchOne(
                "SELECT *, 
                 datetime(created_at) as created_formatted,
                 datetime(last_activity) as last_activity_formatted,
                 (strftime('%s', 'now') - strftime('%s', last_activity)) as seconds_since_activity
                 FROM sessions WHERE id = ? AND user_id = ?",
                [$sessionId, $userId]
            );
            
            if ($dbSession) {
                echo "<table>";
                echo "<tr><th>Property</th><th>Value</th></tr>";
                echo "<tr><td>DB Created At</td><td>" . $dbSession['created_formatted'] . "</td></tr>";
                echo "<tr><td>DB Last Activity</td><td>" . $dbSession['last_activity_formatted'] . "</td></tr>";
                echo "<tr><td>Seconds Since Activity</td><td>" . $dbSession['seconds_since_activity'] . "</td></tr>";
                echo "<tr><td>IP Address</td><td>" . $dbSession['ip_address'] . "</td></tr>";
                echo "<tr><td>User Agent</td><td>" . substr($dbSession['user_agent'], 0, 50) . "...</td></tr>";
                echo "</table>";
                
                // Calculate remaining time
                $timeRemaining = Config::SESSION_LIFETIME - $dbSession['seconds_since_activity'];
                if ($timeRemaining <= 0) {
                    echo "<div class='highlight critical'>⚠️ SESSION EXPIRED: This session should be invalid according to database!</div>";
                } elseif ($timeRemaining <= 300) { // 5 minutes
                    echo "<div class='highlight warning'>⚠️ SESSION EXPIRING SOON: " . formatSeconds($timeRemaining) . " remaining</div>";
                } else {
                    echo "<div class='highlight success'>✅ Session valid for " . formatSeconds($timeRemaining) . "</div>";
                }
            } else {
                echo "<div class='highlight error'>❌ No database session found for this session ID!</div>";
            }
        } catch (Exception $e) {
            echo "<div class='highlight error'>❌ Database error: " . $e->getMessage() . "</div>";
        }
    }
} else {
    echo "<p class='error'>❌ No active session</p>";
}

echo "</div>";

// Session Configuration Summary
echo "<div class='box'>";
echo "<h2>⚙️ Session Configuration</h2>";
echo "<table>";
echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";
echo "<tr><td>App Session Lifetime</td><td>" . Config::SESSION_LIFETIME . "s (" . formatSeconds(Config::SESSION_LIFETIME) . ")</td><td>Target lifetime</td></tr>";
echo "<tr><td>PHP gc_maxlifetime</td><td>" . ini_get('session.gc_maxlifetime') . "s (" . formatSeconds(ini_get('session.gc_maxlifetime')) . ")</td><td>" . (ini_get('session.gc_maxlifetime') >= Config::SESSION_LIFETIME ? "✅ OK" : "❌ TOO SHORT") . "</td></tr>";
echo "<tr><td>PHP cookie_lifetime</td><td>" . ini_get('session.cookie_lifetime') . "s</td><td>" . (ini_get('session.cookie_lifetime') == Config::SESSION_LIFETIME ? "✅ Match" : "⚠️ Different") . "</td></tr>";
echo "<tr><td>Regenerate Interval</td><td>" . Config::SESSION_REGENERATE_INTERVAL . "s (" . formatSeconds(Config::SESSION_REGENERATE_INTERVAL) . ")</td><td>ID regeneration frequency</td></tr>";
echo "</table>";
echo "</div>";

// Test Actions
echo "<div class='box'>";
echo "<h2>🧪 Test Actions</h2>";
echo "<form method='post' style='display: inline-block; margin-right: 20px;'>";

if (!Session::has('test_user_id') && !Session::has('user_id')) {
    echo "<h3>Create Session:</h3>";
    echo "<button type='submit' name='action' value='create_test_session' class='btn-success'>🧪 Create Test Session (Memory Only)</button>";
    echo "<button type='submit' name='action' value='create_full_session' class='btn-primary'>💾 Create Full Session (With Database)</button>";
} else {
    echo "<h3>Session Actions:</h3>";
    echo "<button type='submit' name='action' value='ping_session' class='btn-primary'>🏓 Ping Session (Update Activity)</button>";
    echo "<button type='submit' name='action' value='regenerate_id' class='btn-warning'>🔄 Regenerate Session ID</button>";
    echo "<button type='submit' name='action' value='destroy_session' class='btn-danger'>🗑️ Destroy Session</button>";
}

echo "<button type='button' onclick='window.location.reload()' class='btn-secondary'>🔄 Refresh Page</button>";
echo "</form>";
echo "</div>";

// Test Instructions
echo "<div class='box'>";
echo "<h2>📋 Testing Protocol</h2>";
echo "<h3>To Test Session Persistence:</h3>";
echo "<ol>";
echo "<li><strong>Create a session</strong> (either type)</li>";
echo "<li><strong>Note the creation time</strong> displayed above</li>";
echo "<li><strong>Wait 15-20 minutes</strong> then refresh this page</li>";
echo "<li><strong>Check if session still exists:</strong>";
echo "<ul>";
echo "<li>If session is gone → Check which component failed</li>";
echo "<li>If session exists → Continue waiting until it fails</li>";
echo "</ul></li>";
echo "<li><strong>Use 'Ping Session'</strong> to simulate user activity</li>";
echo "</ol>";

echo "<h3>To Test Session Regeneration Issues:</h3>";
echo "<ol>";
echo "<li>Create a full session</li>";
echo "<li>Wait until 'Next Regeneration' shows 'DUE NOW'</li>";
echo "<li>Refresh the page (this will trigger regeneration)</li>";
echo "<li>Check if session survives the regeneration</li>";
echo "</ol>";
echo "</div>";

// Real-time Session Monitor
echo "<div class='box'>";
echo "<h2>📡 Real-time Monitor</h2>";
echo "<p>This section will auto-update every 30 seconds when enabled:</p>";
echo "<button id='monitorBtn' onclick='toggleMonitor()' class='btn-success'>▶️ Start Real-time Monitoring</button>";
echo "<div id='monitorStatus' style='margin: 10px 0; font-weight: bold;'>Monitor: Stopped</div>";

echo "<script>";
echo "let monitoring = false;";
echo "let monitorInterval;";
echo "";
echo "function toggleMonitor() {";
echo "  const btn = document.getElementById('monitorBtn');";
echo "  const status = document.getElementById('monitorStatus');";
echo "  ";
echo "  if (monitoring) {";
echo "    monitoring = false;";
echo "    clearInterval(monitorInterval);";
echo "    btn.textContent = '▶️ Start Real-time Monitoring';";
echo "    btn.className = 'btn-success';";
echo "    status.textContent = 'Monitor: Stopped';";
echo "    status.style.color = 'red';";
echo "  } else {";
echo "    monitoring = true;";
echo "    btn.textContent = '⏸️ Stop Real-time Monitoring';";
echo "    btn.className = 'btn-warning';";
echo "    status.textContent = 'Monitor: Active (refreshing every 30s)';";
echo "    status.style.color = 'green';";
echo "    ";
echo "    monitorInterval = setInterval(function() {";
echo "      if (monitoring) {";
echo "        window.location.reload();";
echo "      }";
echo "    }, 30000);";
echo "  }";
echo "}";
echo "</script>";
echo "</div>";

// Debugging Information
echo "<div class='box'>";
echo "<h2>🔧 Debug Information</h2>";
echo "<details>";
echo "<summary>Click to expand technical details</summary>";
echo "<h4>PHP Session Info:</h4>";
echo "<pre>";
echo "Session ID: " . session_id() . "\n";
echo "Session Status: " . session_status() . " (1=disabled, 2=none, 3=active)\n";
echo "Session Save Path: " . (session_save_path() ?: 'default') . "\n";
echo "Session Save Handler: " . ini_get('session.save_handler') . "\n";
echo "</pre>";

echo "<h4>All Session Data:</h4>";
echo "<pre>";
if (!empty($_SESSION)) {
    foreach ($_SESSION as $key => $value) {
        echo "$key = " . (is_scalar($value) ? $value : json_encode($value)) . "\n";
    }
} else {
    echo "No session data\n";
}
echo "</pre>";

echo "<h4>Cookie Information:</h4>";
echo "<pre>";
if (!empty($_COOKIE)) {
    foreach ($_COOKIE as $name => $value) {
        if (strpos($name, 'PHPSESSID') !== false || strpos($name, 'session') !== false) {
            echo "$name = " . substr($value, 0, 20) . "...\n";
        }
    }
} else {
    echo "No relevant cookies found\n";
}
echo "</pre>";
echo "</details>";
echo "</div>";

echo "<hr>";
echo "<p><small>Last updated: $nowFormatted | Session timeout configured for " . formatSeconds(Config::SESSION_LIFETIME) . "</small></p>";

echo "</div>"; // container
echo "</body></html>";

/**
 * Helper function to format seconds into human readable time
 */
function formatSeconds($seconds) {
    if ($seconds < 0) return "0s";
    if ($seconds < 60) return $seconds . "s";
    if ($seconds < 3600) {
        $mins = floor($seconds / 60);
        $secs = $seconds % 60;
        return $mins . "m" . ($secs > 0 ? " {$secs}s" : "");
    }
    
    $hours = floor($seconds / 3600);
    $mins = floor(($seconds % 3600) / 60);
    $secs = $seconds % 60;
    
    $result = $hours . "h";
    if ($mins > 0) $result .= " {$mins}m";
    if ($secs > 0) $result .= " {$secs}s";
    
    return $result;
}
?>