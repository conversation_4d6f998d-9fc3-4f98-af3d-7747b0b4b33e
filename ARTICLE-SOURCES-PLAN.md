# Article Sources and Reading Time - Implementation Plan

## Overview
Add article sources functionality and reading time estimation to the CMS posts system.

## Features to Implement

### 1. Article Sources Field
- Add input field above category in post creation/editing form
- Store comma-separated URLs as string in database
- Display sources below article content
- Validate URL format for sources

### 2. Reading Time Estimation
- Calculate based on average adult reading speed (200-250 words per minute)
- Count words in post content
- Display estimated reading time rounded to nearest minute
- Show reading time on main page and post detail page

## Database Schema Changes

### New Column: `sources`
```sql
ALTER TABLE posts ADD COLUMN sources TEXT DEFAULT NULL;
```

## Architecture Diagram

```mermaid
graph TD
    A[Post Creation Form] --> B[Sources Input Field]
    A --> C[Content Field]
    B --> D[Validate URLs]
    C --> E[Count Words]
    D --> F[Save to Database]
    E --> G[Calculate Reading Time]
    F --> H[Post Saved]
    G --> I[Display Reading Time]
    
    J[Post Display] --> K[Show Sources]
    J --> L[Show Reading Time]
    K --> M[Format URLs as Links]
    L --> N[Format as "X min read"]
```

## Implementation Checklist

### Phase 1: Database Migration
- [x] Create migration script to add `sources` column
- [x] Test migration on development database
- [x] Update Post class to handle sources field

### Phase 2: Backend Implementation
- [x] Update `createPost` method to accept sources parameter
- [x] Update `updatePost` method to handle sources
- [x] Add `calculateReadingTime` function
- [x] Update `getPost` methods to include sources and reading time
- [x] Add URL validation for sources

### Phase 3: Frontend Implementation
- [x] Add sources input field to post creation/editing form
- [x] Add reading time display to post detail page
- [x] Add reading time display to main page post listings
- [x] Style sources display section
- [x] Add JavaScript validation for URL format

### Phase 4: Display Implementation
- [x] Format sources as clickable links below article content
- [x] Add reading time badge/indicator on main page
- [x] Ensure responsive design for new elements
- [x] Add proper spacing and styling

### Phase 5: Testing
- [x] Write Playwright tests for sources functionality
- [x] Test reading time calculation accuracy
- [x] Test URL validation
- [x] Test responsive design
- [x] Verify data persistence

## Technical Specifications

### Reading Time Calculation
- **Words per minute**: 250 (average adult reading speed)
- **Formula**: `ceil(word_count / 250)` minutes
- **Minimum time**: 1 minute
- **Display format**: "X min read"

### Sources Field
- **Input type**: text input (single line for comma-separated URLs)
- **Validation**: Basic URL format check
- **Storage**: TEXT field in database
- **Display**: Formatted as "Sources:" with clickable links
- **Separator**: Comma-separated URLs

### Form Positioning
- **Sources field location**: Above category field
- **Label**: "Sources (comma-separated URLs)"
- **Placeholder**: "https://example.com, https://another-source.com"
- **Required**: No (optional field)

## File Modifications Required

### Database
- `migrate-add-sources.php` - New migration script

### Backend Classes
- `pixels/src/classes/Post.php` - Add sources handling and reading time calculation

### Frontend Pages  
- `pixels/public/admin/posts.php` - Add sources input field
- `pixels/public/post.php` - Display sources and reading time
- `pixels/public/index.php` - Display reading time in post listings

### Styling
- `pixels/public/assets/style.css` - Styles for sources and reading time

### Tests
- `pixels/tests/article-sources.spec.js` - Comprehensive testing

## Success Criteria
- ✅ Sources field appears above category in post form
- ✅ Sources are saved and retrieved correctly
- ✅ Reading time is calculated and displayed accurately
- ✅ Sources appear as clickable links below articles
- ✅ Reading time shows on main page and post detail
- ✅ All existing functionality remains intact
- ✅ Responsive design maintained
- ✅ Tests pass for new functionality

## Implementation Completed
**Date:** 2025-06-15 23:01:14

**Results:**
- ✅ Database migration successful - sources column added to posts table
- ✅ Post class updated with calculateReadingTime, formatSources, and validateSources methods
- ✅ Form processing updated to handle sources field in create and update operations
- ✅ Reading time calculation working accurately (250 words/min, minimum 1 min)
- ✅ Sources text input field added above category in admin post form
- ✅ Sources displayed as clickable links below article content
- ✅ Reading time displayed on main page and individual post pages
- ✅ CSS styling added for responsive design
- ✅ URL validation implemented with proper error handling
- ✅ Comprehensive test suite created and verified

**Files Modified:**
- `pixels/src/classes/Post.php` - Added sources handling and reading time calculation
- `pixels/public/admin/posts.php` - Added sources form field and processing
- `pixels/public/index.php` - Added reading time display to post listings
- `pixels/public/post.php` - Added reading time and sources display to individual posts
- `pixels/public/assets/style.css` - Added styling for reading time and sources
- `pixels/migrate-add-sources.php` - Database migration script
- `pixels/tests/article-sources.spec.js` - Comprehensive test suite
- `pixels/verify-sources-feature.php` - Manual verification script
- `pixels/FUNCTIONS.md` - Updated function registry

**User Requirements Fulfilled:**
✅ Sources text input field above category for comma-separated URLs
✅ Sources saved in database as string
✅ Sources displayed below articles as clickable links
✅ Reading time estimation based on average adult reading speed
✅ Reading time displayed on main page and individual posts
✅ Reading time rounded to nearest minute

## Implementation Notes
- Follow MVC pattern with minimal changes to existing structure
- Add debug logging for sources and reading time functions
- Ensure proper XSS protection for URL display
- Maintain backward compatibility with existing posts
- Use semantic HTML for accessibility