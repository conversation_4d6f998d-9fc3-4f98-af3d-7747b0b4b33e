# Lighttpd Configuration for Minimal CMS
# Server-agnostic setup using app.php as entry point

server.modules = (
    "mod_access",
    "mod_alias",
    "mod_compress",
    "mod_redirect",
    "mod_rewrite",
    "mod_setenv",
    "mod_fastcgi"
)

server.document-root = "/path/to/your/cms/public"
server.errorlog = "/var/log/lighttpd/error.log"
server.pid-file = "/var/run/lighttpd.pid"
server.username = "www-data"
server.groupname = "www-data"
server.port = 80

# MIME types
mimetype.assign = (
    ".html" => "text/html",
    ".txt" => "text/plain",
    ".jpg" => "image/jpeg",
    ".png" => "image/png",
    ".gif" => "image/gif",
    ".svg" => "image/svg+xml",
    ".css" => "text/css",
    ".js" => "application/javascript",
    ".json" => "application/json",
    ".pdf" => "application/pdf",
    ".woff" => "font/woff",
    ".woff2" => "font/woff2"
)

# Index files
index-file.names = ( "app.php", "index.php" )

# Security headers (also handled by PHP)
setenv.add-response-header = (
    "X-Frame-Options" => "DENY",
    "X-Content-Type-Options" => "nosniff",
    "X-XSS-Protection" => "1; mode=block",
    "Referrer-Policy" => "strict-origin-when-cross-origin"
)

# Hide server information
server.tag = ""

# Block access to sensitive files and directories
$HTTP["url"] =~ "\.(htaccess|htpasswd|ini|log|sh|sql|conf|bak|backup|old|orig|save|swp|tmp|db)$" {
    url.access-deny = ( "" )
}

$HTTP["url"] =~ "^/(src|database|logs)/" {
    url.access-deny = ( "" )
}

# Compression
compress.cache-dir = "/var/cache/lighttpd/compress/"
compress.filetype = (
    "application/javascript",
    "text/css",
    "text/html",
    "text/plain",
    "text/xml",
    "application/xml",
    "image/svg+xml"
)

# Cache static files
$HTTP["url"] =~ "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$" {
    expire.url = ( "" => "access plus 30 days" )
}

# PHP FastCGI
fastcgi.server = ( ".php" =>
    ((
        "bin-path" => "/usr/bin/php-cgi",
        "socket" => "/tmp/php.socket",
        "max-procs" => 2,
        "idle-timeout" => 20,
        "bin-environment" => (
            "PHP_FCGI_CHILDREN" => "4",
            "PHP_FCGI_MAX_REQUESTS" => "10000"
        ),
        "bin-copy-environment" => (
            "PATH", "SHELL", "USER"
        ),
        "broken-scriptfilename" => "enable"
    ))
)

# URL rewriting - route all requests through app.php
url.rewrite-if-not-file = (
    "^/(.*)$" => "/app.php"
)

# Optional: Force HTTPS redirect
# $HTTP["scheme"] == "http" {
#     url.redirect = ( "^/(.*)" => "https://your-domain.com/$1" )
# }

# Error pages
server.error-handler-404 = "/404.php"
