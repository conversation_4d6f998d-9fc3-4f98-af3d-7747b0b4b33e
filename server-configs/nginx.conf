# Nginx Configuration for Minimal CMS
# Server-agnostic setup using app.php as entry point

server {
    listen 80;
    server_name your-domain.com;
    root /path/to/your/cms/public;
    index app.php index.php;

    # Security headers (handled by <PERSON><PERSON>, but can be set here too)
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Hide server information
    server_tokens off;

    # Block access to sensitive files and directories
    location ~ /\.(htaccess|htpasswd|ini|log|sh|sql|conf)$ {
        deny all;
    }

    location ~ /\.(bak|backup|old|orig|save|swp|tmp)$ {
        deny all;
    }

    location ~ ^/(src|database|logs)/ {
        deny all;
    }

    location ~ \.db$ {
        deny all;
    }

    # Static files with caching
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 30d;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # Enable gzip compression
        gzip on;
        gzip_vary on;
        gzip_types text/css application/javascript image/svg+xml;
    }

    # PHP files - route through app.php for server-agnostic handling
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock; # Adjust PHP version as needed
        fastcgi_index app.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Route all other requests through app.php
    location / {
        try_files $uri $uri/ /app.php?$query_string;
    }

    # Optional: Force HTTPS redirect
    # return 301 https://$server_name$request_uri;
}

# HTTPS configuration (optional)
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com;
#     root /path/to/your/cms/public;
#     index app.php index.php;
#
#     ssl_certificate /path/to/certificate.crt;
#     ssl_certificate_key /path/to/private.key;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#
#     # Add HSTS header
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#
#     # Include the same location blocks as above
# }
