# Apache Configuration for Minimal CMS
# Server-agnostic setup using app.php as entry point
# This replaces the .htaccess file functionality

<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/your/cms/public
    DirectoryIndex app.php index.php

    # Security headers (also handled by <PERSON><PERSON>)
    Header always set X-Frame-Options "DENY"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header unset Server
    Header unset X-Powered-By

    # Hide server signature
    ServerSignature Off

    # Block access to sensitive files
    <FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf)$">
        Require all denied
    </FilesMatch>

    <FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
        Require all denied
    </FilesMatch>

    <Files "*.db">
        Require all denied
    </Files>

    # Block access to sensitive directories
    <DirectoryMatch "/(src|database|logs)/">
        Require all denied
    </DirectoryMatch>

    # Disable directory browsing
    Options -Indexes

    # Enable rewrite engine
    RewriteEngine On

    # Route all requests through app.php for server-agnostic handling
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ app.php [QSA,L]

    # Cache static assets
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/javascript "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
        ExpiresByType image/gif "access plus 1 month"
        ExpiresByType image/svg+xml "access plus 1 month"
        ExpiresByType font/woff "access plus 1 month"
        ExpiresByType font/woff2 "access plus 1 month"
    </IfModule>

    # Compress files
    <IfModule mod_deflate.c>
        AddOutputFilterByType DEFLATE text/plain
        AddOutputFilterByType DEFLATE text/html
        AddOutputFilterByType DEFLATE text/xml
        AddOutputFilterByType DEFLATE text/css
        AddOutputFilterByType DEFLATE application/xml
        AddOutputFilterByType DEFLATE application/xhtml+xml
        AddOutputFilterByType DEFLATE application/rss+xml
        AddOutputFilterByType DEFLATE application/javascript
        AddOutputFilterByType DEFLATE application/x-javascript
        AddOutputFilterByType DEFLATE image/svg+xml
    </IfModule>

    # Optional: Force HTTPS redirect
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Error pages
    ErrorDocument 404 /404.php
    ErrorDocument 403 /403.php
    ErrorDocument 500 /500.php
</VirtualHost>

# HTTPS configuration (optional)
# <VirtualHost *:443>
#     ServerName your-domain.com
#     DocumentRoot /path/to/your/cms/public
#     DirectoryIndex app.php index.php
#
#     SSLEngine on
#     SSLCertificateFile /path/to/certificate.crt
#     SSLCertificateKeyFile /path/to/private.key
#     SSLProtocol all -SSLv2 -SSLv3 -TLSv1 -TLSv1.1
#     SSLCipherSuite ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384
#     SSLHonorCipherOrder off
#
#     # Add HSTS header
#     Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
#
#     # Include the same directives as above
# }
