# Testing Guide

This guide explains the standardized testing practices for the CMS project, including test post naming conventions and cleanup procedures.

## Test Post Naming Convention

**All test posts MUST start with `THISISATEST` followed by a meaningful description.**

### Format
```
THISISATEST [Meaningful Description of Test]
```

### Examples
- `THISISATEST API Post Creation Functionality`
- `THISISATEST Cleanup Verification Post 1`
- `THISISATEST Browser Interface Test`
- `THISISATEST Invalid Token Rejection`
- `THISISATEST Technology Breakthrough Sample`

### Why This Convention?

1. **Easy Identification**: The `THISISATEST` prefix makes test posts immediately recognizable
2. **Reliable Cleanup**: Automated cleanup scripts can safely target these posts without affecting real content
3. **No False Positives**: This unique prefix won't accidentally match production content
4. **Descriptive**: The meaningful description helps understand what each test is validating

## Test Cleanup

### Automatic Cleanup

Test cleanup happens automatically in two ways:

1. **After Each Test**: Individual tests clean up their own posts using the cleanup script
2. **Pattern Matching**: The cleanup script targets all posts starting with `THISISATEST`

### Manual Cleanup Options

#### Option 1: Command Line Script
```bash
php cleanup-test-posts.php
```

This interactive script will:
- Scan for all test posts
- Show you what will be deleted
- Ask for confirmation
- Delete the posts and log the activity

#### Option 2: Web API Endpoint
```bash
curl -X POST -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     http://localhost:8000/test-cleanup.php
```

#### Option 3: Direct SQL (Advanced)
```sql
DELETE FROM posts WHERE title LIKE 'THISISATEST%';
```

## Creating Test Posts

### In Playwright Tests

```javascript
const postData = {
  title: "THISISATEST Your Test Description Here",
  content: "Test content...",
  description: "Test description...",
  thumbnail_url: "https://example.com/image.jpg"
};

const response = await request.post("/api/posts.php", {
  headers: {
    Authorization: `Bearer ${adminToken}`,
    "Content-Type": "application/json",
  },
  data: postData,
});
```

### In Test HTML Interface

When using `test.html`, the sample data already follows the convention:
- Tech sample: `THISISATEST Technology Breakthrough Sample`
- Blog sample: `THISISATEST Daily Thoughts Sample`
- News sample: `THISISATEST Breaking API Integration Sample`

### In Manual Testing

Always prefix your test posts with `THISISATEST` when creating posts manually for testing purposes.

## Test File Structure

### API Tests
- Location: `tests/api-functionality.spec.js`
- Purpose: Test REST API endpoints
- Cleanup: Automatic after each test

### Cleanup Tests
- Location: `tests/test-cleanup.spec.js`
- Purpose: Verify cleanup functionality
- Cleanup: Built into the test logic

### Manual Test Interface
- Location: `public/test.html`
- Purpose: Interactive API testing
- Cleanup: Manual or via cleanup scripts

## Best Practices

### DO ✅
- Always use `THISISATEST` prefix for test posts
- Include meaningful descriptions after the prefix
- Clean up test posts after testing sessions
- Use the provided cleanup scripts
- Verify cleanup worked by checking post counts

### DON'T ❌
- Create test posts without the `THISISATEST` prefix
- Leave test posts in the database after testing
- Use vague or generic test descriptions
- Mix test posts with production content
- Manually delete posts without using cleanup scripts

## Troubleshooting

### Test Posts Not Being Cleaned Up

1. Check if posts actually start with `THISISATEST`
2. Verify cleanup script permissions
3. Check database connection
4. Review cleanup logs in `logs/test-cleanup.log`

### Cleanup Script Errors

1. Ensure database file exists and is readable
2. Check PHP error logs
3. Verify script has execution permissions
4. Run with verbose error reporting enabled

### False Positive Deletions

The `THISISATEST` prefix is specifically chosen to avoid false positives. If you have legitimate content that starts with this prefix, rename it immediately.

## Logging

All cleanup activities are logged to `logs/test-cleanup.log` with timestamps and details about what was deleted.

Example log entry:
```
2025-06-15 19:37:21 - CLI Cleanup: Deleted 5 test posts
2025-06-15 19:37:22 - Test Cleanup: Deleted 3 test posts
```

## Integration with CI/CD

When setting up continuous integration, consider adding a post-test cleanup step:

```bash
# After running tests
php cleanup-test-posts.php < /dev/null || true
```

The `< /dev/null` provides empty input to skip confirmation prompts in automated environments.

## Monitoring

Regularly monitor test post accumulation:

```sql
SELECT COUNT(*) as test_post_count 
FROM posts 
WHERE title LIKE 'THISISATEST%';
```

If this number grows unexpectedly, investigate which tests aren't cleaning up properly.