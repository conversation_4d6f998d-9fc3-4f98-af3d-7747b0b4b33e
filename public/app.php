<?php
/**
 * Server-Agnostic Application Entry Point
 * This file replaces the need for .htaccess URL rewriting
 * Use this as the main entry point for all requests
 */

// Check if this is an admin route that requires authentication BEFORE any headers are sent
$uri = $_SERVER['REQUEST_URI'];
$path = parse_url($uri, PHP_URL_PATH);
$isAdminRoute = strpos($path, '/admin/') !== false;

// For admin routes (except login), handle authentication before any initialization
if ($isAdminRoute && basename($path) !== 'login.php' && !strpos($path, 'login.php')) {
    // Minimal session start for auth check
    session_start();

    // Simple authentication check without full initialization
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['authenticated']) || !$_SESSION['authenticated']) {
        // Clean redirect without any headers interference
        http_response_code(302);
        header('Location: login.php');
        exit;
    }
}

define('CMS_INIT', true);
require_once __DIR__ . '/../src/includes/init.php';

// Start output buffering with compression
Response::startOutput();

// Initialize and run the router
Router::init();
Router::route();

// Send the response
Response::send();
?>
