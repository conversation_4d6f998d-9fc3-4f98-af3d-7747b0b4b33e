<?php
/**
 * Test Cleanup Script
 * Removes test posts created during API testing to prevent database pollution
 */

// Set error reporting
error_reporting(E_ALL);
ini_set("display_errors", 1);

// Check if running from command line or web
$isCommandLine = php_sapi_name() === "cli";

if (!$isCommandLine) {
    // Set JSON response header for web requests
    header("Content-Type: application/json");

    // Simple authentication check for web access
    $authHeader = $_SERVER["HTTP_AUTHORIZATION"] ?? "";
    if (!$authHeader || !str_contains($authHeader, "Bearer")) {
        http_response_code(401);
        echo json_encode(["error" => "Authentication required"]);
        exit();
    }
}

try {
    // Connect to database
    $dbPath = dirname(__DIR__) . "/database/cms.db";
    if (!file_exists($dbPath)) {
        throw new Exception("Database file not found: " . $dbPath);
    }

    $pdo = new PDO("sqlite:" . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Define patterns that identify test posts - ONLY THISISATEST prefix for safety
    $testPatterns = ["THISISATEST%"];

    $deletedCount = 0;
    $deletedPosts = [];

    // Enhanced safety check: Verify pattern is safe
    foreach ($testPatterns as $pattern) {
        if (!str_starts_with($pattern, "THISISATEST")) {
            throw new Exception(
                "SAFETY ERROR: Only THISISATEST patterns allowed. Unsafe pattern detected: $pattern"
            );
        }
    }

    // Get posts that match test patterns with additional safety constraints
    $whereClause = implode(
        " OR ",
        array_fill(0, count($testPatterns), "title LIKE ?")
    );
    // Extra safety: ensure we only target posts that definitely start with THISISATEST
    $selectQuery = "SELECT id, title, created_at FROM posts WHERE ($whereClause) AND title LIKE 'THISISATEST%' ORDER BY id";

    $stmt = $pdo->prepare($selectQuery);
    $stmt->execute($testPatterns);
    $testPosts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($testPosts)) {
        $message = "No test posts found to clean up.";
        if ($isCommandLine) {
            echo $message . "\n";
        } else {
            echo json_encode(["message" => $message, "deleted_count" => 0]);
        }
        exit();
    }

    // Delete test posts with enhanced safety checks
    $pdo->beginTransaction();

    foreach ($testPosts as $post) {
        // Double safety check: verify post title starts with THISISATEST
        if (!str_starts_with($post["title"], "THISISATEST")) {
            $pdo->rollback();
            throw new Exception(
                "SAFETY ERROR: Attempted to delete non-test post: ID {$post["id"]} - {$post["title"]}"
            );
        }

        $deleteStmt = $pdo->prepare(
            "DELETE FROM posts WHERE id = ? AND title LIKE 'THISISATEST%'"
        );
        if ($deleteStmt->execute([$post["id"]])) {
            $deletedCount++;
            $deletedPosts[] = [
                "id" => $post["id"],
                "title" => $post["title"],
                "created_at" => $post["created_at"],
            ];

            if ($isCommandLine) {
                echo "✓ Deleted post ID {$post["id"]}: {$post["title"]}\n";
            }
        } else {
            if ($isCommandLine) {
                echo "⚠ Failed to delete post ID {$post["id"]}: {$post["title"]}\n";
            }
        }
    }

    $pdo->commit();

    // Enhanced logging with detailed information
    $logMessage =
        date("Y-m-d H:i:s") .
        " - Test Cleanup: Deleted $deletedCount test posts";
    if ($deletedCount > 0) {
        $logMessage .= " - Posts: ";
        $postSummaries = array_map(function ($post) {
            return "ID{$post["id"]}({$post["title"]})";
        }, $deletedPosts);
        $logMessage .= implode(", ", $postSummaries);
    }
    $logMessage .= "\n";

    $logPath = dirname(__DIR__) . "/logs/test-cleanup.log";

    // Create logs directory if it doesn't exist
    $logsDir = dirname($logPath);
    if (!is_dir($logsDir)) {
        mkdir($logsDir, 0755, true);
    }

    file_put_contents($logPath, $logMessage, FILE_APPEND | LOCK_EX);

    // Response
    $response = [
        "success" => true,
        "message" => "Successfully cleaned up $deletedCount test posts",
        "deleted_count" => $deletedCount,
        "deleted_posts" => $deletedPosts,
    ];

    if ($isCommandLine) {
        echo "\n🎉 Cleanup completed successfully!\n";
        echo "Deleted $deletedCount test posts.\n";

        if ($deletedCount > 0) {
            echo "\nDeleted posts:\n";
            foreach ($deletedPosts as $post) {
                echo "- ID {$post["id"]}: {$post["title"]}\n";
            }
        }
    } else {
        echo json_encode($response);
    }
} catch (PDOException $e) {
    $errorMessage = "Database error: " . $e->getMessage();
    error_log("Test Cleanup Error: " . $errorMessage);

    if ($isCommandLine) {
        echo "❌ Database error: " . $e->getMessage() . "\n";
        exit(1);
    } else {
        http_response_code(500);
        echo json_encode(["error" => $errorMessage]);
    }
} catch (Exception $e) {
    $errorMessage = "Cleanup error: " . $e->getMessage();
    error_log("Test Cleanup Error: " . $errorMessage);

    if ($isCommandLine) {
        echo "❌ Error: " . $e->getMessage() . "\n";
        exit(1);
    } else {
        http_response_code(500);
        echo json_encode(["error" => $errorMessage]);
    }
}
?>
