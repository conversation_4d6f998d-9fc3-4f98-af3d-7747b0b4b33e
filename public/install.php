<?php
/**
 * CMS Installation Script
 * Creates database schema and default admin user
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Prevent direct access if already installed
if (file_exists('../database/cms.db') && filesize('../database/cms.db') > 0) {
    die('CMS is already installed. Delete database/cms.db to reinstall.');
}

// Create necessary directories
$directories = ['../database', '../logs', 'assets'];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

try {
    // Create SQLite database
    $pdo = new PDO('sqlite:../database/cms.db');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create users table
    $pdo->exec("
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            email VARCHAR(100),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME,
            failed_login_attempts INTEGER DEFAULT 0,
            locked_until DATETIME NULL
        )
    ");
    
    // Create posts table
    $pdo->exec("
        CREATE TABLE posts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            is_draft BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            author_id INTEGER NOT NULL,
            category VARCHAR(50) DEFAULT 'tech',
            slug VARCHAR(255),
            thumbnail_url TEXT DEFAULT NULL,
            FOREIGN KEY (author_id) REFERENCES users(id)
        )
    ");
    
    // Create sessions table
    $pdo->exec("
        CREATE TABLE sessions (
            id VARCHAR(128) PRIMARY KEY,
            user_id INTEGER NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
            ip_address VARCHAR(45),
            user_agent TEXT,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
    ");
    
    // Create login attempts table for rate limiting
    $pdo->exec("
        CREATE TABLE login_attempts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ip_address VARCHAR(45) NOT NULL,
            username VARCHAR(50),
            attempted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            success BOOLEAN DEFAULT 0
        )
    ");
    
    // Create default admin user
    $adminPassword = password_hash('admin', PASSWORD_ARGON2ID);
    $stmt = $pdo->prepare("
        INSERT INTO users (username, password_hash, email) 
        VALUES (?, ?, ?)
    ");
    $stmt->execute(['admin', $adminPassword, 'admin@localhost']);
    
    echo "<!DOCTYPE html>
<html>
<head>
    <title>CMS Installation Complete</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin-top: 20px; }
    </style>
</head>
<body>
    <h1>CMS Installation Complete!</h1>
    <div class='success'>
        <h3>✓ Installation Successful</h3>
        <p>Your CMS has been successfully installed and configured.</p>
    </div>
    
    <div class='info'>
        <h3>Default Admin Credentials</h3>
        <p><strong>Username:</strong> admin</p>
        <p><strong>Password:</strong> admin</p>
        <p><strong>Admin URL:</strong> <a href='admin/'>admin/</a></p>
        <p><strong>Public Site:</strong> <a href='index.php'>index.php</a></p>
    </div>
    
    <div class='info'>
        <h3>Security Notice</h3>
        <p>⚠️ Please change the default admin password immediately after logging in!</p>
        <p>⚠️ Delete this install.php file for security.</p>
    </div>
</body>
</html>";

} catch (PDOException $e) {
    die("Installation failed: " . $e->getMessage());
}
?>
