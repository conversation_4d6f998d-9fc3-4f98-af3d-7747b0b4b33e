<?php
/**
 * Public Interface - Main Page
 * Displays published blog posts with pagination
 */

if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once "../src/includes/init.php";
}

// Get current page
$currentPage = getCurrentPage();
$postsPerPage = 8;

// Get search query and category filter if provided
$searchQuery = isset($_GET["search"]) ? trim($_GET["search"]) : "";
$categoryFilter = isset($_GET["category"]) ? trim($_GET["category"]) : "";

try {
    if ($searchQuery) {
        // Search posts
        $result = $postManager->searchPosts(
            $searchQuery,
            true,
            $currentPage,
            $postsPerPage
        );
        $pageTitle =
            'Search Results for "' . Security::escape($searchQuery) . '"';
    } elseif ($categoryFilter) {
        // Filter posts by category
        $result = $postManager->getPostsByCategory(
            $categoryFilter,
            $currentPage,
            $postsPerPage,
            true
        );
        $pageTitle = ucfirst($categoryFilter) . " Posts";
    } else {
        // Get published posts
        $result = $postManager->getAllPosts($currentPage, $postsPerPage, true);
        $pageTitle = "Blog Posts";
    }

    $posts = $result["posts"];
    $totalPages = $result["totalPages"];
    $total = $result["total"];
} catch (Exception $e) {
    error_log("Error loading posts: " . $e->getMessage());
    $posts = [];
    $totalPages = 0;
    $total = 0;
    addFlashMessage("Error loading posts. Please try again later.", "error");
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo Security::escape($pageTitle); ?> - Minimal CMS</title>
    <link rel="stylesheet" href="/assets/style.css">
</head>
<body>
    <header class="site-header">
        <div class="container">
            <h1 class="site-title">
                <a href="/index.php">Minimal CMS</a>
            </h1>

            <nav class="main-nav">
                <ul>
                    <li><a href="/index.php">Home</a></li>
                    <li><a href="/admin/">Admin</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <?php echo displayFlashMessages(); ?>

            <!-- Breadcrumb Navigation -->
            <?php if ($categoryFilter): ?>
                <nav class="breadcrumb">
                    <a href="/index.php">Home</a> &raquo;
                    <span><?php echo Security::escape(
                        ucfirst($categoryFilter)
                    ); ?></span>
                </nav>
            <?php endif; ?>

            <!-- Search Form -->
            <div class="search-form">
                <form method="GET" action="index.php">
                    <input type="text"
                           name="search"
                           placeholder="Search posts..."
                           value="<?php echo Security::escape($searchQuery); ?>"
                           class="search-input">
                    <button type="submit" class="search-button">Search</button>
                    <?php if ($searchQuery): ?>
                        <a href="/index.php" class="clear-search">Clear</a>
                    <?php endif; ?>
                </form>
            </div>

            <div class="content-header">
                <h2><?php echo Security::escape($pageTitle); ?></h2>
                <?php if ($total > 0): ?>
                    <p class="post-count">
                        <?php if ($searchQuery): ?>
                            Found <?php echo $total; ?> post<?php echo $total !==
 1
     ? "s"
     : ""; ?>
                        <?php elseif ($categoryFilter): ?>
                            <?php echo $total; ?> <?php echo Security::escape(
     $categoryFilter
 ); ?> post<?php echo $total !== 1 ? "s" : ""; ?>
                        <?php else: ?>
                            <?php echo $total; ?> post<?php echo $total !== 1
     ? "s"
     : ""; ?> published
                        <?php endif; ?>
                    </p>
                <?php endif; ?>
                <?php if ($categoryFilter): ?>
                    <p class="category-filter-info">
                        <a href="/index.php" class="clear-filter">← View all posts</a>
                    </p>
                <?php endif; ?>
            </div>

            <div class="posts-container">
                <?php if (empty($posts)): ?>
                    <div class="no-posts">
                        <?php if ($searchQuery): ?>
                            <p>No posts found matching your search criteria.</p>
                            <p><a href="/index.php">View all posts</a></p>
                        <?php elseif ($categoryFilter): ?>
                            <p>No posts found in the <?php echo Security::escape(
                                $categoryFilter
                            ); ?> category.</p>
                            <p><a href="/index.php">View all posts</a></p>
                        <?php else: ?>
                            <p>No posts have been published yet.</p>
                        <?php endif; ?>
                    </div>
                <?php
                    // Use description if available, otherwise generate excerpt from content
                    else: ?>
                    <?php foreach ($posts as $post): ?>
                        <article class="post-preview">
                            <?php if (!empty($post["thumbnail_url"])): ?>
                                <div class="post-thumbnail">
                                    <img src="<?php echo Security::escape(
                                        $post["thumbnail_url"]
                                    ); ?>"
                                         alt="<?php echo Security::escape(
                                             $post["title"]
                                         ); ?>"
                                         class="thumbnail-image">
                                </div>
                            <?php endif; ?>

                            <div class="post-details">
                                <header class="post-header">
                                    <h3 class="post-title">
                                        <?php echo Security::escape(
                                            $post["title"]
                                        ); ?>
                                    </h3>
                                    <div class="post-meta">
                                        <span class="post-category">
                                            <a href="/index.php?category=<?php echo Security::escape(
                                                $post["category"] ?? "tech"
                                            ); ?>"
                                               class="category-badge category-<?php echo Security::escape(
                                                   $post["category"] ?? "tech"
                                               ); ?>">
                                                <?php echo Security::escape(
                                                    ucfirst(
                                                        $post["category"] ??
                                                            "tech"
                                                    )
                                                ); ?>
                                            </a>
                                        </span>
                                        <span class="post-author">
                                            By <?php echo Security::escape(
                                                $post["author_name"]
                                            ); ?>
                                        </span>
                                        <span class="post-date">
                                            <?php echo formatDate(
                                                $post["created_at"]
                                            ); ?>
                                        </span>
                                        <span class="post-reading-time">
                                            <?php echo $postManager->calculateReadingTime(
                                                $post["content"]
                                            ); ?> min read
                                        </span>
                                        <?php if (
                                            $post["updated_at"] !==
                                            $post["created_at"]
                                        ): ?>
                                            <span class="post-updated">
                                                (Updated <?php echo timeAgo(
                                                    $post["updated_at"]
                                                ); ?>)
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </header>

                                <div class="post-content">
                                    <?php if (!empty($post["description"])) {
                                        echo parseMarkdown(
                                            $post["description"]
                                        );
                                    } else {
                                        $excerpt = generateExcerpt(
                                            $post["content"],
                                            300
                                        );
                                        if (containsHtml($post["content"])) {
                                            echo $excerpt;
                                        } else {
                                            echo parseMarkdown($excerpt);
                                        }
                                    } ?>
                                </div>

                                <div class="post-actions">
                                    <a href="<?php echo generatePostUrl(
                                        $post
                                    ); ?>" class="read-more">
                                        Read More &raquo;
                                    </a>
                                </div>
                            </div>
                        </article>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="pagination-container">
                    <?php
                    if ($searchQuery) {
                        $baseUrl =
                            "index.php?search=" . urlencode($searchQuery);
                    } elseif ($categoryFilter) {
                        $baseUrl =
                            "index.php?category=" . urlencode($categoryFilter);
                    } else {
                        $baseUrl = "index.php";
                    }
                    echo generatePagination(
                        $currentPage,
                        $totalPages,
                        $baseUrl
                    );
                    ?>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <footer class="site-footer">
        <div class="container">
            <p>&copy; <?php echo date(
                "Y"
            ); ?> Minimal CMS. Built with security in mind.</p>
        </div>
    </footer>
</body>
</html>
