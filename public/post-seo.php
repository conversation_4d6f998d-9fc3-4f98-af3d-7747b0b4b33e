<?php
/**
 * SEO-Friendly Post View Handler
 * Handles URLs in format: /category/slug/
 */

if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once __DIR__ . "/../src/includes/init.php";
}

// Get the request URI and parse it
$requestUri = $_SERVER["REQUEST_URI"];
$path = parse_url($requestUri, PHP_URL_PATH);

// Remove leading slash and split into parts
$pathParts = explode("/", trim($path, "/"));

// We expect at least 2 parts: category and slug
if (count($pathParts) < 2) {
    header("HTTP/1.0 404 Not Found");
    include "404.php";
    exit();
}

$category = $pathParts[0];
$slug = $pathParts[1];

// Validate category
$validCategories = Post::getValidCategories();
if (!in_array($category, $validCategories)) {
    header("HTTP/1.0 404 Not Found");
    include "404.php";
    exit();
}

try {
    // Get the post by category and slug (published only for public view)
    $post = $postManager->getPostBySlug($category, $slug, true);

    if (!$post) {
        header("HTTP/1.0 404 Not Found");
        include "404.php";
        exit();
    }
} catch (Exception $e) {
    error_log("Error loading post: " . $e->getMessage());
    header("HTTP/1.0 500 Internal Server Error");
    include "500.php";
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo Security::escape($post["title"]); ?> - Minimal CMS</title>
    <link rel="stylesheet" href="/assets/style.css">
    <?php // Use description if available, otherwise generate excerpt from content
    $metaDescription = !empty($post["description"])
        ? $post["description"]
        : generateExcerpt($post["content"], 160); ?>
    <meta name="description" content="<?php echo Security::escape(
        $metaDescription
    ); ?>">

    <!-- SEO Meta Tags -->
    <meta property="og:title" content="<?php echo Security::escape(
        $post["title"]
    ); ?>">
    <meta property="og:description" content="<?php echo Security::escape(
        $metaDescription
    ); ?>">
    <meta property="og:type" content="article">
    <meta property="og:url" content="<?php echo "http" .
        (isset($_SERVER["HTTPS"]) ? "s" : "") .
        "://" .
        $_SERVER["HTTP_HOST"] .
        generatePostUrl($post); ?>">
    <?php if (!empty($post["thumbnail_url"])): ?>
    <meta property="og:image" content="<?php echo "http" .
        (isset($_SERVER["HTTPS"]) ? "s" : "") .
        "://" .
        $_SERVER["HTTP_HOST"] .
        Security::escape($post["thumbnail_url"]); ?>">
    <?php endif; ?>
    <meta property="article:author" content="<?php echo Security::escape(
        $post["author_name"]
    ); ?>">
    <meta property="article:published_time" content="<?php echo date(
        "c",
        strtotime($post["created_at"])
    ); ?>">
    <meta property="article:modified_time" content="<?php echo date(
        "c",
        strtotime($post["updated_at"])
    ); ?>">
    <meta property="article:section" content="<?php echo Security::escape(
        ucfirst($post["category"])
    ); ?>">

    <meta name="twitter:card" content="<?php echo !empty($post["thumbnail_url"])
        ? "summary_large_image"
        : "summary"; ?>">
    <meta name="twitter:title" content="<?php echo Security::escape(
        $post["title"]
    ); ?>">
    <meta name="twitter:description" content="<?php echo Security::escape(
        $metaDescription
    ); ?>">
    <?php if (!empty($post["thumbnail_url"])): ?>
    <meta name="twitter:image" content="<?php echo "http" .
        (isset($_SERVER["HTTPS"]) ? "s" : "") .
        "://" .
        $_SERVER["HTTP_HOST"] .
        Security::escape($post["thumbnail_url"]); ?>">
    <?php endif; ?>

    <!-- Canonical URL -->
    <link rel="canonical" href="<?php echo "http" .
        (isset($_SERVER["HTTPS"]) ? "s" : "") .
        "://" .
        $_SERVER["HTTP_HOST"] .
        generatePostUrl($post); ?>">
</head>
<body>
    <header class="site-header">
        <div class="container">
            <h1 class="site-title">
                <a href="/index.php">Minimal CMS</a>
            </h1>

            <nav class="main-nav">
                <ul>
                    <li><a href="/index.php">Home</a></li>
                    <li><a href="/admin/">Admin</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <nav class="breadcrumb">
                <a href="/index.php">Home</a> &raquo;
                <a href="/index.php?category=<?php echo Security::escape(
                    $post["category"]
                ); ?>"><?php echo Security::escape(
    ucfirst($post["category"])
); ?></a> &raquo;
                <span><?php echo Security::escape($post["title"]); ?></span>
            </nav>

            <article class="post-single">
                <?php if (!empty($post["thumbnail_url"])): ?>
                    <div class="post-thumbnail-single">
                        <img src="<?php echo Security::escape(
                            $post["thumbnail_url"]
                        ); ?>"
                             alt="<?php echo Security::escape(
                                 $post["title"]
                             ); ?>"
                             class="single-thumbnail-image">
                    </div>
                <?php endif; ?>

                <header class="post-header">
                    <h1 class="post-title">
                        <?php echo Security::escape($post["title"]); ?>
                    </h1>

                    <div class="post-meta">
                        <span class="post-category">
                            <span class="category-badge category-<?php echo Security::escape(
                                $post["category"] ?? "tech"
                            ); ?>">
                                <?php echo Security::escape(
                                    ucfirst($post["category"] ?? "tech")
                                ); ?>
                            </span>
                        </span>
                        <span class="post-author">
                            By <?php echo Security::escape(
                                $post["author_name"]
                            ); ?>
                        </span>
                        <span class="post-date">
                            Published <?php echo formatDate(
                                $post["created_at"]
                            ); ?>
                        </span>
                        <?php if (
                            $post["updated_at"] !== $post["created_at"]
                        ): ?>
                            <span class="post-updated">
                                Last updated <?php echo formatDate(
                                    $post["updated_at"]
                                ); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </header>

                <div class="post-content">
                    <?php if (containsHtml($post["content"])) {
                        // Content contains HTML, display as-is (already sanitized during input)
                        echo $post["content"];
                    } else {
                        // Parse markdown content and display
                        echo parseMarkdown($post["content"]);
                    } ?>
                </div>

                <footer class="post-footer">
                    <div class="post-actions">
                        <a href="/index.php" class="back-link">&laquo; Back to Posts</a>
                    </div>
                </footer>
            </article>
        </div>
    </main>

    <footer class="site-footer">
        <div class="container">
            <p>&copy; <?php echo date(
                "Y"
            ); ?> Minimal CMS. Built with security in mind.</p>
        </div>
    </footer>
</body>
</html>
