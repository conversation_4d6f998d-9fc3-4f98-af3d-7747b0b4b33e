<?php
/**
 * Manual Test for Mandatory Fields Feature
 * This script tests the mandatory description and thumbnail validation
 */

session_start();
require_once __DIR__ . '/src/includes/config.php';
require_once __DIR__ . '/src/includes/functions.php';
require_once __DIR__ . '/src/classes/Database.php';
require_once __DIR__ . '/src/classes/Security.php';
require_once __DIR__ . '/src/classes/Post.php';

echo "<h1>Manual Test: Mandatory Fields Validation</h1>\n";

$testResults = [];

// Test 1: Verify description field type in database
echo "<h2>Test 1: Database Schema Check</h2>\n";
try {
    $db = Database::getInstance();
    $result = $db->query("DESCRIBE posts");
    $columns = $result->fetchAll(PDO::FETCH_ASSOC);
    
    $descriptionColumn = null;
    foreach ($columns as $column) {
        if ($column['Field'] === 'description') {
            $descriptionColumn = $column;
            break;
        }
    }
    
    if ($descriptionColumn) {
        echo "✅ Description column exists in posts table<br>\n";
        echo "   Type: " . $descriptionColumn['Type'] . "<br>\n";
        echo "   Null: " . $descriptionColumn['Null'] . "<br>\n";
        $testResults['db_schema'] = 'PASS';
    } else {
        echo "❌ Description column not found in posts table<br>\n";
        $testResults['db_schema'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>\n";
    $testResults['db_schema'] = 'ERROR';
}

// Test 2: Test Post class validation - missing description
echo "<h2>Test 2: Post Class Validation - Missing Description</h2>\n";
try {
    $post = new Post();
    $result = $post->createPost(
        'Test Title ' . time(), 
        'Test content', 
        true, 
        1, 
        'tech', 
        'https://example.com/image.jpg', 
        '' // Empty description
    );
    echo "❌ Post creation should have failed with empty description<br>\n";
    $testResults['missing_description'] = 'FAIL';
} catch (Exception $e) {
    if (strpos($e->getMessage(), 'Description is required') !== false) {
        echo "✅ Correctly rejected empty description: " . $e->getMessage() . "<br>\n";
        $testResults['missing_description'] = 'PASS';
    } else {
        echo "❌ Wrong error message: " . $e->getMessage() . "<br>\n";
        $testResults['missing_description'] = 'FAIL';
    }
}

// Test 3: Test Post class validation - missing thumbnail
echo "<h2>Test 3: Post Class Validation - Missing Thumbnail</h2>\n";
try {
    $post = new Post();
    $result = $post->createPost(
        'Test Title ' . time(), 
        'Test content', 
        true, 
        1, 
        'tech', 
        '', // Empty thumbnail
        'Test description'
    );
    echo "❌ Post creation should have failed with empty thumbnail<br>\n";
    $testResults['missing_thumbnail'] = 'FAIL';
} catch (Exception $e) {
    if (strpos($e->getMessage(), 'Thumbnail is required') !== false) {
        echo "✅ Correctly rejected empty thumbnail: " . $e->getMessage() . "<br>\n";
        $testResults['missing_thumbnail'] = 'PASS';
    } else {
        echo "❌ Wrong error message: " . $e->getMessage() . "<br>\n";
        $testResults['missing_thumbnail'] = 'FAIL';
    }
}

// Test 4: Test Post class validation - valid data
echo "<h2>Test 4: Post Class Validation - Valid Data</h2>\n";
try {
    $post = new Post();
    $result = $post->createPost(
        'Test Title Valid ' . time(), 
        'Test content for valid post', 
        true, 
        1, 
        'tech', 
        'https://example.com/valid-image.jpg',
        'Valid test description for the post'
    );
    
    if ($result && is_numeric($result)) {
        echo "✅ Successfully created post with valid data. Post ID: $result<br>\n";
        $testResults['valid_data'] = 'PASS';
        
        // Clean up - delete the test post
        $post->deletePost($result);
        echo "   (Test post cleaned up)<br>\n";
    } else {
        echo "❌ Failed to create post with valid data<br>\n";
        $testResults['valid_data'] = 'FAIL';
    }
} catch (Exception $e) {
    echo "❌ Unexpected error with valid data: " . $e->getMessage() . "<br>\n";
    $testResults['valid_data'] = 'ERROR';
}

// Test 5: Test description length validation
echo "<h2>Test 5: Description Length Validation</h2>\n";
try {
    $post = new Post();
    $longDescription = str_repeat('A', 501); // 501 characters, over limit
    $result = $post->createPost(
        'Test Title Long Desc ' . time(), 
        'Test content', 
        true, 
        1, 
        'tech', 
        'https://example.com/image.jpg',
        $longDescription
    );
    echo "❌ Post creation should have failed with description over 500 characters<br>\n";
    $testResults['description_length'] = 'FAIL';
} catch (Exception $e) {
    if (strpos($e->getMessage(), '500 characters') !== false) {
        echo "✅ Correctly rejected description over 500 characters: " . $e->getMessage() . "<br>\n";
        $testResults['description_length'] = 'PASS';
    } else {
        echo "❌ Wrong error message for long description: " . $e->getMessage() . "<br>\n";
        $testResults['description_length'] = 'FAIL';
    }
}

// Summary
echo "<h2>Test Summary</h2>\n";
$passCount = 0;
$totalCount = count($testResults);

foreach ($testResults as $testName => $result) {
    $icon = $result === 'PASS' ? '✅' : ($result === 'FAIL' ? '❌' : '⚠️');
    echo "$icon $testName: $result<br>\n";
    if ($result === 'PASS') $passCount++;
}

echo "<br><strong>Results: $passCount/$totalCount tests passed</strong><br>\n";

if ($passCount === $totalCount) {
    echo "<h3 style='color: green;'>🎉 All tests passed! Mandatory fields validation is working correctly.</h3>\n";
} else {
    echo "<h3 style='color: red;'>⚠️ Some tests failed. Please review the implementation.</h3>\n";
}

echo "<hr>\n";
echo "<h2>Form Field Test</h2>\n";
echo "<p>Below is a sample form to manually test the UI changes:</p>\n";
?>

<form method="post" style="max-width: 600px; padding: 20px; border: 1px solid #ddd;">
    <div style="margin-bottom: 15px;">
        <label for="test_title" style="display: block; font-weight: bold;">Title *</label>
        <input type="text" id="test_title" name="title" required maxlength="255" style="width: 100%; padding: 8px; border: 1px solid #ccc;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="test_description" style="display: block; font-weight: bold;">Description *</label>
        <input type="text" id="test_description" name="description" required maxlength="500" 
               placeholder="Brief description for SEO and social media previews (max 500 characters)"
               style="width: 100%; padding: 8px; border: 1px solid #ccc;">
        <div style="font-size: 12px; color: #666; margin-top: 5px;">
            This description will be used for SEO meta tags, social media previews (Facebook, Twitter), and post previews on the main page.
            <span id="test-description-counter" style="color: #6c757d;">0/500 characters</span>
        </div>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label style="display: block; font-weight: bold;">Thumbnail Image *</label>
        <input type="url" placeholder="Image URL" required style="width: 100%; padding: 8px; border: 1px solid #ccc;">
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="test_content" style="display: block; font-weight: bold;">Content *</label>
        <textarea id="test_content" name="content" required rows="5" style="width: 100%; padding: 8px; border: 1px solid #ccc;"></textarea>
    </div>
    
    <div style="margin-bottom: 15px;">
        <label for="test_category" style="display: block; font-weight: bold;">Category *</label>
        <select id="test_category" name="category" required style="width: 100%; padding: 8px; border: 1px solid #ccc;">
            <option value="">Select category</option>
            <option value="tech">Tech</option>
            <option value="gaming">Gaming</option>
            <option value="film">Film</option>
            <option value="serie">Serie</option>
        </select>
    </div>
    
    <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; cursor: pointer;">
        Test Form Submission
    </button>
</form>

<script>
// Test the character counter functionality
const testDescriptionField = document.getElementById('test_description');
const testDescriptionCounter = document.getElementById('test-description-counter');

function updateTestDescriptionCounter() {
    const length = testDescriptionField.value.length;
    const maxLength = 500;
    testDescriptionCounter.textContent = `${length}/${maxLength} characters`;
    
    if (length > maxLength * 0.9) {
        testDescriptionCounter.style.color = '#dc3545'; // Red when approaching limit
    } else if (length > maxLength * 0.7) {
        testDescriptionCounter.style.color = '#fd7e14'; // Orange when getting close
    } else {
        testDescriptionCounter.style.color = '#6c757d'; // Default gray
    }
}

// Update counter as user types
testDescriptionField.addEventListener('input', updateTestDescriptionCounter);
testDescriptionField.addEventListener('keyup', updateTestDescriptionCounter);

// Initialize counter
updateTestDescriptionCounter();

console.log('[test-mandatory-fields-manual] Character counter initialized and working');
</script>

<?php
echo "<hr>\n";
echo "<p><strong>Manual Testing Instructions:</strong></p>\n";
echo "<ol>\n";
echo "<li>Try submitting the form above without filling required fields - browser should show validation messages</li>\n";
echo "<li>Notice the description field is a single-line text input (not textarea)</li>\n";
echo "<li>Type in the description field and watch the character counter update with color changes</li>\n";
echo "<li>All fields marked with * are required</li>\n";
echo "<li>Check that the form follows the same styling as the admin interface</li>\n";
echo "</ol>\n";

echo "<p><em>Generated at: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>