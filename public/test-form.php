<?php
/**
 * Simple Form Test for File Uploads
 * Tests if file uploads are working with multipart/form-data
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

if (!defined('CMS_INIT')) {
    define('CMS_INIT', true);
    require_once '../src/includes/init.php';
}

// Check if user is authenticated
if (!$auth->isAuthenticated()) {
    die('Authentication required. Please login first.');
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>Form Submission Debug:</h3>";
    echo "<pre>";
    
    echo "Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'Not set') . "\n";
    echo "Content-Length: " . ($_SERVER['CONTENT_LENGTH'] ?? 'Not set') . "\n\n";
    
    echo "POST data:\n";
    print_r($_POST);
    echo "\n";
    
    echo "FILES data:\n";
    print_r($_FILES);
    echo "\n";
    
    if (isset($_FILES['test_file']) && $_FILES['test_file']['error'] !== UPLOAD_ERR_NO_FILE) {
        $file = $_FILES['test_file'];
        
        echo "File upload detected!\n";
        echo "Name: " . $file['name'] . "\n";
        echo "Type: " . $file['type'] . "\n";
        echo "Size: " . $file['size'] . " bytes (" . round($file['size']/1024/1024, 2) . " MB)\n";
        echo "Error: " . $file['error'] . "\n";
        echo "Temp file: " . $file['tmp_name'] . "\n";
        echo "Temp file exists: " . (file_exists($file['tmp_name']) ? 'YES' : 'NO') . "\n";
        echo "is_uploaded_file: " . (is_uploaded_file($file['tmp_name']) ? 'YES' : 'NO') . "\n";
        
        if ($file['error'] === UPLOAD_ERR_OK) {
            try {
                $imageUpload = new ImageUpload();
                $cropData = null;
                
                if (!empty($_POST['crop_data'])) {
                    $cropData = json_decode($_POST['crop_data'], true);
                    echo "Crop data parsed: " . ($cropData ? 'YES' : 'NO') . "\n";
                }
                
                $result = $imageUpload->uploadImage($file, $cropData);
                echo "Upload result: " . $result . "\n";
                
                $fullPath = PUBLIC_PATH . $result;
                echo "File created: " . (file_exists($fullPath) ? 'YES' : 'NO') . "\n";
                if (file_exists($fullPath)) {
                    echo "Final file size: " . filesize($fullPath) . " bytes\n";
                }
                
                $message = "SUCCESS: File uploaded to " . $result;
            } catch (Exception $e) {
                echo "Upload error: " . $e->getMessage() . "\n";
                $error = "Upload failed: " . $e->getMessage();
            }
        } else {
            $errorMessages = [
                UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize',
                UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE directive',
                UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
                UPLOAD_ERR_NO_FILE => 'No file was uploaded',
                UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
                UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
                UPLOAD_ERR_EXTENSION => 'Upload stopped by extension'
            ];
            $error = "Upload error: " . ($errorMessages[$file['error']] ?? 'Unknown error');
            echo $error . "\n";
        }
    } else {
        echo "NO FILE UPLOAD DETECTED\n";
        if (isset($_FILES['test_file'])) {
            echo "File error code: " . $_FILES['test_file']['error'] . "\n";
        } else {
            echo "No 'test_file' in FILES array\n";
        }
    }
    
    echo "</pre>";
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Upload Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        form { background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin: 20px 0; }
        input, button { padding: 10px; margin: 5px 0; }
        button { background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .form-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>File Upload Form Test</h1>
    <p>This page tests if file uploads are working correctly with multipart/form-data encoding.</p>
    
    <?php if ($message): ?>
        <div class="success"><?php echo htmlspecialchars($message); ?></div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="error"><?php echo htmlspecialchars($error); ?></div>
    <?php endif; ?>
    
    <div class="form-section">
        <h3>Test Form with Correct Encoding</h3>
        <form method="POST" enctype="multipart/form-data">
            <?php echo csrfTokenField(); ?>
            
            <div>
                <label>Select File:</label><br>
                <input type="file" name="test_file" accept="image/*">
            </div>
            
            <div>
                <label>Crop Data (optional):</label><br>
                <input type="text" name="crop_data" placeholder='{"x":10,"y":10,"width":100,"height":100}' style="width: 100%;">
            </div>
            
            <div>
                <button type="submit">Test Upload (Correct Encoding)</button>
            </div>
        </form>
    </div>
    
    <div class="form-section">
        <h3>Test Form with WRONG Encoding (Should Fail)</h3>
        <form method="POST">
            <?php echo csrfTokenField(); ?>
            
            <div>
                <label>Select File:</label><br>
                <input type="file" name="test_file" accept="image/*">
            </div>
            
            <div>
                <button type="submit">Test Upload (Wrong Encoding)</button>
            </div>
        </form>
        <p style="color: #856404; background: #fff3cd; padding: 10px; border-radius: 4px;">
            ⚠️ This form is missing <code>enctype="multipart/form-data"</code> and should NOT work for file uploads.
        </p>
    </div>
    
    <h3>Current PHP Configuration</h3>
    <pre><?php
        echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
        echo "post_max_size: " . ini_get('post_max_size') . "\n";
        echo "memory_limit: " . ini_get('memory_limit') . "\n";
        echo "max_execution_time: " . ini_get('max_execution_time') . "\n";
        echo "GD available: " . (extension_loaded('gd') ? 'YES' : 'NO') . "\n";
        
        $uploadDir = PUBLIC_PATH . '/uploads/thumbnails/';
        echo "\nUpload directory: " . $uploadDir . "\n";
        echo "Directory exists: " . (is_dir($uploadDir) ? 'YES' : 'NO') . "\n";
        echo "Directory writable: " . (is_writable($uploadDir) ? 'YES' : 'NO') . "\n";
    ?></pre>
    
    <h3>Recent Uploads</h3>
    <pre><?php
        $uploadDir = PUBLIC_PATH . '/uploads/thumbnails/';
        if (is_dir($uploadDir)) {
            $files = glob($uploadDir . '*');
            if (empty($files)) {
                echo "No files in uploads directory\n";
            } else {
                echo "Files in uploads directory:\n";
                foreach (array_slice($files, -5) as $file) { // Show last 5 files
                    if (is_file($file)) {
                        $size = filesize($file);
                        $time = date('Y-m-d H:i:s', filemtime($file));
                        echo basename($file) . " - " . $size . " bytes - " . $time . "\n";
                    }
                }
            }
        } else {
            echo "Upload directory does not exist\n";
        }
    ?></pre>
</body>
</html>