<?php
/**
 * REST API Endpoint: Posts
 * Handles creation of new blog posts via API
 */

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set("display_errors", 0); // Don't show errors to API clients

// Set JSON response header
header("Content-Type: application/json");

// Allow CORS for external applications
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// Handle preflight OPTIONS request
if ($_SERVER["REQUEST_METHOD"] === "OPTIONS") {
    http_response_code(200);
    exit();
}

try {
    // Initialize CMS
    if (!defined("CMS_INIT")) {
        define("CMS_INIT", true);
        require_once "../../src/includes/init.php";
    }

    /**
     * Log API request
     */
    function logApiRequest($action, $details, $userId = null)
    {
        $logMessage = date("Y-m-d H:i:s") . " - API: $action - $details";
        if ($userId) {
            $logMessage .= " - User ID: $userId";
        }
        $logMessage .= " - IP: " . Security::getClientIP() . "\n";
        error_log($logMessage, 3, LOGS_PATH . "/api.log");
    }

    // Only allow POST requests for creating posts
    if ($_SERVER["REQUEST_METHOD"] !== "POST") {
        http_response_code(405);
        echo json_encode([
            "error" => "Method not allowed. Use POST to create posts.",
        ]);
        logApiRequest("invalid_method", $_SERVER["REQUEST_METHOD"]);
        exit();
    }

    // Get authorization header
    $headers = getallheaders();
    $authHeader = null;

    // Handle different header formats
    foreach ($headers as $key => $value) {
        if (strtolower($key) === "authorization") {
            $authHeader = $value;
            break;
        }
    }

    if (!$authHeader) {
        http_response_code(401);
        echo json_encode(["error" => "No authorization header provided"]);
        logApiRequest("no_auth_header", "Missing Authorization header");
        exit();
    }

    // Extract Bearer token
    if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        http_response_code(401);
        echo json_encode([
            "error" => "Invalid authorization format. Use: Bearer YOUR_TOKEN",
        ]);
        logApiRequest("invalid_auth_format", $authHeader);
        exit();
    }

    $token = trim($matches[1]);

    if (empty($token)) {
        http_response_code(401);
        echo json_encode(["error" => "No token provided"]);
        logApiRequest("empty_token", "Token is empty");
        exit();
    }

    // Validate API token
    $user = $auth->validateApiToken($token);
    if (!$user) {
        http_response_code(401);
        echo json_encode(["error" => "Invalid token"]);
        logApiRequest("invalid_token", "Token validation failed");
        exit();
    }

    logApiRequest(
        "token_validated",
        "Valid token for user: " . $user["username"],
        $user["id"]
    );

    // Parse JSON request body
    $inputData = file_get_contents("php://input");
    $data = json_decode($inputData, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        http_response_code(400);
        echo json_encode(["error" => "Invalid JSON in request body"]);
        logApiRequest(
            "invalid_json",
            "JSON decode error: " . json_last_error_msg(),
            $user["id"]
        );
        exit();
    }

    // Validate required fields
    if (
        !isset($data["title"]) ||
        !isset($data["content"]) ||
        !isset($data["description"]) ||
        !isset($data["thumbnail_url"])
    ) {
        http_response_code(400);
        echo json_encode([
            "error" =>
                "Missing required fields: title, content, description, and thumbnail_url are required",
        ]);
        logApiRequest("missing_fields", "Missing required fields", $user["id"]);
        exit();
    }

    $title = trim($data["title"]);
    $content = trim($data["content"]);
    $description = trim($data["description"]);
    $thumbnailUrl = trim($data["thumbnail_url"]);

    // Validate field lengths
    if (empty($title)) {
        http_response_code(400);
        echo json_encode(["error" => "Title cannot be empty"]);
        logApiRequest("empty_title", "Title is empty", $user["id"]);
        exit();
    }

    if (empty($content)) {
        http_response_code(400);
        echo json_encode(["error" => "Content cannot be empty"]);
        logApiRequest("empty_content", "Content is empty", $user["id"]);
        exit();
    }

    if (empty($description)) {
        http_response_code(400);
        echo json_encode(["error" => "Description cannot be empty"]);
        logApiRequest("empty_description", "Description is empty", $user["id"]);
        exit();
    }

    if (empty($thumbnailUrl)) {
        http_response_code(400);
        echo json_encode(["error" => "Thumbnail URL cannot be empty"]);
        logApiRequest("empty_thumbnail", "Thumbnail URL is empty", $user["id"]);
        exit();
    }

    if (strlen($title) > 255) {
        http_response_code(400);
        echo json_encode([
            "error" => "Title too long (maximum 255 characters)",
        ]);
        logApiRequest(
            "title_too_long",
            "Title length: " . strlen($title),
            $user["id"]
        );
        exit();
    }

    if (strlen($description) > 500) {
        http_response_code(400);
        echo json_encode([
            "error" => "Description too long (maximum 500 characters)",
        ]);
        logApiRequest(
            "description_too_long",
            "Description length: " . strlen($description),
            $user["id"]
        );
        exit();
    }

    // Validate thumbnail URL format
    if (!filter_var($thumbnailUrl, FILTER_VALIDATE_URL)) {
        http_response_code(400);
        echo json_encode(["error" => "Invalid thumbnail URL format"]);
        logApiRequest(
            "invalid_thumbnail_url",
            "Invalid URL: " . $thumbnailUrl,
            $user["id"]
        );
        exit();
    }

    // Sanitize input
    $title = Security::escape($title);
    $content = Security::escape($content);
    $description = Security::escape($description);
    $thumbnailUrl = Security::escape($thumbnailUrl);

    // Generate slug from title
    $slug = strtolower(
        trim(preg_replace("/[^A-Za-z0-9-]+/", "-", $title), "-")
    );

    // Ensure slug is unique
    $originalSlug = $slug;
    $counter = 1;

    $db = Database::getInstance();
    while (true) {
        $existingPost = $db->fetchOne("SELECT id FROM posts WHERE slug = ?", [
            $slug,
        ]);

        if (!$existingPost) {
            break;
        }

        $slug = $originalSlug . "-" . $counter;
        $counter++;
    }

    logApiRequest(
        "creating_post",
        "Title: $title, Content length: " .
            strlen($content) .
            ", Description length: " .
            strlen($description),
        $user["id"]
    );

    // Create the post
    try {
        $db->beginTransaction();

        $stmt = $db->query(
            "INSERT INTO posts (title, content, description, thumbnail_url, slug, author_id, is_draft, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)",
            [$title, $content, $description, $thumbnailUrl, $slug, $user["id"]]
        );

        $postId = $db->lastInsertId();

        $db->commit();

        // Log successful creation
        logApiRequest(
            "post_created",
            "Post ID: $postId, Title: $title",
            $user["id"]
        );

        // Return success response
        http_response_code(201);
        echo json_encode([
            "success" => true,
            "id" => (int) $postId,
            "slug" => $slug,
            "message" => "Post created successfully",
        ]);
    } catch (Exception $e) {
        $db->rollback();

        error_log("API: Failed to create post - " . $e->getMessage());
        logApiRequest("post_creation_failed", $e->getMessage(), $user["id"]);

        http_response_code(500);
        echo json_encode(["error" => "Failed to create post"]);
    }
} catch (Exception $e) {
    // Log unexpected errors
    error_log("API: Unexpected error - " . $e->getMessage());
    logApiRequest("unexpected_error", $e->getMessage());

    http_response_code(500);
    echo json_encode(["error" => "Internal server error"]);
}
?>
