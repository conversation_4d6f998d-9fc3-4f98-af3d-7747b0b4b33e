<?php
/**
 * Debug Upload Test Page
 * Simple test to debug image upload functionality
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

if (!defined('CMS_INIT')) {
    define('CMS_INIT', true);
    require_once '../src/includes/init.php';
}

// Check if user is authenticated
if (!$auth->isAuthenticated()) {
    die('Authentication required. Please login first.');
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        echo "<h3>Debug Information:</h3>";
        echo "<pre>";
        echo "PHP Version: " . PHP_VERSION . "\n";
        echo "GD Extension: " . (extension_loaded('gd') ? 'Available' : 'NOT AVAILABLE') . "\n";
        echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";
        echo "Post Max Size: " . ini_get('post_max_size') . "\n";
        echo "Max File Uploads: " . ini_get('max_file_uploads') . "\n";
        echo "\n";
        
        echo "FILES array:\n";
        var_dump($_FILES);
        echo "\n";
        
        echo "POST array:\n";
        var_dump($_POST);
        echo "\n";
        
        if (isset($_FILES['test_image']) && $_FILES['test_image']['error'] !== UPLOAD_ERR_NO_FILE) {
            echo "Processing upload...\n";
            
            $file = $_FILES['test_image'];
            echo "File name: " . $file['name'] . "\n";
            echo "File type: " . $file['type'] . "\n";
            echo "File size: " . $file['size'] . " bytes (" . round($file['size']/1024/1024, 2) . " MB)\n";
            echo "File error code: " . $file['error'] . "\n";
            
            // Decode error code
            $errorMessages = [
                UPLOAD_ERR_OK => 'No error',
                UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize',
                UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE directive',
                UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
                UPLOAD_ERR_NO_FILE => 'No file was uploaded',
                UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
                UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
                UPLOAD_ERR_EXTENSION => 'Upload stopped by extension'
            ];
            echo "Error meaning: " . ($errorMessages[$file['error']] ?? 'Unknown error') . "\n";
            
            echo "Temp file: " . $file['tmp_name'] . "\n";
            echo "Temp file exists: " . (file_exists($file['tmp_name']) ? 'YES' : 'NO') . "\n";
            echo "Temp file readable: " . (is_readable($file['tmp_name']) ? 'YES' : 'NO') . "\n";
            echo "is_uploaded_file(): " . (is_uploaded_file($file['tmp_name']) ? 'YES' : 'NO') . "\n";
            
            if (file_exists($file['tmp_name'])) {
                echo "Temp file size: " . filesize($file['tmp_name']) . " bytes\n";
                $mimeType = mime_content_type($file['tmp_name']);
                echo "Actual MIME type: " . $mimeType . "\n";
                
                $imageInfo = getimagesize($file['tmp_name']);
                if ($imageInfo) {
                    echo "Image dimensions: " . $imageInfo[0] . "x" . $imageInfo[1] . "\n";
                    echo "Image type: " . $imageInfo['mime'] . "\n";
                    echo "Image bits: " . $imageInfo['bits'] . "\n";
                    
                    // Test if GD can load it
                    echo "Testing GD processing of uploaded file...\n";
                    $gdImage = null;
                    switch ($imageInfo['mime']) {
                        case 'image/jpeg':
                            $gdImage = imagecreatefromjpeg($file['tmp_name']);
                            break;
                        case 'image/png':
                            $gdImage = imagecreatefrompng($file['tmp_name']);
                            break;
                        case 'image/gif':
                            $gdImage = imagecreatefromgif($file['tmp_name']);
                            break;
                        case 'image/webp':
                            $gdImage = imagecreatefromwebp($file['tmp_name']);
                            break;
                    }
                    
                    if ($gdImage) {
                        echo "GD can process uploaded file: YES\n";
                        echo "GD dimensions: " . imagesx($gdImage) . "x" . imagesy($gdImage) . "\n";
                        imagedestroy($gdImage);
                    } else {
                        echo "GD can process uploaded file: NO\n";
                    }
                } else {
                    echo "Not a valid image file\n";
                }
            }
            
            // Test upload directory
            $uploadDir = PUBLIC_PATH . '/uploads/thumbnails/';
            echo "\nUpload directory: " . $uploadDir . "\n";
            echo "Directory exists: " . (is_dir($uploadDir) ? 'YES' : 'NO') . "\n";
            echo "Directory writable: " . (is_writable($uploadDir) ? 'YES' : 'NO') . "\n";
            echo "Directory permissions: " . (is_dir($uploadDir) ? substr(sprintf('%o', fileperms($uploadDir)), -4) : 'N/A') . "\n";
            
            // Try to create a test file
            $testFile = $uploadDir . 'test_' . time() . '.txt';
            if (file_put_contents($testFile, 'test')) {
                echo "Test file creation: SUCCESS\n";
                unlink($testFile);
            } else {
                echo "Test file creation: FAILED\n";
            }
            
            // Test ImageUpload class validation steps
            echo "\nTesting ImageUpload validation steps...\n";
            
            // Check file size limit
            $maxSize = 5 * 1024 * 1024;
            echo "File size check: " . ($file['size'] <= $maxSize ? 'PASS' : 'FAIL') . " (" . $file['size'] . " <= " . $maxSize . ")\n";
            
            // Check MIME type
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            $actualMime = file_exists($file['tmp_name']) ? mime_content_type($file['tmp_name']) : 'unknown';
            echo "MIME type check: " . (in_array($actualMime, $allowedTypes) ? 'PASS' : 'FAIL') . " (" . $actualMime . ")\n";
            
            // Check extension
            $allowedExts = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            echo "Extension check: " . (in_array($ext, $allowedExts) ? 'PASS' : 'FAIL') . " (" . $ext . ")\n";
            
            // Test ImageUpload class
            echo "\nTesting ImageUpload class...\n";
            try {
                $imageUpload = new ImageUpload();
                echo "ImageUpload class instantiated successfully\n";
                
                // Parse crop data if provided
                $cropData = null;
                if (isset($_POST['crop_data']) && !empty($_POST['crop_data'])) {
                    echo "Crop data provided: " . $_POST['crop_data'] . "\n";
                    $cropData = json_decode($_POST['crop_data'], true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        echo "Crop data parsed successfully\n";
                        echo "Crop coordinates: x=" . $cropData['x'] . ", y=" . $cropData['y'] . ", w=" . $cropData['width'] . ", h=" . $cropData['height'] . "\n";
                    } else {
                        echo "Crop data JSON error: " . json_last_error_msg() . "\n";
                        $cropData = null;
                    }
                }
                
                // Attempt upload
                echo "Attempting upload...\n";
                $uploadedPath = $imageUpload->uploadImage($file, $cropData);
                echo "Upload successful! Path: " . $uploadedPath . "\n";
                
                // Check if file actually exists
                $fullPath = PUBLIC_PATH . $uploadedPath;
                echo "Full file path: " . $fullPath . "\n";
                echo "File exists after upload: " . (file_exists($fullPath) ? 'YES' : 'NO') . "\n";
                
                if (file_exists($fullPath)) {
                    $filesize = filesize($fullPath);
                    echo "File size after upload: " . $filesize . " bytes (" . round($filesize/1024/1024, 2) . " MB)\n";
                    
                    // Verify it's still a valid image
                    $finalInfo = getimagesize($fullPath);
                    if ($finalInfo) {
                        echo "Final image dimensions: " . $finalInfo[0] . "x" . $finalInfo[1] . "\n";
                        echo "Final image MIME: " . $finalInfo['mime'] . "\n";
                    }
                    
                    $message = "Upload successful! File saved to: " . $uploadedPath;
                } else {
                    $error = "File was not found after upload";
                }
                
            } catch (Exception $e) {
                echo "ImageUpload error: " . $e->getMessage() . "\n";
                echo "Exception file: " . $e->getFile() . " line " . $e->getLine() . "\n";
                echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
                $error = "Upload failed: " . $e->getMessage();
            }
        } else {
            echo "No file uploaded or upload error\n";
            if (isset($_FILES['test_image'])) {
                echo "Upload error code: " . $_FILES['test_image']['error'] . "\n";
                $errorMessages = [
                    UPLOAD_ERR_OK => 'No error',
                    UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize',
                    UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE directive',
                    UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
                    UPLOAD_ERR_NO_FILE => 'No file was uploaded',
                    UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
                    UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
                    UPLOAD_ERR_EXTENSION => 'Upload stopped by extension'
                ];
                echo "Error meaning: " . ($errorMessages[$_FILES['test_image']['error']] ?? 'Unknown error') . "\n";
            } else {
                echo "FILES array does not contain 'test_image'\n";
                echo "Available keys in FILES: " . implode(', ', array_keys($_FILES)) . "\n";
            }
        }
        
        echo "</pre>";
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
        echo "<pre>Exception: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "</pre>";
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Upload Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        form { background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin: 20px 0; }
        input, button { padding: 10px; margin: 5px 0; }
        button { background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .crop-area { margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        #imagePreview { max-width: 100%; height: auto; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Debug Upload Test</h1>
    <p>This page tests the image upload functionality to help debug any issues.</p>
    
    <?php if ($message): ?>
        <div class="success"><?php echo htmlspecialchars($message); ?></div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="error"><?php echo htmlspecialchars($error); ?></div>
    <?php endif; ?>
    
    <form method="POST" enctype="multipart/form-data">
        <?php echo csrfTokenField(); ?>
        
        <h3>Upload Test Image</h3>
        <div>
            <label>Select Image File:</label><br>
            <input type="file" name="test_image" accept="image/*" id="imageInput">
        </div>
        
        <div class="crop-area">
            <label>Crop Data (JSON - optional):</label><br>
            <input type="text" name="crop_data" placeholder='{"x":10,"y":10,"width":100,"height":100}' style="width: 100%;">
            <small>Example: {"x":10,"y":10,"width":100,"height":100}</small>
        </div>
        
        <div>
            <button type="submit">Test Upload</button>
        </div>
    </form>
    
    <div id="imagePreviewContainer" style="display: none;">
        <h3>Image Preview</h3>
        <img id="imagePreview" src="" alt="Preview">
    </div>
    
    <h3>System Information</h3>
    <pre><?php
        echo "PHP Version: " . PHP_VERSION . "\n";
        echo "GD Extension: " . (extension_loaded('gd') ? 'Available' : 'NOT AVAILABLE') . "\n";
        if (extension_loaded('gd')) {
            $gdInfo = gd_info();
            echo "GD Version: " . $gdInfo['GD Version'] . "\n";
            echo "JPEG Support: " . ($gdInfo['JPEG Support'] ? 'YES' : 'NO') . "\n";
            echo "PNG Support: " . ($gdInfo['PNG Support'] ? 'YES' : 'NO') . "\n";
            echo "GIF Support: " . ($gdInfo['GIF Create Support'] ? 'YES' : 'NO') . "\n";
            echo "WebP Support: " . ($gdInfo['WebP Support'] ? 'YES' : 'NO') . "\n";
        }
        echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";
        echo "Post Max Size: " . ini_get('post_max_size') . "\n";
        echo "Max File Uploads: " . ini_get('max_file_uploads') . "\n";
        echo "Memory Limit: " . ini_get('memory_limit') . "\n";
        echo "Max Execution Time: " . ini_get('max_execution_time') . " seconds\n";
        
        $uploadDir = PUBLIC_PATH . '/uploads/thumbnails/';
        echo "\nUpload Directory: " . $uploadDir . "\n";
        echo "Directory Exists: " . (is_dir($uploadDir) ? 'YES' : 'NO') . "\n";
        echo "Directory Writable: " . (is_writable($uploadDir) ? 'YES' : 'NO') . "\n";
        
        if (is_dir($uploadDir)) {
            $permissions = substr(sprintf('%o', fileperms($uploadDir)), -4);
            echo "Directory Permissions: " . $permissions . "\n";
        }
    ?></pre>
    
    <script>
        document.getElementById('imageInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('imagePreview');
                    preview.src = e.target.result;
                    document.getElementById('imagePreviewContainer').style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>
</html>