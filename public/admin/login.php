<?php
/**
 * Admin Login Page
 * Handles user authentication for admin access
 */

if (!defined('CMS_INIT')) {
    define('CMS_INIT', true);
    require_once '../../src/includes/init.php';
}

// Redirect if already logged in
if ($auth->isAuthenticated()) {
    redirect('index.php');
}

$error = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        validateCSRF();
        
        $username = Security::sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        if (empty($username) || empty($password)) {
            throw new Exception('Username and password are required.');
        }
        
        // Attempt login
        $user = $auth->login($username, $password);
        
        // Log successful login
        logActivity('admin_login', 'User logged in successfully', $user['id']);
        
        // Redirect to admin dashboard
        addFlashMessage('Welcome back, ' . $user['username'] . '!', 'success');
        redirect('index.php');
        
    } catch (Exception $e) {
        $error = $e->getMessage();
        logActivity('admin_login_failed', 'Login attempt failed: ' . $error);
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Minimal CMS</title>
    <link rel="stylesheet" href="/assets/style.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-form-wrapper">
            <header class="login-header">
                <h1>Admin Login</h1>
                <p>Sign in to manage your content</p>
            </header>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <?php echo Security::escape($error); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="login.php" class="login-form">
                <?php echo csrfTokenField(); ?>
                
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" 
                           id="username" 
                           name="username" 
                           value="<?php echo Security::escape($_POST['username'] ?? ''); ?>"
                           required 
                           autocomplete="username"
                           class="form-control">
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" 
                           id="password" 
                           name="password" 
                           required 
                           autocomplete="current-password"
                           class="form-control">
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="button button-primary">
                        Sign In
                    </button>
                </div>
            </form>
            
            <div class="login-footer">
                <p><a href="../index.php">&laquo; Back to Site</a></p>
            </div>
        </div>
    </div>
    
    <script>
        // Focus on username field
        document.getElementById('username').focus();
        
        // Basic client-side validation
        document.querySelector('.login-form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                e.preventDefault();
                alert('Please enter both username and password.');
                return false;
            }
            
            if (username.length < 3) {
                e.preventDefault();
                alert('Username must be at least 3 characters long.');
                return false;
            }
        });
    </script>
</body>
</html>
