<?php
/**
 * Image Upload Endpoint for Thumbnails
 * Handles AJAX image uploads and returns JSON responses
 */

// Disable output buffering for AJAX
while (ob_get_level()) {
    ob_end_clean();
}

// Set JSON content type
header('Content-Type: application/json');

if (!defined('CMS_INIT')) {
    define('CMS_INIT', true);
    require_once '../../src/includes/init.php';
}

// Require authentication
try {
    $auth->requireAuth();
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Validate CSRF token
try {
    validateCSRF();
} catch (Exception $e) {
    http_response_code(403);
    echo json_encode(['error' => 'Invalid CSRF token']);
    exit;
}

// Check if GD extension is available
if (!ImageUpload::isGdAvailable()) {
    http_response_code(500);
    echo json_encode(['error' => 'Image processing not available. GD extension is required.']);
    exit;
}

try {
    // Check if file was uploaded
    if (!isset($_FILES['image']) || $_FILES['image']['error'] === UPLOAD_ERR_NO_FILE) {
        throw new Exception('No image file uploaded');
    }

    $uploadedFile = $_FILES['image'];
    $cropData = null;

    // Parse crop data if provided
    if (isset($_POST['crop_data'])) {
        $cropData = json_decode($_POST['crop_data'], true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid crop data format');
        }
    }

    // Initialize image upload handler
    $imageUpload = new ImageUpload();

    // Upload and process the image
    $imagePath = $imageUpload->uploadImage($uploadedFile, $cropData);

    // Get image dimensions for response
    $dimensions = $imageUpload->getImageDimensions($imagePath);

    // Log the upload activity
    $currentUser = $auth->getCurrentUser();
    logActivity('image_uploaded', 'Uploaded thumbnail image: ' . basename($imagePath), $currentUser['id']);

    // Return success response
    echo json_encode([
        'success' => true,
        'image_url' => $imagePath,
        'image_path' => $imagePath,
        'dimensions' => $dimensions,
        'message' => 'Image uploaded successfully'
    ]);

} catch (Exception $e) {
    // Log the error
    error_log("Image upload error: " . $e->getMessage());
    
    // Return error response
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>