<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>API Test Interface</title>
        <style>
            * {
                box-sizing: border-box;
            }

            body {
                font-family:
                    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
                    sans-serif;
                max-width: 900px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
                color: #333;
            }

            .container {
                background: white;
                border-radius: 8px;
                padding: 2rem;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }

            h1 {
                color: #2c3e50;
                text-align: center;
                margin-bottom: 2rem;
            }

            .form-section {
                margin-bottom: 2rem;
                padding: 1.5rem;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                background-color: #fafafa;
            }

            .form-section h3 {
                margin-top: 0;
                color: #34495e;
            }

            .form-group {
                margin-bottom: 1rem;
            }

            label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 600;
                color: #555;
            }

            input[type="text"],
            input[type="url"],
            textarea {
                width: 100%;
                padding: 0.75rem;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 1rem;
                transition: border-color 0.3s ease;
            }

            input[type="text"]:focus,
            input[type="url"]:focus,
            textarea:focus {
                outline: none;
                border-color: #3498db;
            }

            textarea {
                resize: vertical;
                min-height: 120px;
                font-family: inherit;
            }

            .button {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 4px;
                font-size: 1rem;
                cursor: pointer;
                transition: background-color 0.3s ease;
            }

            .button:hover {
                background-color: #2980b9;
            }

            .button:disabled {
                background-color: #bdc3c7;
                cursor: not-allowed;
            }

            .button-warning {
                background-color: #f39c12;
            }

            .button-warning:hover {
                background-color: #e67e22;
            }

            .response-section {
                margin-top: 2rem;
            }

            #response {
                background-color: #2c3e50;
                color: #ecf0f1;
                padding: 1rem;
                border-radius: 4px;
                font-family: "Courier New", monospace;
                font-size: 0.9rem;
                white-space: pre-wrap;
                overflow-x: auto;
                min-height: 100px;
                border: 2px solid #34495e;
            }

            .success {
                border-color: #27ae60 !important;
                background-color: #d5f4e6 !important;
                color: #155724 !important;
            }

            .error {
                border-color: #e74c3c !important;
                background-color: #f8d7da !important;
                color: #721c24 !important;
            }

            .loading {
                border-color: #f39c12 !important;
                background-color: #fff3cd !important;
                color: #856404 !important;
            }

            .status-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
                padding: 0.5rem;
                background-color: #ecf0f1;
                border-radius: 4px;
            }

            .status-code {
                font-weight: bold;
                padding: 0.25rem 0.5rem;
                border-radius: 3px;
                color: white;
            }

            .status-200 {
                background-color: #27ae60;
            }
            .status-201 {
                background-color: #27ae60;
            }
            .status-400 {
                background-color: #e67e22;
            }
            .status-401 {
                background-color: #e74c3c;
            }
            .status-404 {
                background-color: #e74c3c;
            }
            .status-500 {
                background-color: #8e44ad;
            }

            .help-text {
                font-size: 0.875rem;
                color: #666;
                margin-top: 0.25rem;
            }

            .endpoint-info {
                background-color: #e8f4fd;
                border: 1px solid #b8daff;
                border-radius: 4px;
                padding: 1rem;
                margin-bottom: 1rem;
            }

            .quick-fill {
                display: flex;
                gap: 0.5rem;
                margin-top: 0.5rem;
                flex-wrap: wrap;
            }

            .quick-fill button {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 3px;
                cursor: pointer;
            }

            .quick-fill button:hover {
                background-color: #7f8c8d;
            }

            @media (max-width: 768px) {
                body {
                    padding: 10px;
                }

                .container {
                    padding: 1rem;
                }

                .quick-fill {
                    flex-direction: column;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🧪 API Test Interface</h1>
            <p style="text-align: center; color: #666">
                Test your REST API endpoints with ease
            </p>

            <div class="endpoint-info">
                <strong>📍 Endpoint:</strong> <code>POST /api/posts.php</code
                ><br />
                <strong>🔐 Authentication:</strong> Bearer Token Required<br />
                <strong>📝 Purpose:</strong> Create new blog posts via API
            </div>

            <!-- API Configuration -->
            <div class="form-section">
                <h3>🔧 API Configuration</h3>

                <div class="form-group">
                    <label for="api_url">API Endpoint URL</label>
                    <input
                        type="url"
                        id="api_url"
                        value="/api/posts.php"
                        placeholder="https://yoursite.com/api/posts.php"
                    />
                    <div class="help-text">
                        The full URL to your API endpoint
                    </div>
                </div>

                <div class="form-group">
                    <label for="api_token">API Token</label>
                    <input
                        type="text"
                        id="api_token"
                        placeholder="Enter your API token from Profile page"
                    />
                    <div class="help-text">
                        Get your token from the Admin → Profile page
                    </div>
                    <div class="quick-fill">
                        <button
                            onclick="document.getElementById('api_token').value = 'PASTE_YOUR_TOKEN_HERE'"
                        >
                            Example Token
                        </button>
                        <button
                            onclick="document.getElementById('api_token').select()"
                        >
                            Select Token
                        </button>
                    </div>
                </div>
            </div>

            <!-- Post Data -->
            <div class="form-section">
                <h3>📝 Post Data</h3>

                <div class="form-group">
                    <label for="post_title">Post Title</label>
                    <input
                        type="text"
                        id="post_title"
                        placeholder="Enter your post title"
                    />
                    <div class="quick-fill">
                        <button onclick="fillSample('tech')">
                            Tech Sample
                        </button>
                        <button onclick="fillSample('blog')">
                            Blog Sample
                        </button>
                        <button onclick="fillSample('news')">
                            News Sample
                        </button>
                        <button onclick="clearForm()">Clear All</button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="post_content">Post Content</label>
                    <textarea
                        id="post_content"
                        placeholder="Enter your post content (supports Markdown)"
                    ></textarea>
                    <div class="help-text">
                        Supports Markdown formatting: **bold**, *italic*, etc.
                    </div>
                </div>

                <div class="form-group">
                    <label for="post_description">Post Description *</label>
                    <textarea
                        id="post_description"
                        placeholder="Brief description for SEO and social media (max 500 chars)"
                        maxlength="500"
                        style="min-height: 80px"
                    ></textarea>
                    <div class="help-text">
                        <span id="description_counter">0/500 characters</span>
                        <span
                            >- Used for SEO meta description and social media
                            previews</span
                        >
                    </div>
                </div>

                <div class="form-group">
                    <label for="post_thumbnail">Thumbnail URL *</label>
                    <input
                        type="url"
                        id="post_thumbnail"
                        placeholder="https://example.com/thumbnail.jpg"
                    />
                    <div class="help-text">
                        Direct URL to the thumbnail image (required for social
                        media sharing)
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div style="text-align: center; margin: 2rem 0">
                <button id="send_button" class="button" onclick="sendPost()">
                    🚀 Send API Request
                </button>
                <button class="button button-warning" onclick="validateForm()">
                    ✅ Validate Form
                </button>
            </div>

            <!-- Response Section -->
            <div class="response-section">
                <h3>📡 API Response</h3>
                <div id="status_info" class="status-info" style="display: none">
                    <span
                        >Status:
                        <span id="status_code" class="status-code"
                            >-</span
                        ></span
                    >
                    <span>Time: <span id="response_time">-</span>ms</span>
                </div>
                <div id="response">Ready to send API request...</div>
            </div>
        </div>

        <script>
            // Sample data for quick testing
            const samples = {
                tech: {
                    title: "THISISATEST Technology Breakthrough Sample",
                    content:
                        "# Technology News\n\nToday we're excited to announce a **major breakthrough** in our technology stack.\n\n## Key Features\n\n- Improved performance\n- Better security\n- Enhanced user experience\n\n*This post was created via API!*",
                    description:
                        "Major breakthrough in our technology stack with improved performance, better security, and enhanced user experience.",
                    thumbnail:
                        "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop",
                },
                blog: {
                    title: "THISISATEST Daily Thoughts Sample",
                    content:
                        "## Today's Reflection\n\nJust wanted to share some thoughts about the day.\n\n### What I learned:\n\n1. **API testing** is actually quite fun\n2. *REST endpoints* are powerful tools\n3. Automation saves time\n\n> This is a quote about learning and growth.\n\nThanks for reading! 🙂",
                    description:
                        "Daily reflection on learning and growth, including thoughts on API testing and REST endpoints.",
                    thumbnail:
                        "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop",
                },
                news: {
                    title: "THISISATEST Breaking API Integration Sample",
                    content:
                        '**BREAKING NEWS:** Our REST API integration is now **complete** and ready for production use!\n\n### What this means:\n\n- External applications can now create posts\n- Automated content publishing is possible\n- Integration with third-party tools is seamless\n\n#### Technical Details:\n\n```json\n{\n  "endpoint": "/api/posts.php",\n  "method": "POST",\n  "auth": "Bearer token"\n}\n```\n\n*Posted automatically via API* ⚡',
                    description:
                        "Breaking news: REST API integration is complete and ready for production use with external applications.",
                    thumbnail:
                        "https://images.unsplash.com/photo-1504384308090-c894fdcc538d?w=800&h=600&fit=crop",
                },
            };

            function fillSample(type) {
                const sample = samples[type];
                if (sample) {
                    document.getElementById("post_title").value = sample.title;
                    document.getElementById("post_content").value =
                        sample.content;
                    document.getElementById("post_description").value =
                        sample.description;
                    document.getElementById("post_thumbnail").value =
                        sample.thumbnail;
                    updateDescriptionCounter();
                }
            }

            function clearForm() {
                document.getElementById("post_title").value = "";
                document.getElementById("post_content").value = "";
                document.getElementById("post_description").value = "";
                document.getElementById("post_thumbnail").value = "";
                updateDescriptionCounter();
            }

            function validateForm() {
                const url = document.getElementById("api_url").value.trim();
                const token = document.getElementById("api_token").value.trim();
                const title = document
                    .getElementById("post_title")
                    .value.trim();
                const content = document
                    .getElementById("post_content")
                    .value.trim();
                const description = document
                    .getElementById("post_description")
                    .value.trim();
                const thumbnail = document
                    .getElementById("post_thumbnail")
                    .value.trim();

                let errors = [];

                if (!url) errors.push("API URL is required");
                if (!token) errors.push("API Token is required");
                if (!title) errors.push("Post Title is required");
                if (!content) errors.push("Post Content is required");
                if (!description) errors.push("Post Description is required");
                if (!thumbnail) errors.push("Thumbnail URL is required");

                // Validate thumbnail URL format
                if (thumbnail && !isValidUrl(thumbnail)) {
                    errors.push("Thumbnail URL must be a valid URL");
                }

                // Validate description length
                if (description && description.length > 500) {
                    errors.push("Description must be 500 characters or less");
                }

                const responseDiv = document.getElementById("response");

                if (errors.length > 0) {
                    responseDiv.className = "error";
                    responseDiv.textContent =
                        "❌ Validation Errors:\n\n" + errors.join("\n");
                } else {
                    responseDiv.className = "success";
                    responseDiv.textContent =
                        "✅ Form validation passed!\n\nAll required fields are filled. Ready to send API request.";
                }
            }

            async function sendPost() {
                console.log("[API Test] Starting request...");

                const url = document.getElementById("api_url").value.trim();
                const token = document.getElementById("api_token").value.trim();
                const title = document
                    .getElementById("post_title")
                    .value.trim();
                const content = document
                    .getElementById("post_content")
                    .value.trim();
                const description = document
                    .getElementById("post_description")
                    .value.trim();
                const thumbnail = document
                    .getElementById("post_thumbnail")
                    .value.trim();

                const responseDiv = document.getElementById("response");
                const statusInfo = document.getElementById("status_info");
                const statusCode = document.getElementById("status_code");
                const responseTime = document.getElementById("response_time");
                const sendButton = document.getElementById("send_button");

                // Reset UI
                responseDiv.className = "";
                statusInfo.style.display = "none";

                // Validate required fields
                if (
                    !url ||
                    !token ||
                    !title ||
                    !content ||
                    !description ||
                    !thumbnail
                ) {
                    responseDiv.className = "error";
                    responseDiv.textContent =
                        "❌ Please fill in all required fields:\n\n- API Endpoint URL\n- API Token\n- Post Title\n- Post Content\n- Post Description\n- Thumbnail URL";
                    return;
                }

                // Validate thumbnail URL format
                if (!isValidUrl(thumbnail)) {
                    responseDiv.className = "error";
                    responseDiv.textContent =
                        "❌ Thumbnail URL must be a valid URL starting with http:// or https://";
                    return;
                }

                // Validate description length
                if (description.length > 500) {
                    responseDiv.className = "error";
                    responseDiv.textContent =
                        "❌ Description must be 500 characters or less (currently " +
                        description.length +
                        " characters)";
                    return;
                }

                // Set loading state
                responseDiv.className = "loading";
                responseDiv.textContent =
                    "⏳ Sending API request...\n\nPlease wait while we process your request.";
                sendButton.disabled = true;
                sendButton.textContent = "⏳ Sending...";

                const startTime = Date.now();

                try {
                    console.log("[API Test] Making request to:", url);
                    console.log(
                        "[API Test] With token:",
                        token.substring(0, 10) + "...",
                    );

                    const response = await fetch(url, {
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                            Authorization: `Bearer ${token}`,
                        },
                        body: JSON.stringify({
                            title: title,
                            content: content,
                            description: description,
                            thumbnail_url: thumbnail,
                        }),
                    });

                    const endTime = Date.now();
                    const duration = endTime - startTime;

                    console.log("[API Test] Response status:", response.status);
                    console.log("[API Test] Response time:", duration + "ms");

                    const data = await response.json();

                    // Show status info
                    statusInfo.style.display = "flex";
                    statusCode.textContent = response.status;
                    statusCode.className = `status-code status-${response.status}`;
                    responseTime.textContent = duration;

                    // Format response
                    const formattedResponse = JSON.stringify(data, null, 2);

                    if (response.ok) {
                        responseDiv.className = "success";
                        responseDiv.textContent = `✅ Success! (${response.status})\n\n${formattedResponse}`;
                        console.log("[API Test] Success:", data);
                    } else {
                        responseDiv.className = "error";
                        responseDiv.textContent = `❌ Error ${response.status}\n\n${formattedResponse}`;
                        console.log("[API Test] Error:", data);
                    }
                } catch (error) {
                    const endTime = Date.now();
                    const duration = endTime - startTime;

                    console.error("[API Test] Request failed:", error);

                    statusInfo.style.display = "flex";
                    statusCode.textContent = "ERR";
                    statusCode.className = "status-code status-500";
                    responseTime.textContent = duration;

                    responseDiv.className = "error";
                    responseDiv.textContent = `❌ Network Error\n\nFailed to connect to the API:\n${error.message}\n\nPossible causes:\n- Invalid URL\n- Server is down\n- CORS issues\n- Network connectivity problems`;
                } finally {
                    // Reset button state
                    sendButton.disabled = false;
                    sendButton.textContent = "🚀 Send API Request";
                }
            }

            // Helper function to validate URL
            function isValidUrl(string) {
                try {
                    new URL(string);
                    return true;
                } catch (_) {
                    return false;
                }
            }

            // Update description character counter
            function updateDescriptionCounter() {
                const description =
                    document.getElementById("post_description").value;
                const counter = document.getElementById("description_counter");
                const length = description.length;
                counter.textContent = length + "/500 characters";

                if (length > 450) {
                    counter.style.color = "#e74c3c";
                } else if (length > 350) {
                    counter.style.color = "#f39c12";
                } else {
                    counter.style.color = "#27ae60";
                }
            }

            // Auto-focus on first empty field
            window.addEventListener("load", function () {
                const fields = [
                    "api_url",
                    "api_token",
                    "post_title",
                    "post_content",
                    "post_description",
                    "post_thumbnail",
                ];
                for (let fieldId of fields) {
                    const field = document.getElementById(fieldId);
                    if (!field.value.trim()) {
                        field.focus();
                        break;
                    }
                }

                // Add event listener for description counter
                document
                    .getElementById("post_description")
                    .addEventListener("input", updateDescriptionCounter);
            });

            // Add keyboard shortcuts
            document.addEventListener("keydown", function (e) {
                // Ctrl/Cmd + Enter to send request
                if ((e.ctrlKey || e.metaKey) && e.key === "Enter") {
                    e.preventDefault();
                    sendPost();
                }

                // Escape to clear response
                if (e.key === "Escape") {
                    document.getElementById("response").textContent =
                        "Ready to send API request...";
                    document.getElementById("response").className = "";
                    document.getElementById("status_info").style.display =
                        "none";
                }
            });

            console.log("[API Test] Interface loaded successfully");
        </script>
    </body>
</html>
