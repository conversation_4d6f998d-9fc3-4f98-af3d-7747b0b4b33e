#!/usr/bin/env php
<?php
/**
 * Command Line Test Post Cleanup Script
 *
 * This script removes all test posts that start with "THISISATEST" from the database.
 * It can be run from the command line to quickly clean up test data.
 *
 * Usage: php cleanup-test-posts.php
 */

// Set error reporting
error_reporting(E_ALL);
ini_set("display_errors", 1);

// Ensure this is running from command line
if (php_sapi_name() !== "cli") {
    echo "This script must be run from the command line.\n";
    exit(1);
}

echo "🧹 Test Post Cleanup Script\n";
echo "==========================\n\n";

try {
    // Connect to database
    $dbPath = __DIR__ . "/database/cms.db";
    if (!file_exists($dbPath)) {
        throw new Exception("Database file not found: " . $dbPath);
    }

    $pdo = new PDO("sqlite:" . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "📊 Scanning for test posts...\n";

    // Enhanced safety check: Only THISISATEST prefix allowed
    $testPattern = "THISISATEST%";

    // Verify pattern is safe
    if (!str_starts_with($testPattern, "THISISATEST")) {
        throw new Exception(
            "SAFETY ERROR: Only THISISATEST patterns allowed. Unsafe pattern: $testPattern"
        );
    }

    // Find all test posts that start with "THISISATEST" with double safety check
    $selectQuery =
        "SELECT id, title, created_at FROM posts WHERE title LIKE ? AND title LIKE 'THISISATEST%' ORDER BY id";
    $stmt = $pdo->prepare($selectQuery);
    $stmt->execute([$testPattern]);
    $testPosts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($testPosts)) {
        echo "✅ No test posts found. Database is clean!\n";
        exit(0);
    }

    echo "🔍 Found " . count($testPosts) . " test posts to clean up:\n\n";

    // Display found test posts
    foreach ($testPosts as $post) {
        echo "   📝 ID {$post["id"]}: {$post["title"]}\n";
        echo "      Created: {$post["created_at"]}\n\n";
    }

    // Ask for confirmation
    echo "⚠️  Are you sure you want to delete these " .
        count($testPosts) .
        " test posts? (y/N): ";
    $handle = fopen("php://stdin", "r");
    $confirmation = trim(fgets($handle));
    fclose($handle);

    if (
        strtolower($confirmation) !== "y" &&
        strtolower($confirmation) !== "yes"
    ) {
        echo "❌ Cleanup cancelled.\n";
        exit(0);
    }

    echo "\n🗑️  Deleting test posts...\n";

    // Delete test posts
    $pdo->beginTransaction();
    $deletedCount = 0;

    foreach ($testPosts as $post) {
        // Double safety check: verify post title starts with THISISATEST
        if (!str_starts_with($post["title"], "THISISATEST")) {
            $pdo->rollback();
            throw new Exception(
                "SAFETY ERROR: Attempted to delete non-test post: ID {$post["id"]} - {$post["title"]}"
            );
        }

        // Enhanced delete with both ID and pattern verification
        $deleteStmt = $pdo->prepare(
            'DELETE FROM posts WHERE id = ? AND title LIKE "THISISATEST%"'
        );
        if ($deleteStmt->execute([$post["id"]])) {
            $deletedCount++;
            echo "   ✅ Deleted ID {$post["id"]}: " .
                substr($post["title"], 0, 50) .
                "...\n";
        } else {
            echo "   ❌ Failed to delete ID {$post["id"]}\n";
        }
    }

    $pdo->commit();

    // Enhanced logging with detailed information
    $logMessage =
        date("Y-m-d H:i:s") .
        " - CLI Cleanup: Deleted $deletedCount test posts";
    if ($deletedCount > 0) {
        $logMessage .= " - Posts: ";
        $postSummaries = array_map(function ($post) {
            return "ID{$post["id"]}(" . substr($post["title"], 0, 30) . ")";
        }, $testPosts);
        $logMessage .= implode(
            ", ",
            array_slice($postSummaries, 0, $deletedCount)
        );
    }
    $logMessage .= "\n";
    $logPath = __DIR__ . "/logs/test-cleanup.log";

    // Create logs directory if it doesn't exist
    $logsDir = dirname($logPath);
    if (!is_dir($logsDir)) {
        mkdir($logsDir, 0755, true);
    }

    file_put_contents($logPath, $logMessage, FILE_APPEND | LOCK_EX);

    echo "\n🎉 Cleanup completed successfully!\n";
    echo "📊 Deleted $deletedCount test posts.\n";
    echo "📝 Activity logged to: logs/test-cleanup.log\n\n";

    // Enhanced verification with safety check
    $verifyStmt = $pdo->prepare(
        "SELECT COUNT(*) FROM posts WHERE title LIKE 'THISISATEST%'"
    );
    $verifyStmt->execute();
    $remainingCount = $verifyStmt->fetchColumn();

    if ($remainingCount == 0) {
        echo "✅ Verification: Database is now clean of test posts.\n";
    } else {
        echo "⚠️  Warning: $remainingCount test posts still remain.\n";

        // Show remaining test posts for manual review
        $remainingStmt = $pdo->prepare(
            "SELECT id, title FROM posts WHERE title LIKE 'THISISATEST%' LIMIT 5"
        );
        $remainingStmt->execute();
        $remaining = $remainingStmt->fetchAll(PDO::FETCH_ASSOC);

        echo "   Remaining test posts:\n";
        foreach ($remaining as $post) {
            echo "   - ID {$post["id"]}: {$post["title"]}\n";
        }
    }
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    error_log("CLI Test Cleanup Error: " . $e->getMessage());
    exit(1);
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    error_log("CLI Test Cleanup Error: " . $e->getMessage());
    exit(1);
}

echo "\nDone! 👋\n";

?>
