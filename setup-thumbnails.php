<?php
/**
 * Thumbnail Setup Script
 * Run this script to add thumbnail functionality to your CMS
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

define('CMS_INIT', true);
require_once __DIR__ . '/src/includes/init.php';

// Default thumbnail URL for existing posts
$defaultThumbnail = "https://d1csarkz8obe9u.cloudfront.net/posterpreviews/youtube-thumbnail-template-design-5fa9cd79a6367f8446a5208e4540a493_screen.jpg?ts=1699135222";

try {
    $db = Database::getInstance();
    $pdo = $db->getPdo();
    
    echo "<!DOCTYPE html>\n<html><head><title>Thumbnail Setup</title>";
    echo "<style>";
    echo "body { font-family: Arial, sans-serif; max-width: 900px; margin: 50px auto; padding: 20px; line-height: 1.6; }";
    echo ".success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }";
    echo ".info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 10px 0; }";
    echo ".warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 10px 0; }";
    echo "table { width: 100%; border-collapse: collapse; margin: 20px 0; }";
    echo "th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }";
    echo "th { background: #f8f9fa; font-weight: bold; }";
    echo "img { max-width: 80px; height: auto; border: 1px solid #ddd; border-radius: 4px; }";
    echo ".btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }";
    echo ".btn:hover { background: #0056b3; }";
    echo "</style>";
    echo "</head><body>\n";
    
    echo "<h1>🖼️ CMS Thumbnail Setup</h1>\n";
    echo "<p>This script will add thumbnail functionality to your CMS and update existing posts with a default thumbnail.</p>\n";
    
    // Check if already set up
    $columns = $pdo->query("PRAGMA table_info(posts)")->fetchAll();
    $hasThumbnailColumn = false;
    $hasCategoryColumn = false;
    $hasSlugColumn = false;
    
    foreach ($columns as $column) {
        if ($column['name'] === 'thumbnail_url') $hasThumbnailColumn = true;
        if ($column['name'] === 'category') $hasCategoryColumn = true;
        if ($column['name'] === 'slug') $hasSlugColumn = true;
    }
    
    $needsUpdate = !$hasThumbnailColumn || !$hasCategoryColumn || !$hasSlugColumn;
    
    if (!$needsUpdate) {
        echo "<div class='success'>✅ Thumbnail functionality is already set up!</div>\n";
    } else {
        echo "<div class='info'>📋 Setting up thumbnail functionality...</div>\n";
        
        // Begin transaction
        $pdo->beginTransaction();
        
        try {
            $updates = [];
            
            // Add missing columns
            if (!$hasThumbnailColumn) {
                $pdo->exec("ALTER TABLE posts ADD COLUMN thumbnail_url TEXT DEFAULT NULL");
                $updates[] = "Added thumbnail_url column";
            }
            
            if (!$hasCategoryColumn) {
                $pdo->exec("ALTER TABLE posts ADD COLUMN category VARCHAR(50) DEFAULT 'tech'");
                $updates[] = "Added category column";
            }
            
            if (!$hasSlugColumn) {
                $pdo->exec("ALTER TABLE posts ADD COLUMN slug VARCHAR(255) DEFAULT NULL");
                $updates[] = "Added slug column";
            }
            
            // Update existing posts with default values
            $postCount = $pdo->query("SELECT COUNT(*) as count FROM posts")->fetch()['count'];
            
            if ($postCount > 0) {
                // Update posts without thumbnails
                if (!$hasThumbnailColumn) {
                    $stmt = $pdo->prepare("UPDATE posts SET thumbnail_url = ? WHERE thumbnail_url IS NULL OR thumbnail_url = ''");
                    $stmt->execute([$defaultThumbnail]);
                    $thumbnailUpdates = $stmt->rowCount();
                    $updates[] = "Set default thumbnail for {$thumbnailUpdates} posts";
                }
                
                // Update posts without categories
                if (!$hasCategoryColumn) {
                    $stmt = $pdo->prepare("UPDATE posts SET category = 'tech' WHERE category IS NULL OR category = ''");
                    $stmt->execute();
                    $categoryUpdates = $stmt->rowCount();
                    $updates[] = "Set default category for {$categoryUpdates} posts";
                }
                
                // Generate slugs for posts without them
                if (!$hasSlugColumn) {
                    $posts = $pdo->query("SELECT id, title FROM posts WHERE slug IS NULL OR slug = ''")->fetchAll();
                    foreach ($posts as $post) {
                        $slug = generateUniqueSlug($post['title'], $post['id']);
                        $stmt = $pdo->prepare("UPDATE posts SET slug = ? WHERE id = ?");
                        $stmt->execute([$slug, $post['id']]);
                    }
                    $updates[] = "Generated slugs for " . count($posts) . " posts";
                }
            }
            
            // Commit transaction
            $pdo->commit();
            
            echo "<div class='success'>";
            echo "<h3>✅ Setup completed successfully!</h3>";
            echo "<ul>";
            foreach ($updates as $update) {
                echo "<li>{$update}</li>";
            }
            echo "</ul>";
            echo "</div>";
            
        } catch (Exception $e) {
            $pdo->rollback();
            throw $e;
        }
    }
    
    // Display current posts
    $posts = $pdo->query("
        SELECT p.id, p.title, p.category, p.thumbnail_url, p.slug, p.created_at, u.username as author
        FROM posts p 
        LEFT JOIN users u ON p.author_id = u.id 
        ORDER BY p.created_at DESC 
        LIMIT 10
    ")->fetchAll();
    
    if (!empty($posts)) {
        echo "<h2>📝 Your Posts (Latest 10)</h2>\n";
        echo "<table>\n";
        echo "<tr><th>Thumbnail</th><th>Title</th><th>Category</th><th>Author</th><th>Created</th><th>Actions</th></tr>\n";
        
        foreach ($posts as $post) {
            echo "<tr>\n";
            echo "<td>\n";
            if ($post['thumbnail_url']) {
                echo "<img src='" . htmlspecialchars($post['thumbnail_url']) . "' alt='Thumbnail'>";
            } else {
                echo "<div style='width: 80px; height: 60px; background: #f8f9fa; border: 1px solid #dee2e6; display: flex; align-items: center; justify-content: center; font-size: 11px; color: #6c757d;'>No Image</div>";
            }
            echo "</td>\n";
            echo "<td><strong>" . htmlspecialchars($post['title']) . "</strong></td>\n";
            echo "<td><span style='background: #e3f2fd; color: #1565c0; padding: 2px 8px; border-radius: 12px; font-size: 12px;'>" . htmlspecialchars(ucfirst($post['category'] ?? 'tech')) . "</span></td>\n";
            echo "<td>" . htmlspecialchars($post['author']) . "</td>\n";
            echo "<td>" . date('M j, Y', strtotime($post['created_at'])) . "</td>\n";
            echo "<td><a href='admin/posts.php?action=edit&id={$post['id']}' class='btn' style='font-size: 12px; padding: 5px 10px;'>Edit</a></td>\n";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
    }
    
    echo "<h2>🎯 What's New</h2>\n";
    echo "<div class='info'>";
    echo "<h3>Thumbnail Features Added:</h3>";
    echo "<ul>";
    echo "<li><strong>Thumbnail URLs:</strong> Add image URLs to make your posts more visual</li>";
    echo "<li><strong>Post Categories:</strong> Organize posts into tech, gaming, film, or serie categories</li>";
    echo "<li><strong>SEO-Friendly URLs:</strong> Clean URLs like /tech/my-post-title/</li>";
    echo "<li><strong>Responsive Design:</strong> Thumbnails look great on all devices</li>";
    echo "<li><strong>Admin Interface:</strong> Easy thumbnail management in the admin panel</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🔧 How to Use</h2>\n";
    echo "<div class='info'>";
    echo "<h3>Adding Thumbnails to Posts:</h3>";
    echo "<ol>";
    echo "<li>Go to the <a href='admin/posts.php'>Posts Management</a> page</li>";
    echo "<li>Create a new post or edit an existing one</li>";
    echo "<li>In the 'Thumbnail URL' field, paste the URL of an image</li>";
    echo "<li>Save your post and view it on the <a href='index.php'>homepage</a></li>";
    echo "</ol>";
    echo "<p><strong>Tip:</strong> You can use images from any public URL, or upload images to a service like Imgur or use your own hosting.</p>";
    echo "</div>";
    
    echo "<h2>🔗 Quick Actions</h2>\n";
    echo "<p>";
    echo "<a href='admin/posts.php' class='btn'>📝 Manage Posts</a>";
    echo "<a href='admin/posts.php?action=new' class='btn'>➕ Create New Post</a>";
    echo "<a href='index.php' class='btn'>🌐 View Website</a>";
    echo "<a href='admin/' class='btn'>🏠 Admin Dashboard</a>";
    echo "</p>";
    
    echo "<h2>💡 Default Thumbnail</h2>\n";
    echo "<div class='warning'>";
    echo "<p><strong>All existing posts now have this default thumbnail:</strong></p>";
    echo "<img src='{$defaultThumbnail}' alt='Default thumbnail' style='max-width: 200px;'>";
    echo "<p><small>{$defaultThumbnail}</small></p>";
    echo "<p>You can change this by editing each post individually in the admin panel.</p>";
    echo "</div>";
    
    // Show sample thumbnail URLs
    echo "<h2>🖼️ Sample Thumbnail URLs</h2>\n";
    echo "<div class='info'>";
    echo "<p>Here are some free placeholder image services you can use:</p>";
    echo "<ul>";
    echo "<li><strong>Picsum:</strong> https://picsum.photos/800/600 (random images)</li>";
    echo "<li><strong>Placeholder.com:</strong> https://via.placeholder.com/800x600/0066cc/ffffff?text=Your+Title</li>";
    echo "<li><strong>Unsplash:</strong> https://source.unsplash.com/800x600/?technology (category-based)</li>";
    echo "<li><strong>Lorem Picsum:</strong> https://picsum.photos/id/1/800/600 (specific image)</li>";
    echo "</ul>";
    echo "<p><strong>For production:</strong> Upload your images to a reliable hosting service or your own server.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-weight: bold; padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "❌ Setup failed: " . htmlspecialchars($e->getMessage());
    echo "</div>";
    echo "<p>Please check your database permissions and try again.</p>";
}

echo "</body></html>\n";
?>