{"name": "pixels-cms-description-field-tests", "version": "1.0.0", "description": "End-to-end tests for the description field feature in Pixels CMS", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:report": "playwright show-report", "install-browsers": "playwright install"}, "keywords": ["cms", "blog", "php", "playwright", "e2e-testing", "seo", "description-field"], "author": "Developer", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0"}, "engines": {"node": ">=16.0.0"}}