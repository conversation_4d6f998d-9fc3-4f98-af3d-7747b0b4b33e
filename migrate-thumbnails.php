<?php
/**
 * Database Migration Script - Add Thumbnail URLs
 * Adds thumbnail_url column to posts table and sets default thumbnail for existing posts
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

define('CMS_INIT', true);
require_once __DIR__ . '/src/includes/init.php';

// Default thumbnail URL for existing posts
$defaultThumbnail = "https://d1csarkz8obe9u.cloudfront.net/posterpreviews/youtube-thumbnail-template-design-5fa9cd79a6367f8446a5208e4540a493_screen.jpg?ts=1699135222";

try {
    $db = Database::getInstance();
    $pdo = $db->getPdo();
    
    echo "<!DOCTYPE html>\n<html><head><title>Thumbnail Migration</title>";
    echo "<style>body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }</style>";
    echo "</head><body>\n";
    echo "<h1>Thumbnail URL Migration</h1>\n";
    
    // Check if thumbnail_url column already exists
    $columns = $pdo->query("PRAGMA table_info(posts)")->fetchAll();
    $hasThumbnailColumn = false;
    
    foreach ($columns as $column) {
        if ($column['name'] === 'thumbnail_url') {
            $hasThumbnailColumn = true;
            break;
        }
    }
    
    if ($hasThumbnailColumn) {
        echo "<p style='color: orange;'>⚠️ Migration already completed - thumbnail_url column already exists!</p>\n";
    } else {
        echo "<p>📊 Starting migration...</p>\n";
        
        // Begin transaction
        $pdo->beginTransaction();
        
        try {
            // Add thumbnail_url column to posts table
            $pdo->exec("ALTER TABLE posts ADD COLUMN thumbnail_url TEXT DEFAULT NULL");
            echo "<p style='color: green;'>✅ Added thumbnail_url column to posts table</p>\n";
            
            // Get count of existing posts
            $postCount = $pdo->query("SELECT COUNT(*) as count FROM posts")->fetch()['count'];
            echo "<p>📝 Found {$postCount} existing posts</p>\n";
            
            if ($postCount > 0) {
                // Update all existing posts with default thumbnail
                $stmt = $pdo->prepare("UPDATE posts SET thumbnail_url = ? WHERE thumbnail_url IS NULL");
                $stmt->execute([$defaultThumbnail]);
                
                $updatedCount = $stmt->rowCount();
                echo "<p style='color: green;'>✅ Updated {$updatedCount} posts with default thumbnail</p>\n";
            }
            
            // Commit transaction
            $pdo->commit();
            echo "<p style='color: green; font-weight: bold;'>🎉 Migration completed successfully!</p>\n";
            
        } catch (Exception $e) {
            // Rollback on error
            $pdo->rollback();
            throw $e;
        }
    }
    
    // Display current posts with thumbnails
    $posts = $pdo->query("SELECT id, title, thumbnail_url, created_at FROM posts ORDER BY created_at DESC")->fetchAll();
    
    if (!empty($posts)) {
        echo "<h2>Posts with Thumbnails</h2>\n";
        echo "<table border='1' cellpadding='10' cellspacing='0' style='width: 100%; border-collapse: collapse;'>\n";
        echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Title</th><th>Thumbnail</th><th>Created</th></tr>\n";
        
        foreach ($posts as $post) {
            echo "<tr>\n";
            echo "<td>{$post['id']}</td>\n";
            echo "<td>" . htmlspecialchars($post['title']) . "</td>\n";
            echo "<td>\n";
            if ($post['thumbnail_url']) {
                echo "<img src='" . htmlspecialchars($post['thumbnail_url']) . "' style='max-width: 100px; height: auto;' alt='Thumbnail'><br>\n";
                echo "<small>" . htmlspecialchars($post['thumbnail_url']) . "</small>\n";
            } else {
                echo "<em>No thumbnail</em>\n";
            }
            echo "</td>\n";
            echo "<td>" . date('Y-m-d H:i', strtotime($post['created_at'])) . "</td>\n";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
    }
    
    echo "<h2>Next Steps</h2>\n";
    echo "<ol>\n";
    echo "<li>The admin interface will now show a thumbnail URL field when creating/editing posts</li>\n";
    echo "<li>The frontend will display thumbnails for all posts</li>\n";
    echo "<li>You can edit existing posts to change their thumbnail URLs</li>\n";
    echo "<li>All existing posts now have the default YouTube thumbnail</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='admin/posts.php'>→ Go to Posts Management</a></p>\n";
    echo "<p><a href='index.php'>→ View Frontend</a></p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ Migration failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Please check the error logs and try again.</p>\n";
}

echo "</body></html>\n";
?>