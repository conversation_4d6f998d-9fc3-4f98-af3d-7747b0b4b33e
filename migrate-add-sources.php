<?php
/**
 * Database Migration: Add Sources Column
 * Adds sources field to posts table for storing article source URLs
 */

if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once "src/includes/init.php";
}

echo "Article Sources Migration Script\n";
echo "================================\n\n";

try {
    // Connect to database
    $db = Database::getInstance();
    $pdo = $db->getPdo();

    // Check if sources column already exists
    $checkQuery = "PRAGMA table_info(posts)";
    $result = $pdo->query($checkQuery);
    $columns = $result->fetchAll(PDO::FETCH_ASSOC);

    $sourcesColumnExists = false;
    foreach ($columns as $column) {
        if ($column["name"] === "sources") {
            $sourcesColumnExists = true;
            break;
        }
    }

    if ($sourcesColumnExists) {
        echo "⚠️  Sources column already exists in posts table.\n";
        echo "No migration needed.\n\n";
    } else {
        echo "Adding sources column to posts table...\n";

        // Add sources column
        $alterQuery = "ALTER TABLE posts ADD COLUMN sources TEXT DEFAULT NULL";
        $pdo->exec($alterQuery);

        echo "✅ Sources column added successfully!\n\n";
    }

    // Verify the migration
    echo "Verifying migration...\n";
    $verifyQuery = "PRAGMA table_info(posts)";
    $result = $pdo->query($verifyQuery);
    $columns = $result->fetchAll(PDO::FETCH_ASSOC);

    echo "Current posts table structure:\n";
    echo "┌─────────────────┬──────────────┬─────────┬─────────┬─────────────┐\n";
    echo "│ Column Name     │ Type         │ NotNull │ Default │ Primary Key │\n";
    echo "├─────────────────┼──────────────┼─────────┼─────────┼─────────────┤\n";

    foreach ($columns as $column) {
        printf(
            "│ %-15s │ %-12s │ %-7s │ %-7s │ %-11s │\n",
            $column["name"],
            $column["type"],
            $column["notnull"] ? "YES" : "NO",
            $column["dflt_value"] ?? "NULL",
            $column["pk"] ? "YES" : "NO"
        );
    }
    echo "└─────────────────┴──────────────┴─────────┴─────────┴─────────────┘\n\n";

    // Check if sources column is now present
    $sourcesFound = false;
    foreach ($columns as $column) {
        if ($column["name"] === "sources") {
            $sourcesFound = true;
            break;
        }
    }

    if ($sourcesFound) {
        echo "✅ Migration verification successful!\n";
        echo "✅ Sources column is now available in the posts table.\n\n";

        // Show example usage
        echo "Example usage in code:\n";
        echo "─────────────────────\n";
        echo "// Create post with sources\n";
        echo "\$sources = 'https://example.com, https://source2.com';\n";
        echo "\$postManager->createPost(\$title, \$content, \$isDraft, \$authorId, \$category, \$thumbnailUrl, \$description, \$sources);\n\n";

        echo "// Update post with sources\n";
        echo "\$postManager->updatePost(\$id, \$title, \$content, \$isDraft, \$category, \$thumbnailUrl, \$description, \$sources);\n\n";

        echo "Migration completed successfully!\n";
        echo "You can now add article sources to your posts.\n";
    } else {
        echo "❌ Migration verification failed!\n";
        echo "Sources column was not found after migration.\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "❌ Migration failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Migration completed at: " . date("Y-m-d H:i:s") . "\n";
?>
