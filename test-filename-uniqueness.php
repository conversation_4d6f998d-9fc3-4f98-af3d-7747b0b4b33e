<?php
/**
 * Filename Uniqueness Test
 * Tests the filename generation to ensure uniqueness and identify collision issues
 */

define('CMS_INIT', true);
require_once __DIR__ . '/src/includes/init.php';

// Clear any existing output
if (ob_get_level()) {
    ob_clean();
}

header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>\n<html><head><title>Filename Uniqueness Test</title>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
.container { max-width: 1000px; margin: 0 auto; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.success { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.info { color: blue; font-weight: bold; }
.box { margin: 15px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
.highlight { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; border-radius: 3px; }
button { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; color: white; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-danger { background: #dc3545; }
.btn-warning { background: #ffc107; color: black; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
.duplicate { background-color: #ffebee; }
.unique { background-color: #e8f5e8; }
</style></head><body>\n";

echo "<div class='container'>";
echo "<h1>🔧 Filename Uniqueness Test</h1>";
echo "<p>This test verifies that the filename generation always produces unique filenames.</p>";

$now = time();
$nowFormatted = date('Y-m-d H:i:s', $now);

// Handle test actions
$action = $_POST['action'] ?? '';
$message = '';

// Test the filename generation directly
function testFilenameGeneration($testName, $originalName, $extension, $count = 10) {
    echo "<h3>$testName</h3>";
    
    // Access the ImageUpload class's private method using reflection
    $imageUpload = new ImageUpload();
    $reflection = new ReflectionClass($imageUpload);
    $method = $reflection->getMethod('generateUniqueFilename');
    $method->setAccessible(true);
    
    $filenames = [];
    $duplicates = [];
    $errors = [];
    
    echo "<table>";
    echo "<tr><th>#</th><th>Generated Filename</th><th>Status</th><th>Timestamp</th></tr>";
    
    for ($i = 1; $i <= $count; $i++) {
        try {
            $start = microtime(true);
            $filename = $method->invoke($imageUpload, $originalName, $extension);
            $end = microtime(true);
            $duration = round(($end - $start) * 1000, 2);
            
            $status = "unique";
            $statusClass = "unique";
            
            if (in_array($filename, $filenames)) {
                $duplicates[] = $filename;
                $status = "DUPLICATE!";
                $statusClass = "duplicate";
            }
            
            $filenames[] = $filename;
            
            echo "<tr class='$statusClass'>";
            echo "<td>$i</td>";
            echo "<td>" . htmlspecialchars($filename) . "</td>";
            echo "<td>$status</td>";
            echo "<td>{$duration}ms</td>";
            echo "</tr>";
            
            // Small delay to test timestamp differences
            if ($i % 5 == 0) {
                usleep(1000); // 1ms delay every 5 iterations
            }
            
        } catch (Exception $e) {
            $errors[] = "Iteration $i: " . $e->getMessage();
            echo "<tr class='duplicate'>";
            echo "<td>$i</td>";
            echo "<td colspan='3'>ERROR: " . htmlspecialchars($e->getMessage()) . "</td>";
            echo "</tr>";
        }
    }
    
    echo "</table>";
    
    // Summary
    $uniqueCount = count(array_unique($filenames));
    $totalCount = count($filenames);
    $duplicateCount = $totalCount - $uniqueCount;
    
    echo "<div class='highlight " . ($duplicateCount > 0 ? 'error' : 'success') . "'>";
    echo "<strong>Results:</strong><br>";
    echo "• Total generated: $totalCount<br>";
    echo "• Unique filenames: $uniqueCount<br>";
    echo "• Duplicates: $duplicateCount<br>";
    
    if ($duplicateCount > 0) {
        echo "<br><strong>Duplicate filenames found:</strong><br>";
        foreach (array_unique($duplicates) as $duplicate) {
            $occurrences = array_count_values($filenames)[$duplicate];
            echo "• " . htmlspecialchars($duplicate) . " (appeared $occurrences times)<br>";
        }
    }
    
    if (!empty($errors)) {
        echo "<br><strong>Errors:</strong><br>";
        foreach ($errors as $error) {
            echo "• " . htmlspecialchars($error) . "<br>";
        }
    }
    
    echo "</div>";
    
    return $duplicateCount === 0;
}

// Test rapid generation to simulate the issue
function testRapidGeneration($originalName, $extension, $count = 100) {
    echo "<h3>Rapid Generation Test (Stress Test)</h3>";
    echo "<p>Generating $count filenames as fast as possible to test for collisions...</p>";
    
    $imageUpload = new ImageUpload();
    $reflection = new ReflectionClass($imageUpload);
    $method = $reflection->getMethod('generateUniqueFilename');
    $method->setAccessible(true);
    
    $filenames = [];
    $duplicates = [];
    $startTime = microtime(true);
    
    for ($i = 0; $i < $count; $i++) {
        try {
            $filename = $method->invoke($imageUpload, $originalName, $extension);
            
            if (in_array($filename, $filenames)) {
                $duplicates[] = $filename;
            }
            
            $filenames[] = $filename;
            
        } catch (Exception $e) {
            echo "<div class='highlight error'>Error at iteration $i: " . $e->getMessage() . "</div>";
            break;
        }
    }
    
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    $uniqueCount = count(array_unique($filenames));
    $totalCount = count($filenames);
    $duplicateCount = $totalCount - $uniqueCount;
    
    echo "<div class='highlight " . ($duplicateCount > 0 ? 'error' : 'success') . "'>";
    echo "<strong>Rapid Generation Results:</strong><br>";
    echo "• Generated $totalCount filenames in {$duration}ms<br>";
    echo "• Average: " . round($duration / $totalCount, 3) . "ms per filename<br>";
    echo "• Unique filenames: $uniqueCount<br>";
    echo "• Duplicates: $duplicateCount<br>";
    
    if ($duplicateCount > 0) {
        echo "<br><strong>❌ FAILURE: Duplicates found in rapid generation!</strong><br>";
        foreach (array_unique($duplicates) as $duplicate) {
            $occurrences = array_count_values($filenames)[$duplicate];
            echo "• " . htmlspecialchars($duplicate) . " (appeared $occurrences times)<br>";
        }
    } else {
        echo "<br><strong>✅ SUCCESS: All filenames were unique!</strong>";
    }
    
    echo "</div>";
    
    return $duplicateCount === 0;
}

// Simulate the old method for comparison
function testOldMethod($originalName, $extension, $count = 10) {
    echo "<h3>Old Method Simulation (Before Fix)</h3>";
    echo "<p>Simulating the old filename generation method to show the problem...</p>";
    
    $safeName = preg_replace('/[^a-zA-Z0-9_-]/', '', $originalName);
    $safeName = substr($safeName, 0, 50);
    if (empty($safeName)) {
        $safeName = 'image';
    }
    
    $filenames = [];
    $duplicates = [];
    
    echo "<table>";
    echo "<tr><th>#</th><th>Generated Filename (Old Method)</th><th>Status</th></tr>";
    
    for ($i = 1; $i <= $count; $i++) {
        $timestamp = time(); // Old method used time() not microtime()
        $random = bin2hex(random_bytes(4)); // Old method used 4 bytes
        
        $filename = $safeName . '_' . $timestamp . '_' . $random . '.' . $extension;
        
        $status = "unique";
        $statusClass = "unique";
        
        if (in_array($filename, $filenames)) {
            $duplicates[] = $filename;
            $status = "DUPLICATE!";
            $statusClass = "duplicate";
        }
        
        $filenames[] = $filename;
        
        echo "<tr class='$statusClass'>";
        echo "<td>$i</td>";
        echo "<td>" . htmlspecialchars($filename) . "</td>";
        echo "<td>$status</td>";
        echo "</tr>";
        
        // No delay - generate as fast as possible to show the issue
    }
    
    echo "</table>";
    
    $uniqueCount = count(array_unique($filenames));
    $totalCount = count($filenames);
    $duplicateCount = $totalCount - $uniqueCount;
    
    echo "<div class='highlight " . ($duplicateCount > 0 ? 'error' : 'success') . "'>";
    echo "<strong>Old Method Results:</strong><br>";
    echo "• Total generated: $totalCount<br>";
    echo "• Unique filenames: $uniqueCount<br>";
    echo "• Duplicates: $duplicateCount<br>";
    
    if ($duplicateCount > 0) {
        echo "<br><strong>❌ Problem demonstrated: Duplicates found with old method!</strong><br>";
    } else {
        echo "<br><strong>No duplicates in this small test (try rapid generation)</strong>";
    }
    
    echo "</div>";
}

switch ($action) {
    case 'test_basic':
        echo "<div class='box'>";
        echo "<h2>📊 Basic Filename Generation Tests</h2>";
        
        $allPassed = true;
        $allPassed &= testFilenameGeneration("Test 1: Normal Filename", "vacation-photo", "jpg", 20);
        $allPassed &= testFilenameGeneration("Test 2: Special Characters", "My Family Picture!@#$%", "png", 15);
        $allPassed &= testFilenameGeneration("Test 3: Empty Name", "", "gif", 10);
        $allPassed &= testFilenameGeneration("Test 4: Long Name", "This_is_a_very_long_filename_that_should_be_truncated_to_fifty_characters_maximum", "webp", 10);
        
        echo "<div class='highlight " . ($allPassed ? 'success' : 'error') . "'>";
        echo "<h3>" . ($allPassed ? "✅ All Basic Tests Passed!" : "❌ Some Tests Failed") . "</h3>";
        echo "</div>";
        
        echo "</div>";
        break;
        
    case 'test_rapid':
        echo "<div class='box'>";
        echo "<h2>⚡ Rapid Generation Test</h2>";
        
        $passed = testRapidGeneration("test-image", "jpg", 1000);
        
        echo "</div>";
        break;
        
    case 'test_old_method':
        echo "<div class='box'>";
        echo "<h2>🔍 Old Method Comparison</h2>";
        
        testOldMethod("test-image", "jpg", 50);
        
        echo "</div>";
        break;
        
    case 'test_all':
        echo "<div class='box'>";
        echo "<h2>🧪 Complete Test Suite</h2>";
        
        echo "<h3>1. Basic Tests</h3>";
        $basicPassed = testFilenameGeneration("Quick Basic Test", "image", "jpg", 10);
        
        echo "<h3>2. Rapid Generation Test</h3>";
        $rapidPassed = testRapidGeneration("image", "jpg", 500);
        
        echo "<h3>3. Old Method Demonstration</h3>";
        testOldMethod("image", "jpg", 20);
        
        $allPassed = $basicPassed && $rapidPassed;
        
        echo "<div class='highlight " . ($allPassed ? 'success' : 'error') . "'>";
        echo "<h3>🏆 Overall Results</h3>";
        echo "• Basic Tests: " . ($basicPassed ? "✅ PASSED" : "❌ FAILED") . "<br>";
        echo "• Rapid Tests: " . ($rapidPassed ? "✅ PASSED" : "❌ FAILED") . "<br>";
        echo "<br><strong>" . ($allPassed ? "✅ ALL TESTS PASSED - Filename uniqueness is working!" : "❌ TESTS FAILED - There are still uniqueness issues!") . "</strong>";
        echo "</div>";
        
        echo "</div>";
        break;
}

if (empty($action)) {
    echo "<div class='box'>";
    echo "<h2>📋 Test Overview</h2>";
    echo "<p>This tool tests the filename generation system to ensure all generated filenames are unique.</p>";
    
    echo "<h3>The Problem:</h3>";
    echo "<ul>";
    echo "<li>Original method used <code>time()</code> which only has 1-second precision</li>";
    echo "<li>Multiple uploads within the same second could get identical filenames</li>";
    echo "<li>No check for existing files on disk</li>";
    echo "<li>Could cause files to overwrite each other</li>";
    echo "</ul>";
    
    echo "<h3>The Fix:</h3>";
    echo "<ul>";
    echo "<li>Uses <code>microtime(true)</code> for microsecond precision</li>";
    echo "<li>Increased random bytes from 4 to 6 for better collision resistance</li>";
    echo "<li>Added file existence check to guarantee uniqueness</li>";
    echo "<li>Retry mechanism if collision still occurs</li>";
    echo "</ul>";
    
    echo "</div>";
}

// Test Actions
echo "<div class='box'>";
echo "<h2>🧪 Run Tests</h2>";
echo "<form method='post'>";

echo "<h3>Choose a Test:</h3>";
echo "<button type='submit' name='action' value='test_basic' class='btn-primary'>📊 Basic Tests (Different filenames)</button>";
echo "<button type='submit' name='action' value='test_rapid' class='btn-warning'>⚡ Rapid Generation (Stress test)</button>";
echo "<button type='submit' name='action' value='test_old_method' class='btn-danger'>🔍 Old Method Demo (Show problem)</button>";
echo "<button type='submit' name='action' value='test_all' class='btn-success'>🧪 Complete Test Suite</button>";

echo "<button type='button' onclick='window.location.reload()' class='btn-primary' style='background: #6c757d; margin-left: 20px;'>🔄 Refresh</button>";
echo "</form>";
echo "</div>";

// Code Comparison
echo "<div class='box'>";
echo "<h2>🔧 Code Comparison</h2>";

echo "<h3>❌ Old Method (Problematic):</h3>";
echo "<pre>";
echo "private function generateUniqueFilename(\$originalName, \$extension) {\n";
echo "    \$safeName = preg_replace('/[^a-zA-Z0-9_-]/', '', \$originalName);\n";
echo "    \$safeName = substr(\$safeName, 0, 50);\n";
echo "    \n";
echo "    if (empty(\$safeName)) {\n";
echo "        \$safeName = 'image';\n";
echo "    }\n";
echo "    \n";
echo "    \$timestamp = time(); // ❌ Only 1-second precision\n";
echo "    \$random = bin2hex(random_bytes(4)); // ❌ Only 8 hex chars\n";
echo "    \n";
echo "    return \$safeName . '_' . \$timestamp . '_' . \$random . '.' . \$extension;\n";
echo "    // ❌ No existence check!\n";
echo "}";
echo "</pre>";

echo "<h3>✅ New Method (Fixed):</h3>";
echo "<pre>";
echo "private function generateUniqueFilename(\$originalName, \$extension) {\n";
echo "    \$safeName = preg_replace('/[^a-zA-Z0-9_-]/', '', \$originalName);\n";
echo "    \$safeName = substr(\$safeName, 0, 50);\n";
echo "    \n";
echo "    if (empty(\$safeName)) {\n";
echo "        \$safeName = 'image';\n";
echo "    }\n";
echo "    \n";
echo "    \$maxAttempts = 100;\n";
echo "    \$attempts = 0;\n";
echo "    \n";
echo "    do {\n";
echo "        \$timestamp = microtime(true); // ✅ Microsecond precision\n";
echo "        \$timestampStr = sprintf('%.3f', \$timestamp);\n";
echo "        \$timestampStr = str_replace('.', '', \$timestampStr);\n";
echo "        \$random = bin2hex(random_bytes(6)); // ✅ 12 hex chars\n";
echo "        \n";
echo "        \$filename = \$safeName . '_' . \$timestampStr . '_' . \$random . '.' . \$extension;\n";
echo "        \$filepath = \$this->uploadDir . \$filename;\n";
echo "        \n";
echo "        \$attempts++;\n";
echo "        \n";
echo "    } while (file_exists(\$filepath) && \$attempts < \$maxAttempts); // ✅ Existence check!\n";
echo "    \n";
echo "    if (\$attempts >= \$maxAttempts) {\n";
echo "        throw new Exception('Unable to generate unique filename');\n";
echo "    }\n";
echo "    \n";
echo "    return \$filename;\n";
echo "}";
echo "</pre>";

echo "</div>";

echo "</div>"; // container

echo "<hr>";
echo "<p><small>Generated at $nowFormatted</small></p>";

echo "</body></html>";
?>