const { chromium } = require("@playwright/test");

async function testMediaPage() {
  console.log("[test-media-page] start - testing media management page");

  const browser = await chromium.launch({
    headless: false,
  });

  try {
    const page = await browser.newPage();

    // Enable console logging
    page.on("console", (msg) => {
      console.log(`[BROWSER] ${msg.text()}`);
    });

    page.on("pageerror", (error) => {
      console.error(`[PAGE ERROR] ${error.message}`);
    });

    // Login
    console.log("[test-media-page] Logging in...");
    await page.goto("http://localhost:8000/admin/login.php");
    await page.fill("#username", "admin");
    await page.fill("#password", "admin");
    await page.click('button[type="submit"]');

    // Wait for redirect after login
    await page.waitForURL(/admin\/index\.php/, { timeout: 5000 });
    console.log("[test-media-page] Login successful, redirected to dashboard");

    // Navigate to media page
    console.log("[test-media-page] Navigating to media page...");
    const response = await page.goto("http://localhost:8000/admin/media.php");
    console.log("[test-media-page] Response status:", response.status());

    // Check page content for debugging
    const pageContent = await page.content();
    console.log("[test-media-page] Page content length:", pageContent.length);
    console.log("[test-media-page] Page URL after navigation:", page.url());

    // Check if we were redirected
    if (page.url().includes("login.php")) {
      console.log("[test-media-page] ERROR: Redirected to login page");
      console.log(
        "[test-media-page] Page content preview:",
        pageContent.substring(0, 500),
      );
      return;
    }

    // Look for any PHP errors
    if (
      pageContent.includes("Fatal error") ||
      pageContent.includes("Parse error")
    ) {
      console.log("[test-media-page] ERROR: PHP error detected");
      console.log(
        "[test-media-page] Error content:",
        pageContent.substring(0, 1000),
      );
      return;
    }

    await page.waitForSelector("h2", { timeout: 10000 });

    // Check page title and navigation
    const pageTitle = await page.textContent("h2");
    console.log("[test-media-page] Page title:", pageTitle);

    // Check if Media tab is active in navigation
    const activeNavItem = await page
      .locator(".admin-nav .active")
      .textContent();
    console.log("[test-media-page] Active navigation item:", activeNavItem);

    // Check statistics
    const statItems = await page.locator(".stat-item").all();
    console.log("[test-media-page] Number of stat items:", statItems.length);

    for (let i = 0; i < statItems.length; i++) {
      const statText = await statItems[i].textContent();
      console.log(
        `[test-media-page] Stat ${i + 1}:`,
        statText.replace(/\s+/g, " ").trim(),
      );
    }

    // Check if media grid exists
    const mediaGrid = page.locator(".media-grid");
    const hasMediaGrid = await mediaGrid.isVisible();
    console.log("[test-media-page] Media grid visible:", hasMediaGrid);

    if (hasMediaGrid) {
      // Count media items
      const mediaItems = await page.locator(".media-item").all();
      console.log(
        "[test-media-page] Number of media items:",
        mediaItems.length,
      );

      if (mediaItems.length > 0) {
        // Test first media item
        const firstItem = mediaItems[0];

        // Check if image is displayed
        const hasImage = await firstItem.locator("img").isVisible();
        console.log("[test-media-page] First item has image:", hasImage);

        // Get file details
        const fileName = await firstItem.locator(".media-name").textContent();
        const fileDetails = await firstItem.locator(".detail-value").all();

        console.log("[test-media-page] First file name:", fileName);

        for (let i = 0; i < fileDetails.length; i++) {
          const detail = await fileDetails[i].textContent();
          console.log(`[test-media-page] Detail ${i + 1}:`, detail);
        }

        // Test copy URL functionality
        console.log("[test-media-page] Testing copy URL functionality...");
        const copyBtn = firstItem.locator(".copy-btn");
        if (await copyBtn.isVisible()) {
          await copyBtn.click();
          await page.waitForTimeout(1000);

          const btnText = await copyBtn.textContent();
          console.log(
            "[test-media-page] Copy button text after click:",
            btnText,
          );
        }

        // Test view button
        const viewBtn = firstItem.locator(".view-btn");
        if (await viewBtn.isVisible()) {
          const viewHref = await viewBtn.getAttribute("href");
          console.log("[test-media-page] View button href:", viewHref);
        }
      }
    } else {
      // Check for empty state
      const emptyState = page.locator(".empty-state");
      const hasEmptyState = await emptyState.isVisible();
      console.log("[test-media-page] Empty state visible:", hasEmptyState);

      if (hasEmptyState) {
        const emptyMessage = await emptyState.locator("h3").textContent();
        console.log("[test-media-page] Empty state message:", emptyMessage);
      }
    }

    // Test navigation to other pages
    console.log("[test-media-page] Testing navigation to Posts page...");
    await page.click('a[href="posts.php"]');
    await page.waitForTimeout(1000);

    const postsPageUrl = page.url();
    console.log("[test-media-page] Posts page URL:", postsPageUrl);

    // Navigate back to media page
    await page.click('a[href="media.php"]');
    await page.waitForTimeout(1000);

    // Verify we're back on media page
    const finalPageTitle = await page.textContent("h2");
    console.log("[test-media-page] Final page title:", finalPageTitle);

    // Take screenshot
    await page.screenshot({
      path: "media-page-test.png",
      fullPage: true,
    });
    console.log("[test-media-page] Screenshot saved as media-page-test.png");

    // Test results summary
    const testResults = {
      pageLoaded: pageTitle === "Media Management",
      navigationActive: activeNavItem === "Media",
      hasStatistics: statItems.length === 4,
      mediaGridExists: hasMediaGrid,
      navigationWorking:
        postsPageUrl.includes("posts.php") &&
        finalPageTitle === "Media Management",
    };

    console.log("\n--- TEST RESULTS ---");
    console.log("✅ Page loaded correctly:", testResults.pageLoaded);
    console.log("✅ Navigation active state:", testResults.navigationActive);
    console.log("✅ Statistics displayed:", testResults.hasStatistics);
    console.log("✅ Media grid present:", testResults.mediaGridExists);
    console.log("✅ Navigation working:", testResults.navigationWorking);

    const allTestsPassed = Object.values(testResults).every(
      (result) => result === true,
    );

    if (allTestsPassed) {
      console.log("\n🎉 [test-media-page] ✅ ALL TESTS PASSED!");
      console.log("✅ Media management page is fully functional");
      console.log("✅ Navigation integration working correctly");
      console.log("✅ File listing and statistics working");
      console.log("✅ User interface responsive and functional");
    } else {
      console.log("\n❌ [test-media-page] SOME TESTS FAILED!");
      Object.entries(testResults).forEach(([test, result]) => {
        if (!result) {
          console.log(`  - ${test}: FAILED`);
        }
      });
    }

    // Keep browser open for manual inspection
    console.log("\n[test-media-page] Keeping browser open for 10 seconds...");
    await page.waitForTimeout(10000);
  } catch (error) {
    console.error("[test-media-page] Error:", error.message);
    console.error(error.stack);
  } finally {
    await browser.close();
    console.log("[test-media-page] end - test completed");
  }
}

// Run the test
testMediaPage().catch(console.error);
