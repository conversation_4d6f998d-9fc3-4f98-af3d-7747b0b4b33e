# REST API Implementation Plan

## Overview
Implement a secure REST API for the Pixels application that allows external applications to create posts using token-based authentication. Only admin users will have access to API tokens and the ability to create posts via the API.

## Architecture Diagram

```mermaid
graph TB
    A[External Application] -->|POST /api/posts.php| B[API Endpoint]
    B -->|Bearer Token| C[Token Validation]
    C -->|Valid Admin Token| D[Post Creation]
    C -->|Invalid Token| E[401 Unauthorized]
    D -->|Success| F[JSON Response]
    
    G[Admin Panel] -->|Profile Page| H[Token Management]
    H -->|Generate/Regenerate| I[New API Token]
    H -->|Display| J[Current Token]
    
    K[Database] -->|users table| L[api_token column]
    K -->|posts table| M[title, content, created_at]
```

## Database Schema Changes

```mermaid
erDiagram
    users {
        int id PK
        string username
        string password_hash
        string role
        string api_token "NEW FIELD"
        datetime created_at
        datetime updated_at
    }
    
    posts {
        int id PK
        string title
        text content
        datetime created_at
        datetime updated_at
    }
```

## Implementation Checklist

### Phase 1: Database & Authentication Setup
- [ ] Add `api_token` column to users table
- [ ] Create function to generate secure API tokens
- [ ] Create function to validate API tokens
- [ ] Update user creation to generate initial token

### Phase 2: Admin Interface Updates
- [ ] Add API navigation link to index.php
- [ ] Create api.php documentation page
- [ ] Update profile.php to show API token
- [ ] Add token regeneration functionality
- [ ] Add AJAX handler for token regeneration

### Phase 3: REST API Implementation
- [ ] Create api/ directory structure
- [ ] Implement POST /api/posts.php endpoint
- [ ] Add proper CORS headers
- [ ] Implement authentication middleware
- [ ] Add input validation and sanitization
- [ ] Add proper error handling and responses

### Phase 4: Testing Infrastructure
- [ ] Create test.html for manual API testing
- [ ] Write Playwright end-to-end tests
- [ ] Test token validation scenarios
- [ ] Test post creation via API
- [ ] Test error handling cases

### Phase 5: Documentation & Security
- [ ] Update FUNCTIONS.md with new functions
- [ ] Add API documentation to admin interface
- [ ] Implement rate limiting (future enhancement)
- [ ] Add API usage logging (future enhancement)

## API Specification

### Endpoint: POST /api/posts.php

**Authentication**: Bearer Token in Authorization header

**Request Headers**:
```
Content-Type: application/json
Authorization: Bearer {api_token}
```

**Request Body**:
```json
{
    "title": "Post Title",
    "content": "Post content here..."
}
```

**Success Response (201)**:
```json
{
    "success": true,
    "id": 123,
    "message": "Post created successfully"
}
```

**Error Responses**:
- 401 Unauthorized: Invalid or missing token
- 400 Bad Request: Missing required fields
- 500 Internal Server Error: Database error

## Security Considerations

1. **Token Security**:
   - Generate cryptographically secure random tokens
   - Store tokens hashed in database (optional enhancement)
   - Allow token regeneration for compromised tokens

2. **Access Control**:
   - Only admin users can have API tokens
   - Validate user role and token on each request
   - No token sharing between users

3. **Input Validation**:
   - Sanitize all input data
   - Validate required fields
   - Prevent SQL injection and XSS

4. **Rate Limiting** (Future):
   - Implement per-token rate limiting
   - Log API usage for monitoring

## File Structure

```
pixels/
├── public/
│   ├── admin/
│   │   ├── index.php (updated - add API link)
│   │   ├── profile.php (updated - show token)
│   │   ├── api.php (new - documentation)
│   │   └── ajax/
│   │       └── regenerate-token.php (new)
│   ├── api/
│   │   └── posts.php (new - API endpoint)
│   └── test.html (new - testing interface)
├── includes/
│   ├── auth.php (updated - token functions)
│   └── database.php (updated - token queries)
└── tests/
    └── api.spec.js (new - E2E tests)
```

## Testing Strategy

1. **Unit Tests**:
   - Token generation and validation
   - Database operations
   - Input sanitization

2. **Integration Tests**:
   - API endpoint functionality
   - Authentication flow
   - Error handling

3. **End-to-End Tests**:
   - Complete user workflow
   - Token management in admin panel
   - API usage from external application

## Success Criteria

- [ ] Admin users can view and regenerate API tokens
- [ ] External applications can create posts with valid tokens
- [ ] Invalid tokens are properly rejected
- [ ] All tests pass consistently
- [ ] No security vulnerabilities introduced
- [ ] Clean error messages and logging