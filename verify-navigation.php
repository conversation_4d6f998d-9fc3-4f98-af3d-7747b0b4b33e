<?php
/**
 * Manual Navigation Verification Script
 * Checks if API navigation links are present on all admin pages
 */

echo "Navigation Verification Script\n";
echo "==============================\n\n";

// Define admin pages to check
$adminPages = [
    'index.php' => 'Dashboard',
    'posts.php' => 'Posts',
    'media.php' => 'Media',
    'profile.php' => 'Profile',
    'api.php' => 'API',
    'session-debug.php' => 'Session Debug'
];

$publicDir = __DIR__ . '/public/admin/';
$results = [];

foreach ($adminPages as $file => $pageName) {
    $filePath = $publicDir . $file;

    if (!file_exists($filePath)) {
        $results[$pageName] = ['exists' => false, 'hasApiLink' => false, 'hasMediaLink' => false];
        continue;
    }

    $content = file_get_contents($filePath);

    // Check for API link
    $hasApiLink = strpos($content, 'href="api.php"') !== false;

    // Check for Media link (important for session-debug)
    $hasMediaLink = strpos($content, 'href="media.php"') !== false;

    $results[$pageName] = [
        'exists' => true,
        'hasApiLink' => $hasApiLink,
        'hasMediaLink' => $hasMediaLink
    ];
}

// Display results
echo "Navigation Link Verification Results:\n";
echo "====================================\n\n";

foreach ($results as $pageName => $result) {
    echo "📄 {$pageName} Page:\n";

    if (!$result['exists']) {
        echo "   ❌ File does not exist\n";
    } else {
        echo "   ✅ File exists\n";
        echo "   " . ($result['hasApiLink'] ? '✅' : '❌') . " API link present\n";

        // Special check for session-debug page
        if ($pageName === 'Session Debug') {
            echo "   " . ($result['hasMediaLink'] ? '✅' : '❌') . " Media link present\n";
        }
    }
    echo "\n";
}

// Summary
$totalPages = count($results);
$pagesWithApiLink = 0;
$pagesExisting = 0;

foreach ($results as $result) {
    if ($result['exists']) {
        $pagesExisting++;
        if ($result['hasApiLink']) {
            $pagesWithApiLink++;
        }
    }
}

echo "Summary:\n";
echo "========\n";
echo "Total admin pages: {$totalPages}\n";
echo "Existing pages: {$pagesExisting}\n";
echo "Pages with API link: {$pagesWithApiLink}\n";

if ($pagesWithApiLink === $pagesExisting) {
    echo "\n🎉 SUCCESS: All admin pages have API navigation links!\n";
} else {
    echo "\n⚠️  ISSUE: Some pages are missing API navigation links.\n";
}

// Test navigation structure consistency
echo "\nTesting Navigation Structure:\n";
echo "============================\n";

$expectedNavItems = [
    'index.php' => 'Dashboard',
    'posts.php' => 'Posts',
    'media.php' => 'Media',
    'profile.php' => 'Profile',
    'api.php' => 'API',
    '../index.php' => 'View Site',
    'logout.php' => 'Logout'
];

foreach ($adminPages as $file => $pageName) {
    $filePath = $publicDir . $file;

    if (!file_exists($filePath)) {
        continue;
    }

    $content = file_get_contents($filePath);
    $missingLinks = [];

    foreach ($expectedNavItems as $href => $linkText) {
        if (strpos($content, "href=\"{$href}\"") === false) {
            $missingLinks[] = $linkText;
        }
    }

    echo "📄 {$pageName}:\n";
    if (empty($missingLinks)) {
        echo "   ✅ All navigation links present\n";
    } else {
        echo "   ❌ Missing links: " . implode(', ', $missingLinks) . "\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Verification completed at: " . date('Y-m-d H:i:s') . "\n";
echo "You can now test the navigation manually at: http://localhost:8000/admin/\n";
echo "Login with: admin / admin\n";
?>
