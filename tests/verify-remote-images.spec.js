const { test, expect } = require('@playwright/test');

test.describe('Verify Remote Images in Media Page', () => {
    test('should display remote images in media page', async ({ page }) => {
        console.log('Verifying remote images display...');

        // Set longer timeout for this test
        test.setTimeout(60000);

        // Navigate to login page
        await page.goto('http://localhost:8000/admin/login.php');
        await page.waitForLoadState('networkidle');

        // Fill login form
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin');

        // Submit login form
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Navigate to media page
        console.log('Navigating to media page...');
        await page.goto('http://localhost:8000/admin/media.php');
        await page.waitForLoadState('networkidle');

        // Verify page loaded correctly
        await expect(page.locator('h2')).toHaveText('Media Management');
        console.log('✅ Media Management page loaded');

        // Check statistics section
        const totalFiles = await page.locator('.stat-item').filter({ hasText: 'Total Files' }).locator('.stat-value').textContent();
        const localFiles = await page.locator('.stat-item').filter({ hasText: 'Local Files' }).locator('.stat-value').textContent();
        const remoteImages = await page.locator('.stat-item').filter({ hasText: 'Remote Images' }).locator('.stat-value').textContent();

        console.log(`Statistics found:
        - Total Files: ${totalFiles}
        - Local Files: ${localFiles}
        - Remote Images: ${remoteImages}`);

        // Verify statistics are numbers
        expect(parseInt(totalFiles)).toBeGreaterThanOrEqual(0);
        expect(parseInt(localFiles)).toBeGreaterThanOrEqual(0);
        expect(parseInt(remoteImages)).toBeGreaterThanOrEqual(0);

        console.log('✅ All statistics are valid numbers');

        // Check if remote images are present
        const remoteImageCount = parseInt(remoteImages);

        if (remoteImageCount > 0) {
            console.log(`Found ${remoteImageCount} remote images - checking display...`);

            // Look for remote image items
            const remoteItems = page.locator('.media-item[data-type="remote"]');
            const actualRemoteItems = await remoteItems.count();

            console.log(`Found ${actualRemoteItems} remote image items in grid`);
            expect(actualRemoteItems).toBeGreaterThan(0);

            // Check first remote item
            const firstRemoteItem = remoteItems.first();

            // Check for remote badge
            const remoteBadge = firstRemoteItem.locator('.badge-remote');
            await expect(remoteBadge).toBeVisible();
            console.log('✅ Remote badge visible');

            // Check for remote image styling
            const dataType = await firstRemoteItem.getAttribute('data-type');
            expect(dataType).toBe('remote');
            console.log('✅ Remote image has correct data-type attribute');

            // Check for source information
            const sourceRow = firstRemoteItem.locator('.detail-row').filter({ hasText: 'Source' });
            if (await sourceRow.count() > 0) {
                console.log('✅ Source information displayed for remote images');
            }

            // Check action buttons
            await expect(firstRemoteItem.locator('.copy-btn')).toBeVisible();
            await expect(firstRemoteItem.locator('.view-btn')).toBeVisible();
            console.log('✅ Action buttons available for remote images');

        } else {
            console.log('ℹ️ No remote images found in current posts');
        }

        // Verify total calculation
        const calculatedTotal = parseInt(localFiles) + parseInt(remoteImages);
        expect(parseInt(totalFiles)).toBe(calculatedTotal);
        console.log('✅ Total files calculation is correct');

        // Check that page doesn't have any obvious errors
        const errorMessages = page.locator('.alert-error, .error');
        const errorCount = await errorMessages.count();
        expect(errorCount).toBe(0);
        console.log('✅ No error messages displayed');

        // Take screenshot for verification
        await page.screenshot({
            path: 'test-results/remote-images-verification.png',
            fullPage: true
        });
        console.log('📸 Screenshot saved for verification');

        console.log('✅ Remote images verification completed successfully!');
    });

    test('should handle mixed local and remote images correctly', async ({ page }) => {
        console.log('Testing mixed local and remote images...');

        // Login
        await page.goto('http://localhost:8000/admin/login.php');
        await page.waitForLoadState('networkidle');
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin');
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Navigate to media page
        await page.goto('http://localhost:8000/admin/media.php');
        await page.waitForLoadState('networkidle');

        // Check for both types of media items
        const localItems = page.locator('.media-item[data-type="local"]');
        const remoteItems = page.locator('.media-item[data-type="remote"]');

        const localCount = await localItems.count();
        const remoteCount = await remoteItems.count();

        console.log(`Found ${localCount} local items and ${remoteCount} remote items`);

        // Verify different styling for different types
        if (localCount > 0) {
            const localBadge = localItems.first().locator('.badge-primary, .badge-secondary');
            await expect(localBadge).toBeVisible();
            console.log('✅ Local images have appropriate badges');
        }

        if (remoteCount > 0) {
            const remoteBadge = remoteItems.first().locator('.badge-remote');
            await expect(remoteBadge).toBeVisible();
            console.log('✅ Remote images have remote badges');

            // Check orange border for remote items
            const borderColor = await remoteItems.first().evaluate(el => {
                return window.getComputedStyle(el).borderLeftColor;
            });
            console.log(`Remote item border color: ${borderColor}`);
        }

        // Verify sorting (newest first)
        const allItems = page.locator('.media-item');
        const totalItems = await allItems.count();

        if (totalItems > 1) {
            console.log(`✅ Found ${totalItems} total media items (sorted by date)`);
        }

        console.log('✅ Mixed media types test completed!');
    });
});
