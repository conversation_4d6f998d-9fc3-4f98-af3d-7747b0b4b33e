const { test, expect } = require('@playwright/test');

test.describe('Category Filtering', () => {
    test.beforeEach(async ({ page }) => {
        // Set longer timeout for this test suite
        test.setTimeout(60000);
        
        // Start from the home page
        await page.goto('/index.php');
        await page.waitForLoadState('networkidle');
    });

    test('should display category filter when URL has category parameter', async ({ page }) => {
        console.log('[test-category-filtering] Testing category filter display');
        
        // Navigate to a category page
        await page.goto('/index.php?category=tech');
        await page.waitForLoadState('networkidle');
        
        // Check that breadcrumb appears
        const breadcrumb = page.locator('.breadcrumb');
        await expect(breadcrumb).toBeVisible();
        await expect(breadcrumb).toContainText('Home');
        await expect(breadcrumb).toContainText('Tech');
        
        // Check that page title shows category
        const pageTitle = page.locator('.content-header h2');
        await expect(pageTitle).toContainText('Tech Posts');
        
        // Check that clear filter link appears
        const clearFilter = page.locator('.clear-filter');
        await expect(clearFilter).toBeVisible();
        await expect(clearFilter).toContainText('View all posts');
        
        console.log('[test-category-filtering] ✅ Category filter display working');
    });

    test('should filter posts by category when category parameter is provided', async ({ page }) => {
        console.log('[test-category-filtering] Testing posts filtering by category');
        
        // First, go to home page and count total posts
        await page.goto('/index.php');
        await page.waitForLoadState('networkidle');
        
        const allPosts = page.locator('.post-preview');
        const totalPostsCount = await allPosts.count();
        
        // Now filter by tech category
        await page.goto('/index.php?category=tech');
        await page.waitForLoadState('networkidle');
        
        const techPosts = page.locator('.post-preview');
        const techPostsCount = await techPosts.count();
        
        // Verify that all displayed posts are tech posts
        for (let i = 0; i < techPostsCount; i++) {
            const post = techPosts.nth(i);
            const categoryBadge = post.locator('.category-badge');
            await expect(categoryBadge).toContainText('Tech');
        }
        
        // Check that post count is correct
        const postCountText = page.locator('.post-count');
        if (techPostsCount > 0) {
            await expect(postCountText).toContainText('tech post');
        }
        
        console.log(`[test-category-filtering] ✅ Filtered ${techPostsCount} tech posts from ${totalPostsCount} total posts`);
    });

    test('should navigate to category filter when clicking category badge', async ({ page }) => {
        console.log('[test-category-filtering] Testing category badge navigation');
        
        // Go to home page
        await page.goto('/index.php');
        await page.waitForLoadState('networkidle');
        
        // Find the first post with a category badge
        const firstPost = page.locator('.post-preview').first();
        const categoryBadge = firstPost.locator('.category-badge');
        
        if (await categoryBadge.count() > 0) {
            // Get the category name before clicking
            const categoryText = await categoryBadge.textContent();
            const expectedCategory = categoryText.toLowerCase().trim();
            
            // Click the category badge
            await categoryBadge.click();
            await page.waitForLoadState('networkidle');
            
            // Verify we're on the category page
            const currentUrl = page.url();
            expect(currentUrl).toContain(`category=${expectedCategory}`);
            
            // Verify breadcrumb shows correct category
            const breadcrumb = page.locator('.breadcrumb');
            await expect(breadcrumb).toContainText(categoryText);
            
            console.log(`[test-category-filtering] ✅ Successfully navigated to ${expectedCategory} category`);
        } else {
            console.log('[test-category-filtering] ⚠️ No category badges found to test navigation');
        }
    });

    test('should handle invalid category gracefully', async ({ page }) => {
        console.log('[test-category-filtering] Testing invalid category handling');
        
        // Try to access an invalid category
        await page.goto('/index.php?category=invalidcategory');
        await page.waitForLoadState('networkidle');
        
        // Should either show error message or redirect to home
        const pageContent = await page.textContent('body');
        const hasErrorMessage = pageContent.includes('Error') || pageContent.includes('Invalid');
        const isOnHomePage = !page.url().includes('category=invalidcategory');
        
        // Either should show an error or redirect to valid page
        expect(hasErrorMessage || isOnHomePage).toBe(true);
        
        console.log('[test-category-filtering] ✅ Invalid category handled gracefully');
    });

    test('should clear category filter when clicking "View all posts"', async ({ page }) => {
        console.log('[test-category-filtering] Testing category filter clearing');
        
        // Navigate to a category page
        await page.goto('/index.php?category=tech');
        await page.waitForLoadState('networkidle');
        
        // Click the clear filter link
        const clearFilter = page.locator('.clear-filter');
        await clearFilter.click();
        await page.waitForLoadState('networkidle');
        
        // Verify we're back on the main page
        const currentUrl = page.url();
        expect(currentUrl).not.toContain('category=');
        expect(currentUrl).toMatch(/\/index\.php$/);
        
        // Verify breadcrumb is gone
        const breadcrumb = page.locator('.breadcrumb');
        await expect(breadcrumb).not.toBeVisible();
        
        // Verify page title is back to normal
        const pageTitle = page.locator('.content-header h2');
        await expect(pageTitle).toContainText('Blog Posts');
        
        console.log('[test-category-filtering] ✅ Category filter cleared successfully');
    });

    test('should maintain category filter in pagination', async ({ page }) => {
        console.log('[test-category-filtering] Testing category filter persistence in pagination');
        
        // Navigate to a category page
        await page.goto('/index.php?category=tech');
        await page.waitForLoadState('networkidle');
        
        // Check if pagination exists
        const pagination = page.locator('.pagination-container');
        
        if (await pagination.count() > 0) {
            // Find pagination links
            const paginationLinks = pagination.locator('a');
            const linkCount = await paginationLinks.count();
            
            if (linkCount > 0) {
                // Click on a pagination link (try the last one, which might be "Next" or a page number)
                const lastLink = paginationLinks.last();
                await lastLink.click();
                await page.waitForLoadState('networkidle');
                
                // Verify we're still on the tech category page
                const currentUrl = page.url();
                expect(currentUrl).toContain('category=tech');
                
                // Verify breadcrumb still shows
                const breadcrumb = page.locator('.breadcrumb');
                await expect(breadcrumb).toContainText('Tech');
                
                console.log('[test-category-filtering] ✅ Category filter maintained in pagination');
            } else {
                console.log('[test-category-filtering] ⚠️ No pagination links found to test');
            }
        } else {
            console.log('[test-category-filtering] ⚠️ No pagination found to test category persistence');
        }
    });

    test('should test all valid categories', async ({ page }) => {
        console.log('[test-category-filtering] Testing all valid categories');
        
        const validCategories = ['tech', 'gaming', 'film', 'serie'];
        
        for (const category of validCategories) {
            console.log(`[test-category-filtering] Testing ${category} category`);
            
            // Navigate to category page
            await page.goto(`/index.php?category=${category}`);
            await page.waitForLoadState('networkidle');
            
            // Verify page loads without error
            const pageTitle = page.locator('.content-header h2');
            await expect(pageTitle).toContainText(`${category.charAt(0).toUpperCase() + category.slice(1)} Posts`);
            
            // Verify breadcrumb shows correct category
            const breadcrumb = page.locator('.breadcrumb');
            await expect(breadcrumb).toContainText(category.charAt(0).toUpperCase() + category.slice(1));
            
            // Verify all posts shown are from this category (if any posts exist)
            const posts = page.locator('.post-preview');
            const postCount = await posts.count();
            
            if (postCount > 0) {
                for (let i = 0; i < Math.min(postCount, 3); i++) { // Check first 3 posts
                    const post = posts.nth(i);
                    const categoryBadge = post.locator('.category-badge');
                    await expect(categoryBadge).toContainText(category.charAt(0).toUpperCase() + category.slice(1));
                }
            }
            
            console.log(`[test-category-filtering] ✅ ${category} category working correctly`);
        }
    });

    test('should show appropriate message when no posts in category', async ({ page }) => {
        console.log('[test-category-filtering] Testing empty category display');
        
        // This test assumes there might be a category with no posts
        // We'll test with a valid category and check the no-posts message structure
        await page.goto('/index.php?category=serie'); // Serie might have fewer posts
        await page.waitForLoadState('networkidle');
        
        const posts = page.locator('.post-preview');
        const postCount = await posts.count();
        
        if (postCount === 0) {
            // Check that appropriate message is shown
            const noPostsMessage = page.locator('.no-posts');
            await expect(noPostsMessage).toBeVisible();
            await expect(noPostsMessage).toContainText('No posts found in the serie category');
            
            // Check that link to view all posts is present
            const viewAllLink = noPostsMessage.locator('a[href="/index.php"]');
            await expect(viewAllLink).toBeVisible();
            
            console.log('[test-category-filtering] ✅ Empty category message displayed correctly');
        } else {
            console.log(`[test-category-filtering] ⚠️ Serie category has ${postCount} posts, cannot test empty state`);
        }
    });
});