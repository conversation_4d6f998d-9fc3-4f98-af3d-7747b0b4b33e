const { test, expect } = require("@playwright/test");

test.describe("Media Delete and Remove Functionality", () => {
  test("should delete local files permanently", async ({ page }) => {
    console.log("Testing local file deletion...");

    // Set longer timeout for this test
    test.setTimeout(90000);

    // Navigate to login page
    await page.goto("http://localhost:8000/admin/login.php");
    await page.waitForLoadState("networkidle");

    // Login
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Navigate to media page
    console.log("Navigating to media page...");
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForLoadState("networkidle");

    // Get initial count of local files
    const initialLocalCount = await page
      .locator(".stat-item")
      .filter({ hasText: "Local Files" })
      .locator(".stat-value")
      .textContent();
    console.log(`Initial local files count: ${initialLocalCount}`);

    // Look for local files with delete buttons
    const localItems = page.locator('.media-item[data-type="local"]');
    const localItemCount = await localItems.count();

    if (localItemCount > 0) {
      console.log(`Found ${localItemCount} local files to test deletion`);

      // Get file name for the first local item
      const firstLocalItem = localItems.first();
      const fileName = await firstLocalItem
        .locator(".media-name")
        .textContent();
      console.log(`Testing deletion of: ${fileName}`);

      // Check that delete button is present
      const deleteBtn = firstLocalItem.locator(".delete-btn");
      await expect(deleteBtn).toBeVisible();
      console.log("✅ Delete button is visible");

      // Set up dialog handler for confirmation BEFORE clicking
      const dialogPromise = page.waitForEvent("dialog");

      // Click delete button
      await deleteBtn.click();

      // Handle the dialog
      const dialog = await dialogPromise;
      expect(dialog.type()).toBe("confirm");
      expect(dialog.message()).toContain(fileName.trim());
      console.log(`Confirmation dialog shown: ${dialog.message()}`);
      await dialog.accept();
      await page.waitForLoadState("networkidle");

      // Check for success message
      const successAlert = page.locator(".alert-success");
      if ((await successAlert.count()) > 0) {
        const message = await successAlert.textContent();
        expect(message).toContain("deleted successfully");
        console.log("✅ Success message displayed");
      }

      // Verify file count decreased
      const newLocalCount = await page
        .locator(".stat-item")
        .filter({ hasText: "Local Files" })
        .locator(".stat-value")
        .textContent();
      console.log(`New local files count: ${newLocalCount}`);

      if (parseInt(initialLocalCount) > 0) {
        expect(parseInt(newLocalCount)).toBeLessThan(
          parseInt(initialLocalCount),
        );
        console.log("✅ Local file count decreased after deletion");
      }
    } else {
      console.log("⚠️ No local files found to test deletion");
    }

    console.log("✅ Local file deletion test completed");
  });

  test("should remove remote images from display list", async ({ page }) => {
    console.log("Testing remote image removal...");

    // Login
    await page.goto("http://localhost:8000/admin/login.php");
    await page.waitForLoadState("networkidle");
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Navigate to media page
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForLoadState("networkidle");

    // Get initial count of remote images
    const initialRemoteCount = await page
      .locator(".stat-item")
      .filter({ hasText: "Remote Images" })
      .locator(".stat-value")
      .textContent();
    console.log(`Initial remote images count: ${initialRemoteCount}`);

    // Look for remote images with remove buttons
    const remoteItems = page.locator('.media-item[data-type="remote"]');
    const remoteItemCount = await remoteItems.count();

    if (remoteItemCount > 0) {
      console.log(`Found ${remoteItemCount} remote images to test removal`);

      // Get image name for the first remote item
      const firstRemoteItem = remoteItems.first();
      const imageName = await firstRemoteItem
        .locator(".media-name")
        .textContent();
      console.log(`Testing removal of: ${imageName}`);

      // Check that remove button is present
      const removeBtn = firstRemoteItem.locator(".remove-btn");
      await expect(removeBtn).toBeVisible();
      console.log("✅ Remove button is visible");

      // Set up dialog handler for confirmation BEFORE clicking
      const dialogPromise = page.waitForEvent("dialog");

      // Click remove button
      await removeBtn.click();

      // Handle the dialog
      const dialog = await dialogPromise;
      expect(dialog.type()).toBe("confirm");
      expect(dialog.message()).toContain("Remove");
      expect(dialog.message()).toContain("Restore Hidden Images");
      console.log(`Confirmation dialog shown: ${dialog.message()}`);
      await dialog.accept();
      await page.waitForLoadState("networkidle");

      // Check for success message
      const successAlert = page.locator(".alert-success");
      if ((await successAlert.count()) > 0) {
        const message = await successAlert.textContent();
        expect(message).toContain("removed from list");
        console.log("✅ Success message displayed");
      }

      // Verify remote image count decreased
      const newRemoteCount = await page
        .locator(".stat-item")
        .filter({ hasText: "Remote Images" })
        .locator(".stat-value")
        .textContent();
      console.log(`New remote images count: ${newRemoteCount}`);

      if (parseInt(initialRemoteCount) > 0) {
        expect(parseInt(newRemoteCount)).toBeLessThan(
          parseInt(initialRemoteCount),
        );
        console.log("✅ Remote image count decreased after removal");
      }

      // Check that hidden count appears
      const hiddenStat = page.locator(".stat-hidden");
      if ((await hiddenStat.count()) > 0) {
        const hiddenCount = await hiddenStat
          .locator(".stat-value")
          .textContent();
        expect(parseInt(hiddenCount)).toBeGreaterThan(0);
        console.log(`✅ Hidden images count shown: ${hiddenCount}`);
      }
    } else {
      console.log("⚠️ No remote images found to test removal");
    }

    console.log("✅ Remote image removal test completed");
  });

  test("should restore hidden remote images", async ({ page }) => {
    console.log("Testing restore hidden images functionality...");

    // Login
    await page.goto("http://localhost:8000/admin/login.php");
    await page.waitForLoadState("networkidle");
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Navigate to media page
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForLoadState("networkidle");

    // First, hide a remote image if available
    const remoteItems = page.locator('.media-item[data-type="remote"]');
    const remoteItemCount = await remoteItems.count();

    if (remoteItemCount > 0) {
      console.log("Hiding a remote image first...");

      // Hide first remote image
      const removeBtn = remoteItems.first().locator(".remove-btn");
      const dialogPromise = page.waitForEvent("dialog");
      await removeBtn.click();
      const dialog = await dialogPromise;
      await dialog.accept();
      await page.waitForLoadState("networkidle");

      // Check that restore button appears
      const restoreBtn = page
        .locator("button")
        .filter({ hasText: "Restore Hidden Images" });
      await expect(restoreBtn).toBeVisible();
      console.log("✅ Restore button is visible");

      // Get counts before restore
      const remoteCountBeforeRestore = await page
        .locator(".stat-item")
        .filter({ hasText: "Remote Images" })
        .locator(".stat-value")
        .textContent();
      const hiddenCount = await page
        .locator(".stat-hidden .stat-value")
        .textContent();
      console.log(
        `Before restore - Remote: ${remoteCountBeforeRestore}, Hidden: ${hiddenCount}`,
      );

      // Set up dialog handler for restore confirmation BEFORE clicking
      const restoreDialogPromise = page.waitForEvent("dialog");

      // Click restore button
      await restoreBtn.click();

      // Handle the dialog
      const restoreDialog = await restoreDialogPromise;
      expect(restoreDialog.type()).toBe("confirm");
      expect(restoreDialog.message()).toContain("Restore all hidden");
      console.log(`Restore confirmation: ${restoreDialog.message()}`);
      await restoreDialog.accept();
      await page.waitForLoadState("networkidle");

      // Check for success message
      const successAlert = page.locator(".alert-success");
      if ((await successAlert.count()) > 0) {
        const message = await successAlert.textContent();
        expect(message).toContain("restored");
        console.log("✅ Restore success message displayed");
      }

      // Verify hidden stat is gone
      const hiddenStatAfter = page.locator(".stat-hidden");
      const hiddenStatCount = await hiddenStatAfter.count();
      expect(hiddenStatCount).toBe(0);
      console.log("✅ Hidden stat removed after restore");

      // Verify restore button is gone
      const restoreBtnAfter = page
        .locator("button")
        .filter({ hasText: "Restore Hidden Images" });
      const restoreBtnCount = await restoreBtnAfter.count();
      expect(restoreBtnCount).toBe(0);
      console.log("✅ Restore button removed after restore");

      // Verify remote count increased
      const remoteCountAfterRestore = await page
        .locator(".stat-item")
        .filter({ hasText: "Remote Images" })
        .locator(".stat-value")
        .textContent();
      expect(parseInt(remoteCountAfterRestore)).toBeGreaterThan(
        parseInt(remoteCountBeforeRestore),
      );
      console.log(
        `✅ Remote count increased: ${remoteCountBeforeRestore} → ${remoteCountAfterRestore}`,
      );
    } else {
      console.log(
        "⚠️ No remote images available to test restore functionality",
      );
    }

    console.log("✅ Restore hidden images test completed");
  });

  test("should show proper confirmation dialogs with file names", async ({
    page,
  }) => {
    console.log("Testing confirmation dialog content...");

    // Login
    await page.goto("http://localhost:8000/admin/login.php");
    await page.waitForLoadState("networkidle");
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Navigate to media page
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForLoadState("networkidle");

    // Test delete confirmation for local files
    const localItems = page.locator('.media-item[data-type="local"]');
    const localItemCount = await localItems.count();

    if (localItemCount > 0) {
      const fileName = await localItems
        .first()
        .locator(".media-name")
        .textContent();
      const deleteBtn = localItems.first().locator(".delete-btn");

      const deleteDialogPromise = page.waitForEvent("dialog");

      await deleteBtn.click();

      const deleteDialog = await deleteDialogPromise;
      expect(deleteDialog.type()).toBe("confirm");
      expect(deleteDialog.message()).toContain("permanently delete");
      expect(deleteDialog.message()).toContain("cannot be undone");
      console.log("✅ Delete confirmation dialog contains proper warnings");
      await deleteDialog.dismiss(); // Dismiss to avoid actual deletion
      console.log("✅ Delete confirmation dialog shown");
    }

    // Test remove confirmation for remote images
    const remoteItems = page.locator('.media-item[data-type="remote"]');
    const remoteItemCount = await remoteItems.count();

    if (remoteItemCount > 0) {
      const imageName = await remoteItems
        .first()
        .locator(".media-name")
        .textContent();
      const removeBtn = remoteItems.first().locator(".remove-btn");

      const removeDialogPromise = page.waitForEvent("dialog");

      await removeBtn.click();

      const removeDialog = await removeDialogPromise;
      expect(removeDialog.type()).toBe("confirm");
      expect(removeDialog.message()).toContain("Remove");
      expect(removeDialog.message()).toContain("Restore Hidden Images");
      console.log("✅ Remove confirmation dialog contains proper instructions");
      await removeDialog.dismiss(); // Dismiss to avoid actual removal
      console.log("✅ Remove confirmation dialog shown");
    }

    console.log("✅ Confirmation dialog content test completed");
  });

  test("should handle CSRF protection properly", async ({ page }) => {
    console.log("Testing CSRF protection...");

    // Login
    await page.goto("http://localhost:8000/admin/login.php");
    await page.waitForLoadState("networkidle");
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Navigate to media page
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForLoadState("networkidle");

    // Check that CSRF token is present in meta tag
    const csrfToken = await page
      .locator('meta[name="csrf-token"]')
      .getAttribute("content");
    expect(csrfToken).toBeTruthy();
    console.log("✅ CSRF token found in meta tag");

    // Verify that action buttons include CSRF tokens in their forms
    const deleteBtn = page.locator(".delete-btn").first();
    const removeBtn = page.locator(".remove-btn").first();

    if ((await deleteBtn.count()) > 0) {
      // Simulate delete button click to check form creation
      await page.evaluate(() => {
        // Override confirm to return false to prevent actual submission
        window.confirm = () => false;
      });

      await deleteBtn.click();
      console.log("✅ Delete button CSRF handling verified");
    }

    if ((await removeBtn.count()) > 0) {
      // Simulate remove button click to check form creation
      await page.evaluate(() => {
        // Override confirm to return false to prevent actual submission
        window.confirm = () => false;
      });

      await removeBtn.click();
      console.log("✅ Remove button CSRF handling verified");
    }

    console.log("✅ CSRF protection test completed");
  });
});
