const { test, expect } = require("@playwright/test");

test.describe("Markdown Functionality Tests", () => {
  let postId;

  test.beforeEach(async ({ page }) => {
    // Navigate to admin login
    await page.goto("http://localhost:8000/admin/login.php");

    // Login as admin
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');

    // Wait for successful login redirect
    await page.waitForURL("**/admin/index.php");
  });

  test("should create post with markdown content and display it correctly", async ({
    page,
  }) => {
    console.log("[markdown-test] start");

    // Navigate to posts page
    await page.goto("http://localhost:8000/admin/posts.php");
    await page.waitForTimeout(1000);

    // Click "Add New Post" button
    await page.click('a[href="posts.php?action=new"]');
    await page.waitForTimeout(1000);

    // Fill out the form with markdown content
    const testTitle = "Markdown Test Post " + Date.now();
    const markdownContent = `# Main Heading Test

This is a **bold text** example and this is *italic text* example.

## Sub Heading Test

Here's a [link to Google](https://www.google.com) and some \`inline code\`.

### Third Level Heading Test

> This is a blockquote with important markdown information.

Here's an unordered list:
- First item in list
- Second item with **bold** text
- Third item with *italic* text

Here's an image test:
![Test Image](https://via.placeholder.com/300x200/0066cc/ffffff?text=Markdown+Test)

And here's some normal paragraph text with line breaks.
This should be properly formatted.`;

    const testDescription =
      "This is a **test description** with *markdown* formatting.";

    await page.fill('input[name="title"]', testTitle);
    await page.fill('textarea[name="description"]', testDescription);
    await page.fill('textarea[name="content"]', markdownContent);
    await page.selectOption('select[name="category"]', "tech");

    // Submit the form
    await page.click('button[type="submit"]');
    await page.waitForTimeout(2000);

    // Verify post was created successfully
    const successMessage = page.locator(".flash-message.success");
    await expect(successMessage).toBeVisible();
    await expect(successMessage).toContainText("Post created successfully");

    // Extract post ID from the success message or URL
    const currentUrl = page.url();
    const urlMatch = currentUrl.match(/id=(\d+)/);
    if (urlMatch) {
      postId = urlMatch[1];
    }

    // Navigate to the public post view
    await page.goto(`http://localhost:8000/index.php`);
    await page.waitForTimeout(1000);

    // Find and click on our test post
    const postLink = page.locator(`a:has-text("${testTitle}")`);
    await expect(postLink).toBeVisible();
    await postLink.click();
    await page.waitForTimeout(2000);

    // Verify markdown content is properly rendered on the post page
    const postContent = page.locator(".post-content");

    // Check for properly rendered HTML elements from markdown
    await expect(postContent.locator("h1")).toContainText("Main Heading Test");
    await expect(postContent.locator("h2")).toContainText("Sub Heading Test");
    await expect(postContent.locator("h3")).toContainText(
      "Third Level Heading Test",
    );

    // Check bold and italic text
    await expect(postContent.locator("strong")).toContainText("bold text");
    await expect(postContent.locator("em")).toContainText("italic text");

    // Check links are rendered properly
    const googleLink = postContent.locator('a[href="https://www.google.com"]');
    await expect(googleLink).toBeVisible();
    await expect(googleLink).toContainText("link to Google");
    await expect(googleLink).toHaveAttribute("target", "_blank");
    await expect(googleLink).toHaveAttribute("rel", "noopener noreferrer");

    // Check inline code
    await expect(postContent.locator("code")).toContainText("inline code");

    // Check blockquote
    await expect(postContent.locator("blockquote")).toContainText(
      "blockquote with important markdown information",
    );

    // Check list items
    const listItems = postContent.locator("li");
    await expect(listItems.nth(0)).toContainText("First item in list");
    await expect(listItems.nth(1)).toContainText("Second item with");
    await expect(listItems.nth(1).locator("strong")).toContainText("bold");
    await expect(listItems.nth(2)).toContainText("Third item with");
    await expect(listItems.nth(2).locator("em")).toContainText("italic");

    // Check image is rendered
    const testImage = postContent.locator('img[alt="Test Image"]');
    await expect(testImage).toBeVisible();
    await expect(testImage).toHaveAttribute(
      "src",
      "https://via.placeholder.com/300x200/0066cc/ffffff?text=Markdown+Test",
    );
    await expect(testImage).toHaveAttribute(
      "style",
      "max-width: 100%; height: auto;",
    );

    console.log(
      "[markdown-test] Post content markdown verification successful",
    );

    // Go back to homepage and verify markdown in post preview
    await page.goto("http://localhost:8000/index.php");
    await page.waitForTimeout(1000);

    // Find our test post in the list
    const postPreview = page
      .locator(".post-preview")
      .filter({ hasText: testTitle });
    await expect(postPreview).toBeVisible();

    // Check that description markdown is also rendered in preview
    const previewContent = postPreview.locator(".post-content");
    await expect(previewContent.locator("strong")).toContainText(
      "test description",
    );
    await expect(previewContent.locator("em")).toContainText("markdown");

    console.log(
      "[markdown-test] Post preview markdown verification successful",
    );

    console.log("[markdown-test] end");
  });

  test("should handle markdown with mixed HTML content", async ({ page }) => {
    console.log("[markdown-mixed-content-test] start");

    // Navigate to posts page
    await page.goto("http://localhost:8000/admin/posts.php");
    await page.waitForTimeout(1000);

    // Click "Add New Post" button
    await page.click('a[href="posts.php?action=new"]');
    await page.waitForTimeout(1000);

    // Fill out the form with mixed markdown and HTML content
    const testTitle = "Mixed Content Test " + Date.now();
    const mixedContent = `# Markdown Heading

This is **markdown bold** and this is <strong>HTML bold</strong>.

Here's a markdown link: [Google](https://www.google.com)
And here's an HTML link: <a href="https://www.example.com">Example</a>

- Markdown list item
- Another **markdown** item

<div>HTML div content</div>

> Markdown blockquote

<blockquote>HTML blockquote</blockquote>`;

    await page.fill('input[name="title"]', testTitle);
    await page.fill('textarea[name="content"]', mixedContent);
    await page.selectOption('select[name="category"]', "tech");

    // Submit the form
    await page.click('button[type="submit"]');
    await page.waitForTimeout(2000);

    // Verify post was created successfully
    const successMessage = page.locator(".flash-message.success");
    await expect(successMessage).toBeVisible();

    // Navigate to the public post view
    await page.goto(`http://localhost:8000/index.php`);
    await page.waitForTimeout(1000);

    // Find and click on our test post
    const postLink = page.locator(`a:has-text("${testTitle}")`);
    await expect(postLink).toBeVisible();
    await postLink.click();
    await page.waitForTimeout(2000);

    // Verify mixed content is handled properly
    const postContent = page.locator(".post-content");

    // Check markdown heading is converted
    await expect(postContent.locator("h1")).toContainText("Markdown Heading");

    // Check both markdown and HTML bold text work
    const boldElements = postContent.locator("strong");
    await expect(boldElements.nth(0)).toContainText("markdown bold");
    await expect(boldElements.nth(1)).toContainText("HTML bold");

    // Check both markdown and HTML links work
    const googleLink = postContent.locator('a[href="https://www.google.com"]');
    await expect(googleLink).toBeVisible();
    await expect(googleLink).toContainText("Google");

    // Check list items are rendered
    await expect(postContent.locator("li")).toHaveCount(2);

    console.log("[markdown-mixed-content-test] end");
  });

  test("should prevent XSS in markdown content", async ({ page }) => {
    console.log("[markdown-xss-test] start");

    // Navigate to posts page
    await page.goto("http://localhost:8000/admin/posts.php");
    await page.waitForTimeout(1000);

    // Click "Add New Post" button
    await page.click('a[href="posts.php?action=new"]');
    await page.waitForTimeout(1000);

    // Fill out the form with potentially malicious markdown content
    const testTitle = "XSS Test Post " + Date.now();
    const maliciousContent = `# Safe Heading

This is normal **bold text**.

[Safe Link](https://www.google.com)

> Safe blockquote

- Safe list item
- Another safe item

Normal paragraph text.`;

    await page.fill('input[name="title"]', testTitle);
    await page.fill('textarea[name="content"]', maliciousContent);
    await page.selectOption('select[name="category"]', "tech");

    // Submit the form
    await page.click('button[type="submit"]');
    await page.waitForTimeout(2000);

    // Verify post was created successfully
    const successMessage = page.locator(".flash-message.success");
    await expect(successMessage).toBeVisible();

    // Navigate to the public post view
    await page.goto(`http://localhost:8000/index.php`);
    await page.waitForTimeout(1000);

    // Find and click on our test post
    const postLink = page.locator(`a:has-text("${testTitle}")`);
    await expect(postLink).toBeVisible();
    await postLink.click();
    await page.waitForTimeout(2000);

    // Verify content is safely rendered
    const postContent = page.locator(".post-content");
    await expect(postContent.locator("h1")).toContainText("Safe Heading");
    await expect(postContent.locator("strong")).toContainText("bold text");
    await expect(postContent.locator("a")).toHaveAttribute(
      "href",
      "https://www.google.com",
    );

    // Check for any console errors that might indicate XSS attempts
    const logs = [];
    page.on("console", (msg) => {
      if (msg.type() === "error") {
        logs.push(msg.text());
      }
    });

    // Reload page to check for any XSS-related console errors
    await page.reload();
    await page.waitForTimeout(1000);

    // Verify no XSS-related errors in console
    const xssErrors = logs.filter(
      (log) =>
        log.includes("script") || log.includes("XSS") || log.includes("unsafe"),
    );
    expect(xssErrors.length).toBe(0);

    console.log("[markdown-xss-test] end");
  });

  test.afterEach(async ({ page }) => {
    // Clean up: delete the test post if postId is available
    if (postId) {
      try {
        await page.goto("http://localhost:8000/admin/posts.php");
        await page.waitForTimeout(1000);

        // Find the delete button for our test post
        const deleteButton = page.locator(
          `button[onclick*="deletePost(${postId})"]`,
        );
        if (await deleteButton.isVisible()) {
          await deleteButton.click();
          await page.waitForTimeout(500);

          // Confirm deletion in the modal
          const confirmButton = page.locator('button:has-text("Delete")');
          if (await confirmButton.isVisible()) {
            await confirmButton.click();
            await page.waitForTimeout(1000);
          }
        }
      } catch (error) {
        console.log("Cleanup error:", error.message);
      }
    }
  });
});
