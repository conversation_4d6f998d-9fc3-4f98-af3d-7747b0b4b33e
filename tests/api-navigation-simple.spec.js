/**
 * Simple API Navigation Test
 * Verifies that API navigation links are present on all admin pages
 */

const { test, expect } = require("@playwright/test");

test.describe("API Navigation Links", () => {
  test.beforeEach(async ({ page }) => {
    console.log("[api-navigation-test] start - logging in");

    // Navigate to admin login
    await page.goto("http://localhost:8000/admin/login.php");

    // Login with admin credentials
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');

    // Wait for successful login
    await page.waitForURL("**/admin/index.php", { timeout: 10000 });

    console.log("[api-navigation-test] end - login successful");
  });

  test("API link should be present on all admin pages", async ({ page }) => {
    console.log("[api-navigation-test] start - checking API links on all pages");

    const adminPages = [
      { url: "http://localhost:8000/admin/index.php", name: "Dashboard" },
      { url: "http://localhost:8000/admin/posts.php", name: "Posts" },
      { url: "http://localhost:8000/admin/media.php", name: "Media" },
      { url: "http://localhost:8000/admin/profile.php", name: "Profile" },
      { url: "http://localhost:8000/admin/session-debug.php", name: "Session Debug" }
    ];

    for (const adminPage of adminPages) {
      console.log(`[api-navigation-test] checking ${adminPage.name} page`);

      // Navigate to the page
      await page.goto(adminPage.url);
      await page.waitForSelector("nav.admin-nav", { timeout: 5000 });

      // Check that API link exists and is visible
      const apiLink = page.locator('nav.admin-nav a[href="api.php"]');
      await expect(apiLink).toBeVisible();
      await expect(apiLink).toHaveText("API");

      console.log(`✅ API link found on ${adminPage.name} page`);
    }

    console.log("[api-navigation-test] end - all pages have API links");
  });

  test("API link should be functional from posts page", async ({ page }) => {
    console.log("[api-navigation-test] start - testing API link functionality from posts");

    // Navigate to posts page
    await page.goto("http://localhost:8000/admin/posts.php");
    await page.waitForSelector("nav.admin-nav");

    // Click API link
    const apiLink = page.locator('nav.admin-nav a[href="api.php"]');
    await apiLink.click();

    // Verify navigation to API page
    await page.waitForURL("**/admin/api.php");
    await expect(page.locator("h2")).toContainText("API");

    console.log("✅ API navigation from posts page successful");
    console.log("[api-navigation-test] end - API link functionality verified");
  });

  test("API link should be functional from media page", async ({ page }) => {
    console.log("[api-navigation-test] start - testing API link functionality from media");

    // Navigate to media page
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForSelector("nav.admin-nav");

    // Click API link
    const apiLink = page.locator('nav.admin-nav a[href="api.php"]');
    await apiLink.click();

    // Verify navigation to API page
    await page.waitForURL("**/admin/api.php");
    await expect(page.locator("h2")).toContainText("API");

    console.log("✅ API navigation from media page successful");
    console.log("[api-navigation-test] end - API link functionality verified");
  });

  test("session-debug page should have both Media and API links", async ({ page }) => {
    console.log("[api-navigation-test] start - testing session-debug page navigation");

    // Navigate to session-debug page
    await page.goto("http://localhost:8000/admin/session-debug.php");
    await page.waitForSelector("nav.admin-nav");

    // Check that both Media and API links exist
    const mediaLink = page.locator('nav.admin-nav a[href="media.php"]');
    const apiLink = page.locator('nav.admin-nav a[href="api.php"]');

    await expect(mediaLink).toBeVisible();
    await expect(mediaLink).toHaveText("Media");

    await expect(apiLink).toBeVisible();
    await expect(apiLink).toHaveText("API");

    // Test API link functionality
    await apiLink.click();
    await page.waitForURL("**/admin/api.php");
    await expect(page.locator("h2")).toContainText("API");

    console.log("✅ Session-debug page has both Media and API links");
    console.log("[api-navigation-test] end - session-debug navigation verified");
  });

  test.afterEach(async ({ page }) => {
    // Clean up: logout
    try {
      await page.goto("http://localhost:8000/admin/logout.php");
    } catch (error) {
      // Ignore logout errors during cleanup
    }
  });
});
