const { test, expect } = require("@playwright/test");

test.describe("Permanent Remote Image Deletion", () => {
  test("should permanently delete remote images from post content", async ({ page }) => {
    console.log("Testing permanent remote image deletion...");

    // Set longer timeout for this test
    test.setTimeout(120000);

    // Navigate to login page
    await page.goto("http://localhost:8000/admin/login.php");
    await page.waitForLoadState("networkidle");

    // Login
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // First, create a test post with remote images
    console.log("Creating test post with remote images...");
    await page.goto("http://localhost:8000/admin/posts.php?action=new");
    await page.waitForLoadState("networkidle");

    // Fill the form with test data including remote images
    await page.fill('input[name="title"]', "Test Post for Remote Image Deletion");
    await page.fill('select[name="category"]', "tech");
    await page.fill('input[name="thumbnail_url"]', "https://picsum.photos/400/300?random=999");
    await page.fill('textarea[name="description"]', "Test post for testing permanent remote image deletion");

    // Add markdown content with remote images
    const testImageUrl = "https://picsum.photos/600/400?random=888";
    const markdownContent = `# Test Post for Deletion

This post contains a remote image that will be deleted:

![Test Image](${testImageUrl})

Some more content here.

<img src="https://picsum.photos/500/350?random=777" alt="HTML Image">

End of post.`;

    await page.fill('textarea[name="content"]', markdownContent);

    // Submit the form
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Navigate to media page
    console.log("Navigating to media page...");
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForLoadState("networkidle");

    // Get initial count of remote images
    const initialRemoteCount = await page
      .locator(".stat-item")
      .filter({ hasText: "Remote Images" })
      .locator(".stat-value")
      .textContent();
    console.log(`Initial remote images count: ${initialRemoteCount}`);

    // Find the remote image we want to delete
    const remoteItems = page.locator('.media-item[data-type="remote"]');
    const remoteItemCount = await remoteItems.count();
    expect(remoteItemCount).toBeGreaterThan(0);
    console.log(`Found ${remoteItemCount} remote images`);

    // Find the specific image we created (check data-url)
    let targetItem = null;
    for (let i = 0; i < remoteItemCount; i++) {
      const item = remoteItems.nth(i);
      const deleteBtn = item.locator(".delete-remote-btn");
      const dataUrl = await deleteBtn.getAttribute("data-url");
      if (dataUrl && dataUrl.includes("random=888")) {
        targetItem = item;
        console.log(`Found target image: ${dataUrl}`);
        break;
      }
    }

    if (!targetItem) {
      // If we can't find the specific image, use the first remote item
      targetItem = remoteItems.first();
      console.log("Using first remote image as target");
    }

    // Get the image details
    const imageName = await targetItem.locator(".media-name").textContent();
    const deleteBtn = targetItem.locator(".delete-remote-btn");

    console.log(`Target image name: ${imageName}`);

    // Verify delete button is present and has correct properties
    await expect(deleteBtn).toBeVisible();
    const buttonTitle = await deleteBtn.getAttribute("title");
    expect(buttonTitle).toContain("Delete permanently from posts");
    console.log("✅ Delete button found with correct title");

    // Set up dialog handler for confirmation
    const dialogPromise = page.waitForEvent("dialog");

    // Click delete button
    console.log("Clicking delete button...");
    await deleteBtn.click();

    // Handle the confirmation dialog
    const dialog = await dialogPromise;
    expect(dialog.type()).toBe("confirm");
    expect(dialog.message()).toContain("permanently delete");
    expect(dialog.message()).toContain("from all posts");
    expect(dialog.message()).toContain("cannot be undone");
    console.log(`Confirmation dialog: ${dialog.message()}`);
    await dialog.accept();

    // Wait for the action to complete
    await page.waitForLoadState("networkidle");

    // Check for success message
    const successAlert = page.locator(".alert-success");
    if (await successAlert.count() > 0) {
      const message = await successAlert.textContent();
      expect(message).toContain("permanently removed");
      console.log(`✅ Success message: ${message}`);
    }

    // Verify the remote image count decreased
    const newRemoteCount = await page
      .locator(".stat-item")
      .filter({ hasText: "Remote Images" })
      .locator(".stat-value")
      .textContent();
    console.log(`New remote images count: ${newRemoteCount}`);

    expect(parseInt(newRemoteCount)).toBeLessThan(parseInt(initialRemoteCount));
    console.log("✅ Remote image count decreased after deletion");

    // Verify the specific image is no longer in the list
    await page.reload();
    await page.waitForLoadState("networkidle");

    const remainingRemoteItems = page.locator('.media-item[data-type="remote"]');
    const remainingCount = await remainingRemoteItems.count();
    console.log(`Remaining remote images: ${remainingCount}`);

    // Check that our specific image is gone
    let imageStillExists = false;
    for (let i = 0; i < remainingCount; i++) {
      const item = remainingRemoteItems.nth(i);
      const deleteBtn = item.locator(".delete-remote-btn");
      const dataUrl = await deleteBtn.getAttribute("data-url");
      if (dataUrl && dataUrl.includes("random=888")) {
        imageStillExists = true;
        break;
      }
    }

    expect(imageStillExists).toBe(false);
    console.log("✅ Target image successfully removed from media list");

    // Verify the image was removed from the post content
    console.log("Verifying image was removed from post content...");
    await page.goto("http://localhost:8000/admin/posts.php");
    await page.waitForLoadState("networkidle");

    // Find our test post
    const postLinks = page.locator('a[href*="action=edit"]').filter({ hasText: "Test Post for Remote Image Deletion" });
    if (await postLinks.count() > 0) {
      await postLinks.first().click();
      await page.waitForLoadState("networkidle");

      // Check the content field
      const contentValue = await page.locator('textarea[name="content"]').inputValue();
      expect(contentValue).not.toContain("random=888");
      console.log("✅ Image reference removed from post content");

      // Check if thumbnail was removed (if it was the thumbnail)
      const thumbnailValue = await page.locator('input[name="thumbnail_url"]').inputValue();
      if (thumbnailValue) {
        expect(thumbnailValue).not.toContain("random=888");
        console.log("✅ Thumbnail reference checked");
      }
    }

    console.log("✅ Permanent remote image deletion test completed successfully!");
  });

  test("should handle deletion of remote images from multiple posts", async ({ page }) => {
    console.log("Testing remote image deletion from multiple posts...");

    // Login
    await page.goto("http://localhost:8000/admin/login.php");
    await page.waitForLoadState("networkidle");
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Create two posts with the same remote image
    const sharedImageUrl = "https://picsum.photos/800/600?random=123456";

    console.log("Creating first post with shared image...");
    await page.goto("http://localhost:8000/admin/posts.php?action=new");
    await page.waitForLoadState("networkidle");

    await page.fill('input[name="title"]', "First Post with Shared Image");
    await page.fill('select[name="category"]', "tech");
    await page.fill('textarea[name="description"]', "First post with shared remote image");
    await page.fill('textarea[name="content"]', `# First Post\n\n![Shared Image](${sharedImageUrl})\n\nFirst post content.`);
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    console.log("Creating second post with same shared image...");
    await page.goto("http://localhost:8000/admin/posts.php?action=new");
    await page.waitForLoadState("networkidle");

    await page.fill('input[name="title"]', "Second Post with Shared Image");
    await page.fill('select[name="category"]', "tech");
    await page.fill('textarea[name="description"]', "Second post with shared remote image");
    await page.fill('textarea[name="content"]', `# Second Post\n\n![Same Shared Image](${sharedImageUrl})\n\nSecond post content.`);
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Navigate to media page and delete the shared image
    console.log("Deleting shared remote image...");
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForLoadState("networkidle");

    // Find and delete the shared image
    const remoteItems = page.locator('.media-item[data-type="remote"]');
    const remoteItemCount = await remoteItems.count();

    let targetItem = null;
    for (let i = 0; i < remoteItemCount; i++) {
      const item = remoteItems.nth(i);
      const deleteBtn = item.locator(".delete-remote-btn");
      const dataUrl = await deleteBtn.getAttribute("data-url");
      if (dataUrl && dataUrl.includes("random=123456")) {
        targetItem = item;
        break;
      }
    }

    if (targetItem) {
      const deleteBtn = targetItem.locator(".delete-remote-btn");

      // Handle confirmation dialog
      const dialogPromise = page.waitForEvent("dialog");
      await deleteBtn.click();
      const dialog = await dialogPromise;
      await dialog.accept();
      await page.waitForLoadState("networkidle");

      // Check success message should mention multiple posts
      const successAlert = page.locator(".alert-success");
      if (await successAlert.count() > 0) {
        const message = await successAlert.textContent();
        console.log(`Success message: ${message}`);
        // Should mention that it was removed from multiple posts
        expect(message).toMatch(/removed from \d+ post/);
        console.log("✅ Success message indicates multiple posts updated");
      }

      // Verify both posts no longer contain the image
      console.log("Verifying image removed from both posts...");
      await page.goto("http://localhost:8000/admin/posts.php");
      await page.waitForLoadState("networkidle");

      // Check first post
      const firstPostLink = page.locator('a[href*="action=edit"]').filter({ hasText: "First Post with Shared Image" });
      if (await firstPostLink.count() > 0) {
        await firstPostLink.first().click();
        await page.waitForLoadState("networkidle");

        const firstPostContent = await page.locator('textarea[name="content"]').inputValue();
        expect(firstPostContent).not.toContain("random=123456");
        console.log("✅ Image removed from first post");
      }

      // Check second post
      await page.goto("http://localhost:8000/admin/posts.php");
      await page.waitForLoadState("networkidle");

      const secondPostLink = page.locator('a[href*="action=edit"]').filter({ hasText: "Second Post with Shared Image" });
      if (await secondPostLink.count() > 0) {
        await secondPostLink.first().click();
        await page.waitForLoadState("networkidle");

        const secondPostContent = await page.locator('textarea[name="content"]').inputValue();
        expect(secondPostContent).not.toContain("random=123456");
        console.log("✅ Image removed from second post");
      }

      console.log("✅ Multiple post deletion test completed successfully!");
    } else {
      console.log("⚠️ Could not find shared image to test deletion");
    }
  });

  test("should show proper confirmation dialog for remote deletion", async ({ page }) => {
    console.log("Testing remote deletion confirmation dialog...");

    // Login
    await page.goto("http://localhost:8000/admin/login.php");
    await page.waitForLoadState("networkidle");
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Navigate to media page
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForLoadState("networkidle");

    // Find remote images
    const remoteItems = page.locator('.media-item[data-type="remote"]');
    const remoteItemCount = await remoteItems.count();

    if (remoteItemCount > 0) {
      const firstRemoteItem = remoteItems.first();
      const imageName = await firstRemoteItem.locator(".media-name").textContent();
      const deleteBtn = firstRemoteItem.locator(".delete-remote-btn");

      // Set up dialog handler to check content and dismiss
      const dialogPromise = page.waitForEvent("dialog");

      await deleteBtn.click();

      const dialog = await dialogPromise;
      expect(dialog.type()).toBe("confirm");

      const message = dialog.message();
      expect(message).toContain("permanently delete");
      expect(message).toContain("from all posts");
      expect(message).toContain("cannot be undone");
      expect(message).toContain(imageName.trim());

      console.log(`✅ Confirmation dialog contains proper warnings: ${message}`);

      // Dismiss to avoid actual deletion
      await dialog.dismiss();
      console.log("✅ Remote deletion confirmation dialog test completed");
    } else {
      console.log("⚠️ No remote images found to test confirmation dialog");
    }
  });

  test("should update button styling for permanent deletion", async ({ page }) => {
    console.log("Testing button styling for permanent remote deletion...");

    // Login
    await page.goto("http://localhost:8000/admin/login.php");
    await page.waitForLoadState("networkidle");
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Navigate to media page
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForLoadState("networkidle");

    // Check remote image buttons
    const remoteItems = page.locator('.media-item[data-type="remote"]');
    const remoteItemCount = await remoteItems.count();

    if (remoteItemCount > 0) {
      const deleteRemoteBtn = remoteItems.first().locator(".delete-remote-btn");

      // Check button properties
      await expect(deleteRemoteBtn).toBeVisible();

      const buttonText = await deleteRemoteBtn.textContent();
      expect(buttonText).toContain("Delete");
      expect(buttonText).toContain("🗑️");
      console.log(`✅ Remote delete button text: ${buttonText}`);

      const buttonTitle = await deleteRemoteBtn.getAttribute("title");
      expect(buttonTitle).toContain("Delete permanently from posts");
      console.log(`✅ Remote delete button title: ${buttonTitle}`);

      // Check that it has the correct class
      const hasCorrectClass = await deleteRemoteBtn.evaluate(el => el.classList.contains('delete-remote-btn'));
      expect(hasCorrectClass).toBe(true);
      console.log("✅ Remote delete button has correct CSS class");

      // Test hover effect
      await deleteRemoteBtn.hover();
      await page.waitForTimeout(500);

      const hoverStyles = await deleteRemoteBtn.evaluate(el => {
        const styles = window.getComputedStyle(el);
        return {
          backgroundColor: styles.backgroundColor,
          borderColor: styles.borderColor,
          color: styles.color
        };
      });

      console.log("Remote delete button hover styles:", hoverStyles);
      console.log("✅ Remote delete button hover effect tested");

    } else {
      console.log("⚠️ No remote images found to test button styling");
    }

    console.log("✅ Button styling test completed!");
  });
});
