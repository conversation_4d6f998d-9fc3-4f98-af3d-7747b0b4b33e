const { test, expect } = require('@playwright/test');

test.describe('Remote Images in Media Page', () => {
    test('should display remote images from post content', async ({ page }) => {
        console.log('Testing remote images display in media page...');

        // Set longer timeout for this test
        test.setTimeout(90000);

        // Navigate to login page
        await page.goto('http://localhost:8000/admin/login.php');
        await page.waitForLoadState('networkidle');

        // Fill login form (assuming default credentials)
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin');

        // Submit login form
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // First, create a post with remote images to test with
        console.log('Creating test post with remote images...');
        await page.goto('http://localhost:8000/admin/posts.php?action=new');
        await page.waitForLoadState('networkidle');

        // Fill the form with test data including remote images
        await page.fill('input[name="title"]', 'Test Post with Remote Images');
        await page.fill('select[name="category"]', 'tech');
        await page.fill('input[name="thumbnail_url"]', 'https://picsum.photos/400/300?random=1');
        await page.fill('textarea[name="description"]', 'Test post with remote images for media page testing');

        // Add markdown content with remote images
        const markdownContent = `# Test Post with Remote Images

This is a test post containing remote images:

![Remote Image 1](https://picsum.photos/600/400?random=2)

Some text here.

![Remote Image 2](https://picsum.photos/800/600?random=3)

And some HTML image:
<img src="https://picsum.photos/500/350?random=4" alt="HTML Remote Image">

More content here.`;

        await page.fill('textarea[name="content"]', markdownContent);

        // Submit the form
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Now navigate to media page to test remote image display
        console.log('Navigating to media page...');
        await page.goto('http://localhost:8000/admin/media.php');
        await page.waitForLoadState('networkidle');

        // Check that we're on the correct page
        await expect(page.locator('h2')).toHaveText('Media Management');
        console.log('✅ On media management page');

        // Wait a bit for remote images to load
        await page.waitForTimeout(3000);

        // Check for remote image statistics
        const remoteCountElement = page.locator('.stat-item').filter({ hasText: 'Remote Images' });
        const remoteCount = await remoteCountElement.locator('.stat-value').textContent();
        console.log(`Remote images count: ${remoteCount}`);

        if (parseInt(remoteCount) > 0) {
            console.log('✅ Remote images detected in statistics');

            // Check for remote image items in the grid
            const remoteItems = page.locator('.media-item[data-type="remote"]');
            const remoteItemCount = await remoteItems.count();
            console.log(`Found ${remoteItemCount} remote image items`);

            expect(remoteItemCount).toBeGreaterThan(0);
            console.log('✅ Remote image items found in media grid');

            // Check first remote image item details
            if (remoteItemCount > 0) {
                const firstRemoteItem = remoteItems.first();

                // Check for remote badge
                await expect(firstRemoteItem.locator('.badge-remote')).toBeVisible();
                console.log('✅ Remote badge visible');

                // Check for globe icon indicator
                await expect(firstRemoteItem.locator('.media-preview::before')).toBeVisible().catch(() => {
                    console.log('Globe icon indicator not visible (CSS pseudo-element)');
                });

                // Check for source information (post title)
                const sourceInfo = firstRemoteItem.locator('.detail-row').filter({ hasText: 'Source' });
                if (await sourceInfo.count() > 0) {
                    console.log('✅ Source information displayed');
                } else {
                    console.log('❌ Source information not found');
                }

                // Check for copy URL functionality
                const copyBtn = firstRemoteItem.locator('.copy-btn');
                await expect(copyBtn).toBeVisible();
                console.log('✅ Copy URL button available for remote images');

                // Check for view functionality
                const viewBtn = firstRemoteItem.locator('.view-btn');
                await expect(viewBtn).toBeVisible();
                console.log('✅ View button available for remote images');

                // Test copy functionality
                await copyBtn.click();
                await page.waitForTimeout(1000);
                console.log('✅ Copy button clicked (functionality tested)');
            }

            // Check that remote images have different styling
            const remoteItemBorder = await remoteItems.first().evaluate(el => {
                return window.getComputedStyle(el).borderLeftColor;
            });
            console.log(`Remote item border color: ${remoteItemBorder}`);

        } else {
            console.log('⚠️ No remote images found - may need to create test data');
        }

        // Check console for any errors
        const consoleErrors = [];
        page.on('console', msg => {
            if (msg.type() === 'error') {
                consoleErrors.push(msg.text());
            }
        });

        // Wait to catch any delayed errors
        await page.waitForTimeout(2000);

        if (consoleErrors.length === 0) {
            console.log('✅ No JavaScript console errors found');
        } else {
            console.log('❌ Console errors found:', consoleErrors);
        }

        // Take a screenshot for visual verification
        await page.screenshot({
            path: 'test-results/remote-images-media-page.png',
            fullPage: true
        });
        console.log('📸 Screenshot saved to test-results/remote-images-media-page.png');

        console.log('✅ Remote images test completed successfully!');
    });

    test('should handle remote image loading errors gracefully', async ({ page }) => {
        console.log('Testing remote image error handling...');

        // Navigate to login page
        await page.goto('http://localhost:8000/admin/login.php');
        await page.waitForLoadState('networkidle');

        // Login
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin');
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Create a post with broken remote image URLs
        console.log('Creating test post with broken remote images...');
        await page.goto('http://localhost:8000/admin/posts.php?action=new');
        await page.waitForLoadState('networkidle');

        await page.fill('input[name="title"]', 'Test Post with Broken Remote Images');
        await page.fill('select[name="category"]', 'tech');
        await page.fill('input[name="thumbnail_url"]', 'https://nonexistent-domain-12345.com/image.jpg');
        await page.fill('textarea[name="description"]', 'Test post with broken remote images');

        const brokenMarkdownContent = `# Test Post with Broken Images

![Broken Image 1](https://broken-url-12345.com/image1.jpg)
![Broken Image 2](https://another-broken-url-67890.com/image2.png)`;

        await page.fill('textarea[name="content"]', brokenMarkdownContent);
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Navigate to media page
        await page.goto('http://localhost:8000/admin/media.php');
        await page.waitForLoadState('networkidle');

        // Wait for images to attempt loading and potentially fail
        await page.waitForTimeout(5000);

        // Check for error placeholders
        const errorPlaceholders = page.locator('.media-error');
        const errorCount = await errorPlaceholders.count();

        console.log(`Found ${errorCount} error placeholders`);

        if (errorCount > 0) {
            console.log('✅ Error handling working - placeholders shown for broken images');

            // Check error message content
            await expect(errorPlaceholders.first().locator('.error-text')).toHaveText('Image unavailable');
            console.log('✅ Error message displayed correctly');

            // Check error icon
            await expect(errorPlaceholders.first().locator('.error-icon')).toBeVisible();
            console.log('✅ Error icon displayed');
        } else {
            console.log('⚠️ No error placeholders found - images may have loaded or error handling needs improvement');
        }

        console.log('✅ Remote image error handling test completed!');
    });

    test('should show correct statistics for local vs remote images', async ({ page }) => {
        console.log('Testing local vs remote image statistics...');

        // Login
        await page.goto('http://localhost:8000/admin/login.php');
        await page.waitForLoadState('networkidle');
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin');
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Navigate to media page
        await page.goto('http://localhost:8000/admin/media.php');
        await page.waitForLoadState('networkidle');

        // Get statistics
        const totalFiles = await page.locator('.stat-item').filter({ hasText: 'Total Files' }).locator('.stat-value').textContent();
        const localFiles = await page.locator('.stat-item').filter({ hasText: 'Local Files' }).locator('.stat-value').textContent();
        const remoteImages = await page.locator('.stat-item').filter({ hasText: 'Remote Images' }).locator('.stat-value').textContent();
        const storageUsed = await page.locator('.stat-item').filter({ hasText: 'Storage Used' }).locator('.stat-value').textContent();

        console.log(`Statistics:
        - Total Files: ${totalFiles}
        - Local Files: ${localFiles}
        - Remote Images: ${remoteImages}
        - Storage Used: ${storageUsed}`);

        // Verify that total = local + remote
        const expectedTotal = parseInt(localFiles) + parseInt(remoteImages);
        expect(parseInt(totalFiles)).toBe(expectedTotal);
        console.log('✅ Statistics calculation correct');

        // Storage should only count local files
        expect(storageUsed).not.toBe('0 B'); // Assuming there are some local files
        console.log('✅ Storage calculation excludes remote images');

        console.log('✅ Statistics verification completed!');
    });
});
