const { test, expect } = require('@playwright/test');

test.describe('Media Page Fix Test', () => {
    test('should load media page without errors after login', async ({ page }) => {
        console.log('Testing media page fix after login...');

        // Set longer timeout for this test
        test.setTimeout(90000);

        // Navigate to login page
        await page.goto('http://localhost:8000/admin/login.php');
        await page.waitForLoadState('networkidle');

        // Fill login form (assuming default credentials)
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin');

        // Submit login form
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');

        // Navigate directly to media page
        console.log('Navigating to media page...');
        await page.goto('http://localhost:8000/admin/media.php');
        await page.waitForLoadState('networkidle');

        // Check that we're not redirected to an error page
        const currentUrl = page.url();
        console.log('Current URL:', currentUrl);
        expect(currentUrl).toContain('/admin/media.php');

        // Check for the presence of expected elements
        await expect(page.locator('h2')).toHaveText('Media Management');
        console.log('✅ Page title "Media Management" found');

        // Check that the admin header is present
        await expect(page.locator('.admin-header')).toBeVisible();
        console.log('✅ Admin header structure found');

        // Check that flash messages section exists (even if empty)
        const flashMessagesExist = await page.locator('.alert').count() >= 0;
        console.log('✅ Flash messages handling working (no errors)');

        // Check for stats section
        await expect(page.locator('.admin-stats')).toBeVisible();
        console.log('✅ Statistics section found');

        // Check for either media grid or empty state
        const hasMediaGrid = await page.locator('.media-grid').isVisible();
        const hasEmptyState = await page.locator('.empty-state').isVisible();

        if (hasMediaGrid) {
            console.log('✅ Media grid found - files are present');
        } else if (hasEmptyState) {
            console.log('✅ Empty state found - no files uploaded yet');
        } else {
            console.log('❌ Neither media grid nor empty state found');
        }

        // Check console for any JavaScript errors
        const consoleErrors = [];
        page.on('console', msg => {
            if (msg.type() === 'error') {
                consoleErrors.push(msg.text());
            }
        });

        // Wait a bit to catch any delayed errors
        await page.waitForTimeout(2000);

        if (consoleErrors.length === 0) {
            console.log('✅ No JavaScript console errors found');
        } else {
            console.log('❌ Console errors found:', consoleErrors);
        }

        // Take a screenshot for visual verification
        await page.screenshot({
            path: 'test-results/media-page-fix-verification.png',
            fullPage: true
        });
        console.log('📸 Screenshot saved to test-results/media-page-fix-verification.png');

        console.log('✅ Media page test completed successfully!');
    });

    test('should handle media page without authentication redirect', async ({ page }) => {
        console.log('Testing media page authentication redirect...');

        // Try to access media page without login
        await page.goto('http://localhost:8000/admin/media.php');
        await page.waitForLoadState('networkidle');

        // Should be redirected to login page
        const currentUrl = page.url();
        expect(currentUrl).toContain('login.php');
        console.log('✅ Proper authentication redirect working');
    });
});
