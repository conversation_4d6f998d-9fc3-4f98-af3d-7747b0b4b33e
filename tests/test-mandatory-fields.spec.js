const { test, expect } = require("@playwright/test");

test.describe("Mandatory Fields Validation", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto("/admin/login.php");

    // Login with admin credentials
    await page.fill("#username", "admin");
    await page.fill("#password", "admin");
    await page.click('button[type="submit"]');

    // Wait for redirect to dashboard (could be dashboard.php or index.php)
    await page.waitForURL("**/admin/**");

    // Verify we're logged in by checking for admin navigation
    await page.waitForSelector(".admin-nav", { timeout: 5000 });

    // Navigate to new post page
    await page.goto("/admin/posts.php?action=new");
    await page.waitForLoadState("networkidle");
  });

  test("should show validation errors for missing required fields", async ({
    page,
  }) => {
    console.log(
      "[test-mandatory-fields] Testing validation for missing required fields",
    );

    // Try to submit form without filling required fields
    await page.click('button[type="submit"]');

    // Check that browser validation prevents submission for title (first required field)
    const titleField = page.locator("#title");
    const titleValidationMessage = await titleField.evaluate(
      (el) => el.validationMessage,
    );
    expect(titleValidationMessage).toBeTruthy();

    console.log("[test-mandatory-fields] Title validation working correctly");
  });

  test("should show alert for missing description", async ({ page }) => {
    console.log("[test-mandatory-fields] Testing description requirement");

    // Fill title and content but leave description empty
    await page.fill("#title", "Test Post Title");
    await page.fill("#content", "This is test content for the post.");
    await page.selectOption("#category", "tech");

    // Add a thumbnail URL
    await page.click('button[data-tab="url"]');
    await page.fill("#thumbnail_url", "https://picsum.photos/800/600");

    // Remove the required attribute from description to test JavaScript validation
    await page.evaluate(() => {
      document.getElementById("description").removeAttribute("required");
    });

    // Set up dialog handler before triggering the action
    let alertMessage = "";
    page.on("dialog", async (dialog) => {
      alertMessage = dialog.message();
      await dialog.accept();
    });

    // Try to submit without description
    await page.click('button[type="submit"]');

    // Wait a bit for the alert to be handled
    await page.waitForTimeout(500);

    expect(alertMessage).toBe("Description is required.");
    console.log(
      "[test-mandatory-fields] Description validation alert working correctly",
    );
  });

  test("should show alert for missing thumbnail", async ({ page }) => {
    console.log("[test-mandatory-fields] Testing thumbnail requirement");

    // Fill all fields except thumbnail
    await page.fill("#title", "Test Post Title");
    await page.fill("#description", "This is a test description for the post");
    await page.fill("#content", "This is test content for the post.");
    await page.selectOption("#category", "tech");

    // Set up dialog handler before triggering the action
    let alertMessage = "";
    page.on("dialog", async (dialog) => {
      alertMessage = dialog.message();
      await dialog.accept();
    });

    // Try to submit without thumbnail
    await page.click('button[type="submit"]');

    // Wait a bit for the alert to be handled
    await page.waitForTimeout(500);

    expect(alertMessage).toBe(
      "Thumbnail is required. Please upload an image or provide a URL.",
    );
    console.log(
      "[test-mandatory-fields] Thumbnail validation alert working correctly",
    );
  });

  test("should verify description is text input not textarea", async ({
    page,
  }) => {
    console.log("[test-mandatory-fields] Verifying description field type");

    const descriptionField = page.locator("#description");

    // Check that it's an input element
    const tagName = await descriptionField.evaluate((el) =>
      el.tagName.toLowerCase(),
    );
    expect(tagName).toBe("input");

    // Check that it's type="text"
    const inputType = await descriptionField.getAttribute("type");
    expect(inputType).toBe("text");

    // Check that it has required attribute
    const isRequired = await descriptionField.getAttribute("required");
    expect(isRequired).not.toBeNull();

    console.log(
      "[test-mandatory-fields] Description field is correctly configured as required text input",
    );
  });

  test("should verify character counter works with text input", async ({
    page,
  }) => {
    console.log(
      "[test-mandatory-fields] Testing character counter functionality",
    );

    const descriptionField = page.locator("#description");
    const counter = page.locator("#description-counter");

    // Initially should show 0/500
    await expect(counter).toHaveText("0/500 characters");

    // Type some text
    const testText = "This is a test description";
    await descriptionField.fill(testText);

    // Counter should update
    const expectedCount = `${testText.length}/500 characters`;
    await expect(counter).toHaveText(expectedCount);

    // Test color changes at different thresholds
    // Normal (gray) - under 70%
    let counterColor = await counter.evaluate(
      (el) => getComputedStyle(el).color,
    );
    expect(counterColor).toBe("rgb(108, 117, 125)"); // Bootstrap's text-muted gray

    // Warning (orange) - 70-90% of limit
    const warningText = "A".repeat(400); // 80% of 500
    await descriptionField.fill(warningText);
    await page.waitForTimeout(100);
    counterColor = await counter.evaluate((el) => getComputedStyle(el).color);
    expect(counterColor).toBe("rgb(253, 126, 20)"); // Bootstrap's orange

    // Danger (red) - over 90% of limit
    const dangerText = "A".repeat(475); // 95% of 500
    await descriptionField.fill(dangerText);
    await page.waitForTimeout(100);
    counterColor = await counter.evaluate((el) => getComputedStyle(el).color);
    expect(counterColor).toBe("rgb(220, 53, 69)"); // Bootstrap's red

    console.log(
      "[test-mandatory-fields] Character counter working correctly with color indicators",
    );
  });

  test("should successfully create post with all required fields", async ({
    page,
  }) => {
    console.log("[test-mandatory-fields] Testing successful post creation");

    // Fill all required fields
    const postTitle = `Test Post ${Date.now()}`;
    await page.fill("#title", postTitle);
    await page.fill("#description", "This is a test description for the post");
    await page.fill(
      "#content",
      "This is test content for the post with all required fields filled.",
    );
    await page.selectOption("#category", "tech");

    // Add thumbnail URL
    await page.click('button[data-tab="url"]');
    await page.fill("#thumbnail_url", "https://picsum.photos/800/600");

    // Submit the form
    await page.click('button[type="submit"]');

    // Should redirect to posts edit page (posts.php?action=edit&id=X)
    await page.waitForURL("**/admin/posts.php?action=edit*");

    // Verify success message or post appears in list
    const pageContent = await page.textContent("body");
    expect(pageContent).toContain(postTitle);

    console.log(
      "[test-mandatory-fields] Post created successfully with all required fields",
    );
  });

  test("should verify labels show asterisks for required fields", async ({
    page,
  }) => {
    console.log(
      "[test-mandatory-fields] Verifying required field labels have asterisks",
    );

    // Check title label
    const titleLabel = page.locator('label[for="title"]');
    await expect(titleLabel).toHaveText("Title *");

    // Check description label
    const descriptionLabel = page.locator('label[for="description"]');
    await expect(descriptionLabel).toHaveText("Description *");

    // Check thumbnail label
    const thumbnailLabel = page
      .locator("label")
      .filter({ hasText: "Thumbnail Image *" });
    await expect(thumbnailLabel).toBeVisible();

    console.log(
      "[test-mandatory-fields] All required field labels correctly show asterisks",
    );
  });
});
