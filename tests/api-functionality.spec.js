const { test, expect } = require("@playwright/test");

test.describe("API Functionality Tests", () => {
  let adminToken;
  let baseURL;
  let createdPostIds = [];

  test.beforeEach(async ({ page, baseURL: testBaseURL }) => {
    console.log("[API Test] Starting beforeEach setup");
    baseURL = testBaseURL || "http://localhost:8000";

    // Login as admin to get API token
    await page.goto("/admin/login.php");
    await page.fill("#username", "admin");
    await page.fill("#password", "admin");
    await page.click('button[type="submit"]');

    // Wait for redirect to dashboard
    await expect(page).toHaveURL(/.*admin\/index\.php/);
    console.log("[API Test] Successfully logged in as admin");

    // Navigate to profile page to get API token
    await page.click('a[href="profile.php"]');
    await expect(page).toHaveURL(/.*admin\/profile\.php/);

    // Get the API token
    const tokenInput = page.locator("#api_token");
    await expect(tokenInput).toBeVisible();
    adminToken = await tokenInput.inputValue();

    expect(adminToken).toBeTruthy();
    expect(adminToken.length).toBe(64); // 64 character hex string
    console.log(
      "[API Test] Retrieved API token:",
      adminToken.substring(0, 10) + "...",
    );
  });

  test.afterEach(async ({ request }) => {
    // Clean up test posts using cleanup script
    try {
      console.log("[API Test] Running test cleanup...");

      const response = await request.post("/test-cleanup.php", {
        headers: {
          Authorization: `Bearer ${adminToken}`,
          "Content-Type": "application/json",
        },
      });

      if (response.ok()) {
        const result = await response.json();
        console.log("[API Test] Cleanup result:", result.message);
        if (result.deleted_count > 0) {
          console.log("[API Test] Deleted", result.deleted_count, "test posts");
        }
      } else {
        console.log(
          "[API Test] Cleanup failed with status:",
          response.status(),
        );
      }
    } catch (error) {
      console.log("[API Test] Cleanup error:", error.message);
    }

    // Clear tracking array
    createdPostIds = [];
  });

  test("API link should be visible in navigation", async ({ page }) => {
    console.log("[API Test] Testing navigation link visibility");

    // Go to dashboard
    await page.goto("/admin/index.php");

    // Check if API link is visible in navigation
    const apiLink = page.locator('a[href="api.php"]');
    await expect(apiLink).toBeVisible();

    // Click the API link
    await apiLink.click();
    await expect(page).toHaveURL(/.*admin\/api\.php/);

    // Check if API documentation page loads
    await expect(page.locator("h2")).toContainText("REST API Documentation");
    console.log("[API Test] ✓ API navigation link working correctly");
  });

  test("Profile page should display API token", async ({ page }) => {
    console.log("[API Test] Testing API token display in profile");

    await page.goto("/admin/profile.php");

    // Check if API Token section exists
    const apiTokenSection = page.locator(".api-token-section");
    await expect(apiTokenSection).toBeVisible();

    // Check token input field
    const tokenInput = page.locator("#api_token");
    await expect(tokenInput).toBeVisible();
    await expect(tokenInput).toHaveAttribute("readonly");

    const tokenValue = await tokenInput.inputValue();
    expect(tokenValue).toBeTruthy();
    expect(tokenValue.length).toBe(64);

    // Check copy button
    const copyButton = page.locator("#copy_token");
    await expect(copyButton).toBeVisible();
    await expect(copyButton).toContainText("Copy Token");

    // Check regenerate button
    const regenerateButton = page.locator(
      'button[type="submit"]:has-text("Regenerate Token")',
    );
    await expect(regenerateButton).toBeVisible();

    console.log("[API Test] ✓ API token displayed correctly in profile");
  });

  test("Token regeneration should work", async ({ page }) => {
    console.log("[API Test] Testing token regeneration");

    await page.goto("/admin/profile.php");

    // Get original token
    const originalToken = await page.locator("#api_token").inputValue();

    // Handle the confirmation dialog
    page.on("dialog", async (dialog) => {
      expect(dialog.type()).toBe("confirm");
      expect(dialog.message()).toContain("regenerate your API token");
      await dialog.accept();
    });

    // Click regenerate button
    await page.click('button[type="submit"]:has-text("Regenerate Token")');

    // Wait for page to reload
    await page.waitForLoadState("networkidle");

    // Check for success message
    const successMessage = page.locator(".alert-success");
    await expect(successMessage).toBeVisible();
    await expect(successMessage).toContainText(
      "API token regenerated successfully",
    );

    // Get new token
    const newToken = await page.locator("#api_token").inputValue();

    // Verify token changed
    expect(newToken).toBeTruthy();
    expect(newToken).not.toBe(originalToken);
    expect(newToken.length).toBe(64);

    console.log("[API Test] ✓ Token regeneration working correctly");
    console.log(
      "[API Test] Original token:",
      originalToken.substring(0, 10) + "...",
    );
    console.log("[API Test] New token:", newToken.substring(0, 10) + "...");
  });

  test("API endpoint should create post with valid token", async ({
    request,
  }) => {
    console.log("[API Test] Testing post creation via API");

    const postData = {
      title: "THISISATEST API Post Creation Functionality",
      content:
        "This is a test post created via the REST API. **Markdown** formatting should work!",
      description: "A test post created via the REST API for testing purposes",
      thumbnail_url:
        "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop",
    };

    console.log("[API Test] Sending POST request with data:", postData);

    const response = await request.post("/api/posts.php", {
      headers: {
        Authorization: `Bearer ${adminToken}`,
        "Content-Type": "application/json",
      },
      data: postData,
    });

    console.log("[API Test] Response status:", response.status());

    expect(response.status()).toBe(201);

    const responseData = await response.json();
    console.log("[API Test] Response data:", responseData);

    expect(responseData.success).toBe(true);
    expect(responseData.id).toBeTruthy();
    expect(responseData.slug).toBeTruthy();
    expect(responseData.message).toBe("Post created successfully");

    // Track post for cleanup (cleanup script will find test posts by title patterns)
    createdPostIds.push(responseData.id);

    console.log("[API Test] ✓ Post created successfully via API");
    console.log("[API Test] Post ID:", responseData.id);
    console.log("[API Test] Post slug:", responseData.slug);
  });

  test("API endpoint should reject invalid token", async ({ request }) => {
    console.log("[API Test] Testing API with invalid token");

    const postData = {
      title: "THISISATEST Invalid Token Rejection",
      content: "This should fail with invalid token",
      description: "Test description",
      thumbnail_url: "https://example.com/thumbnail.jpg",
    };

    const response = await request.post("/api/posts.php", {
      headers: {
        Authorization: "Bearer invalid_token_here",
        "Content-Type": "application/json",
      },
      data: postData,
    });

    expect(response.status()).toBe(401);

    const responseData = await response.json();
    expect(responseData.error).toBe("Invalid token");

    console.log("[API Test] ✓ Invalid token correctly rejected");
  });

  test("API endpoint should reject missing token", async ({ request }) => {
    console.log("[API Test] Testing API without token");

    const postData = {
      title: "THISISATEST Missing Token Rejection",
      content: "This should fail without token",
      description: "Test description",
      thumbnail_url: "https://example.com/thumbnail.jpg",
    };

    const response = await request.post("/api/posts.php", {
      headers: {
        "Content-Type": "application/json",
      },
      data: postData,
    });

    expect(response.status()).toBe(401);

    const responseData = await response.json();
    expect(responseData.error).toBe("No authorization header provided");

    console.log("[API Test] ✓ Missing token correctly rejected");
  });

  test("API endpoint should reject missing required fields", async ({
    request,
  }) => {
    console.log("[API Test] Testing API with missing fields");

    // Test missing title
    let response = await request.post("/api/posts.php", {
      headers: {
        Authorization: `Bearer ${adminToken}`,
        "Content-Type": "application/json",
      },
      data: {
        content: "Content without title",
        description: "Test description",
        thumbnail_url: "https://example.com/thumbnail.jpg",
      },
    });

    expect(response.status()).toBe(400);
    let responseData = await response.json();
    expect(responseData.error).toContain("Missing required fields");

    // Test missing content
    response = await request.post("/api/posts.php", {
      headers: {
        Authorization: `Bearer ${adminToken}`,
        "Content-Type": "application/json",
      },
      data: {
        title: "Title without content",
        description: "Test description",
        thumbnail_url: "https://example.com/thumbnail.jpg",
      },
    });

    expect(response.status()).toBe(400);
    responseData = await response.json();
    expect(responseData.error).toContain(
      "Missing required fields: title, content, description, and thumbnail_url are required",
    );

    console.log("[API Test] ✓ Missing required fields correctly rejected");
  });

  test("API endpoint should reject empty fields", async ({ request }) => {
    console.log("[API Test] Testing API with empty fields");

    // Test empty title
    let response = await request.post("/api/posts.php", {
      headers: {
        Authorization: `Bearer ${adminToken}`,
        "Content-Type": "application/json",
      },
      data: {
        title: "",
        content: "Content with empty title",
        description: "Test description",
        thumbnail_url: "https://example.com/thumbnail.jpg",
      },
    });

    expect(response.status()).toBe(400);
    let responseData = await response.json();
    expect(responseData.error).toBe("Title cannot be empty");

    // Test empty content
    response = await request.post("/api/posts.php", {
      headers: {
        Authorization: `Bearer ${adminToken}`,
        "Content-Type": "application/json",
      },
      data: {
        title: "Title with empty content",
        content: "",
        description: "Test description",
        thumbnail_url: "https://example.com/thumbnail.jpg",
      },
    });

    expect(response.status()).toBe(400);
    responseData = await response.json();
    expect(responseData.error).toBe("Content cannot be empty");

    console.log("[API Test] ✓ Empty fields correctly rejected");
  });

  test("API endpoint should handle invalid JSON", async ({ request }) => {
    console.log("[API Test] Testing API with invalid JSON");

    const response = await request.post("/api/posts.php", {
      headers: {
        Authorization: `Bearer ${adminToken}`,
        "Content-Type": "application/json",
      },
      data: "invalid json data",
    });

    expect(response.status()).toBe(400);

    const responseData = await response.json();
    expect(responseData.error).toBe("Invalid JSON in request body");

    console.log("[API Test] ✓ Invalid JSON correctly rejected");
  });

  test("API endpoint should reject non-POST methods", async ({ request }) => {
    console.log("[API Test] Testing API with non-POST methods");

    // Test GET method
    let response = await request.get("/api/posts.php", {
      headers: {
        Authorization: `Bearer ${adminToken}`,
      },
    });

    expect(response.status()).toBe(405);
    let responseData = await response.json();
    expect(responseData.error).toContain("Method not allowed");

    // Test PUT method
    response = await request.put("/api/posts.php", {
      headers: {
        Authorization: `Bearer ${adminToken}`,
        "Content-Type": "application/json",
      },
      data: {
        title: "Test",
        content: "Test",
      },
    });

    expect(response.status()).toBe(405);
    responseData = await response.json();
    expect(responseData.error).toContain("Method not allowed");

    console.log("[API Test] ✓ Non-POST methods correctly rejected");
  });

  test("Test interface should work correctly", async ({ page }) => {
    console.log("[API Test] Testing the test interface");

    await page.goto("/test.html");

    // Check if test interface loads
    await expect(page.locator("h1")).toContainText("API Test Interface");

    // Fill in form
    await page.fill("#api_token", adminToken);
    await page.fill("#post_title", "Test from Browser Interface");
    await page.fill(
      "#post_content",
      "This post was created using the browser test interface.",
    );
    await page.fill(
      "#post_description",
      "A test post created using the browser test interface.",
    );
    await page.fill(
      "#post_thumbnail",
      "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop",
    );

    // Click send button
    await page.click("#send_button");

    // Wait for response
    await page.waitForTimeout(3000);

    // Check response
    const responseDiv = page.locator("#response");
    await expect(responseDiv).toHaveClass(/success/);

    const responseText = await responseDiv.textContent();
    expect(responseText).toContain("Success!");
    expect(responseText).toContain('"success": true');

    // Extract post ID from response for cleanup (cleanup script will find test posts by title patterns)
    const idMatch = responseText.match(/"id":\s*(\d+)/);
    if (idMatch) {
      createdPostIds.push(parseInt(idMatch[1]));
    }

    console.log("[API Test] ✓ Test interface working correctly");
  });

  test("Created post should be visible in admin panel", async ({
    page,
    request,
  }) => {
    console.log("[API Test] Testing post visibility after API creation");

    // Create post via API first
    const postData = {
      title: "API Test Post for Verification",
      content:
        "This post was created via API and should be visible in the admin panel.",
      description: "API test post for verification in admin panel",
      thumbnail_url:
        "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop",
    };

    const response = await request.post("/api/posts.php", {
      headers: {
        Authorization: `Bearer ${adminToken}`,
        "Content-Type": "application/json",
      },
      data: postData,
    });

    expect(response.status()).toBe(201);
    const responseData = await response.json();
    const postId = responseData.id;

    // Track post for cleanup (cleanup script will find test posts by title patterns)
    createdPostIds.push(postId);

    console.log("[API Test] Created post ID:", postId);

    // Now check if post is visible in admin panel
    await page.goto("/admin/posts.php");

    // Look for the post in the posts list
    const postTitle = page.locator(`text="${postData.title}"`);
    await expect(postTitle).toBeVisible();

    // Check if post is published (not draft)
    const postRow = page.locator(`tr:has-text("${postData.title}")`);
    const statusBadge = postRow.locator(".status-badge");
    await expect(statusBadge).toContainText("Published");

    console.log("[API Test] ✓ API-created post visible in admin panel");
  });
});

test.describe("API Security Tests", () => {
  test("API should have proper CORS headers", async ({ request }) => {
    console.log("[API Test] Testing CORS headers");

    const response = await request.post("/api/posts.php", {
      headers: {
        Authorization: "Bearer invalid_token",
        "Content-Type": "application/json",
      },
      data: {
        title: "Test",
        content: "Test",
      },
    });

    const headers = response.headers();
    expect(headers["access-control-allow-origin"]).toBe("*");
    expect(headers["access-control-allow-methods"]).toContain("POST");
    expect(headers["access-control-allow-headers"]).toContain("Content-Type");
    expect(headers["access-control-allow-headers"]).toContain("Authorization");

    console.log("[API Test] ✓ CORS headers properly configured");
  });

  test("API should handle OPTIONS preflight request", async ({ request }) => {
    console.log("[API Test] Testing OPTIONS preflight request");

    const response = await request.fetch("/api/posts.php", {
      method: "OPTIONS",
    });

    expect(response.status()).toBe(200);

    const headers = response.headers();
    expect(headers["access-control-allow-origin"]).toBe("*");
    expect(headers["access-control-allow-methods"]).toContain("POST");

    console.log("[API Test] ✓ OPTIONS preflight request handled correctly");
  });
});

test.describe("API Documentation Tests", () => {
  test("API documentation page should be complete", async ({ page }) => {
    console.log("[API Test] Testing API documentation completeness");

    // Login first
    await page.goto("/admin/login.php");
    await page.fill("#username", "admin");
    await page.fill("#password", "admin");
    await page.click('button[type="submit"]');

    // Go to API documentation
    await page.goto("/admin/api.php");

    // Check main sections
    await expect(page.locator('h3:has-text("Authentication")')).toBeVisible();
    await expect(page.locator('h3:has-text("Base URL")')).toBeVisible();
    await expect(
      page.locator('h3:has-text("Available Endpoints")'),
    ).toBeVisible();
    await expect(page.locator('h3:has-text("Usage Examples")')).toBeVisible();
    await expect(
      page.locator('h3:has-text("Security Best Practices")'),
    ).toBeVisible();

    // Check if API token is displayed
    const tokenHighlight = page.locator(".token-highlight");
    await expect(tokenHighlight).toBeVisible();

    const tokenText = await tokenHighlight.textContent();
    expect(tokenText).toBeTruthy();
    expect(tokenText.length).toBeGreaterThan(10);

    // Check examples
    await expect(page.locator("text=curl -X POST")).toBeVisible();
    await expect(page.locator("text=fetch(")).toBeVisible();
    await expect(page.locator("text=import requests")).toBeVisible();

    console.log("[API Test] ✓ API documentation is complete");
  });
});
