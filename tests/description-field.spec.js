const { test, expect } = require('@playwright/test');

test.describe('Description Field Feature', () => {
  test.beforeEach(async ({ page }) => {
    // Set global timeout for this test suite
    test.setTimeout(90000);
    
    console.log('[Test] Starting description field test');
    
    // Navigate to admin login
    await page.goto('/admin/login.php');
    await page.waitForLoadState('networkidle');
  });

  test('should create post with description and verify SEO meta tags', async ({ page }) => {
    console.log('[Test] User goal: Create a blog post with description field and verify it appears in SEO meta tags and post preview');
    
    // Login as admin
    console.log('[Test] Logging in as admin...');
    await page.fill('#username', 'admin');
    await page.fill('#password', 'admin');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    // Navigate to create new post
    console.log('[Test] Navigating to create new post...');
    await page.goto('/admin/posts.php?action=new');
    await page.waitForLoadState('networkidle');
    
    // Verify description field exists
    console.log('[Test] Verifying description field exists...');
    const descriptionField = page.locator('#description');
    await expect(descriptionField).toBeVisible();
    
    // Verify character counter exists
    const characterCounter = page.locator('#description-counter');
    await expect(characterCounter).toBeVisible();
    await expect(characterCounter).toContainText('0/500 characters');
    
    // Fill out the form
    const testTitle = 'Test Post with Description ' + Date.now();
    const testDescription = 'This is a test description for SEO and social media. It should appear in meta tags and post previews instead of auto-generated excerpts.';
    const testContent = 'This is the full content of the test post. The description field should be used for previews instead of this content.';
    
    console.log('[Test] Filling out post form...');
    await page.fill('#title', testTitle);
    await page.fill('#description', testDescription);
    await page.fill('#content', testContent);
    
    // Verify character counter updates
    console.log('[Test] Verifying character counter updates...');
    await expect(characterCounter).toContainText(`${testDescription.length}/500 characters`);
    
    // Test character counter color coding
    console.log('[Test] Testing character counter color coding...');
    // Fill with text approaching limit (over 70% of 500 = 350 chars)
    const longText = 'A'.repeat(400);
    await page.fill('#description', longText);
    await page.waitForTimeout(100); // Wait for counter to update
    
    // Check if counter turned red (approaching limit)
    const counterColor = await characterCounter.evaluate(el => getComputedStyle(el).color);
    console.log(`[Test] Counter color at 400 chars: ${counterColor}`);
    
    // Restore original description
    await page.fill('#description', testDescription);
    
    // Uncheck draft status to publish
    await page.uncheck('input[name="is_draft"]');
    
    // Submit the form
    console.log('[Test] Submitting post form...');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    // Verify success message
    console.log('[Test] Verifying post creation success...');
    await expect(page.locator('.alert-success')).toContainText('Post created successfully');
    
    // Navigate to public site to verify post appears
    console.log('[Test] Navigating to public site...');
    await page.goto('/index.php');
    await page.waitForLoadState('networkidle');
    
    // Verify the post appears in the list
    console.log('[Test] Verifying post appears in public list...');
    const postTitle = page.locator('.post-title').filter({ hasText: testTitle });
    await expect(postTitle).toBeVisible();
    
    // Verify the description is used in post preview (not auto-generated excerpt)
    const postContent = page.locator('.post-preview').filter({ hasText: testTitle }).locator('.post-content');
    await expect(postContent).toContainText(testDescription);
    await expect(postContent).not.toContainText(testContent); // Should not show full content
    
    // Click on post to view full post
    console.log('[Test] Clicking on post to view full post...');
    await postTitle.click();
    await page.waitForLoadState('networkidle');
    
    // Verify we're on the post page
    console.log('[Test] Verifying we are on the full post page...');
    await expect(page.locator('h1.post-title')).toContainText(testTitle);
    
    // Verify SEO meta tags contain the description
    console.log('[Test] Verifying SEO meta tags...');
    
    // Check meta description
    const metaDescription = page.locator('meta[name="description"]');
    await expect(metaDescription).toHaveAttribute('content', testDescription);
    
    // Check Open Graph description
    const ogDescription = page.locator('meta[property="og:description"]');
    await expect(ogDescription).toHaveAttribute('content', testDescription);
    
    // Check Twitter description
    const twitterDescription = page.locator('meta[name="twitter:description"]');
    await expect(twitterDescription).toHaveAttribute('content', testDescription);
    
    // Check Open Graph title
    const ogTitle = page.locator('meta[property="og:title"]');
    await expect(ogTitle).toHaveAttribute('content', testTitle);
    
    // Check additional meta tags
    const ogType = page.locator('meta[property="og:type"]');
    await expect(ogType).toHaveAttribute('content', 'article');
    
    const twitterCard = page.locator('meta[name="twitter:card"]');
    await expect(twitterCard).toHaveAttribute('content', 'summary');
    
    console.log('[Test] ✓ Success criteria met: Description appears in SEO meta tags correctly');
    
    // Clean up: Delete the test post
    console.log('[Test] Cleaning up test post...');
    await page.goto('/admin/posts.php');
    await page.waitForLoadState('networkidle');
    
    // Find and delete the test post
    const testPostRow = page.locator('tr').filter({ hasText: testTitle });
    if (await testPostRow.count() > 0) {
      const deleteButton = testPostRow.locator('button').filter({ hasText: 'Delete' });
      await deleteButton.click();
      
      // Confirm deletion in dialog
      page.on('dialog', async dialog => {
        console.log('[Test] Confirming deletion...');
        await dialog.accept();
      });
      
      await page.waitForLoadState('networkidle');
      console.log('[Test] Test post deleted successfully');
    }
    
    console.log('[Test] ✓ All tests completed successfully');
  });

  test('should validate description field length limit', async ({ page }) => {
    console.log('[Test] User goal: Verify description field enforces 500 character limit');
    
    // Login as admin
    await page.fill('#username', 'admin');
    await page.fill('#password', 'admin');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    // Navigate to create new post
    await page.goto('/admin/posts.php?action=new');
    await page.waitForLoadState('networkidle');
    
    // Test with exactly 500 characters
    console.log('[Test] Testing with exactly 500 characters...');
    const exactly500Chars = 'A'.repeat(500);
    await page.fill('#description', exactly500Chars);
    
    const characterCounter = page.locator('#description-counter');
    await expect(characterCounter).toContainText('500/500 characters');
    
    // Test with over 500 characters (should be limited by maxlength attribute)
    console.log('[Test] Testing with over 500 characters...');
    const over500Chars = 'A'.repeat(600);
    await page.fill('#description', over500Chars);
    
    // The field should only contain 500 characters due to maxlength attribute
    const actualValue = await page.inputValue('#description');
    expect(actualValue.length).toBe(500);
    
    console.log('[Test] ✓ Success criteria met: Description field properly limits to 500 characters');
  });

  test('should fallback to excerpt when description is empty', async ({ page }) => {
    console.log('[Test] User goal: Verify system falls back to content excerpt when description is empty');
    
    // Login as admin
    await page.fill('#username', 'admin');
    await page.fill('#password', 'admin');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    // Navigate to create new post
    await page.goto('/admin/posts.php?action=new');
    await page.waitForLoadState('networkidle');
    
    // Create post without description
    const testTitle = 'Test Post Without Description ' + Date.now();
    const testContent = 'This is a long content that should be used to generate an excerpt when no description is provided. The system should automatically create a preview from this content.';
    
    console.log('[Test] Creating post without description...');
    await page.fill('#title', testTitle);
    // Leave description empty
    await page.fill('#content', testContent);
    await page.uncheck('input[name="is_draft"]');
    
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    // Verify post was created
    await expect(page.locator('.alert-success')).toContainText('Post created successfully');
    
    // Check public site
    await page.goto('/index.php');
    await page.waitForLoadState('networkidle');
    
    // Verify the post appears and uses content excerpt
    const postPreview = page.locator('.post-preview').filter({ hasText: testTitle });
    await expect(postPreview).toBeVisible();
    
    // The preview should contain text from the content (excerpt)
    const postContentPreview = postPreview.locator('.post-content');
    await expect(postContentPreview).toContainText('This is a long content');
    
    console.log('[Test] ✓ Success criteria met: System falls back to content excerpt when description is empty');
    
    // Clean up
    await page.goto('/admin/posts.php');
    await page.waitForLoadState('networkidle');
    
    const testPostRow = page.locator('tr').filter({ hasText: testTitle });
    if (await testPostRow.count() > 0) {
      const deleteButton = testPostRow.locator('button').filter({ hasText: 'Delete' });
      await deleteButton.click();
      
      page.on('dialog', async dialog => {
        await dialog.accept();
      });
      
      await page.waitForLoadState('networkidle');
    }
  });
});