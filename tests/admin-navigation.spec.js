/**
 * Admin Navigation Test
 * Verifies that API navigation links are present and functional on all admin pages
 */

const { test, expect } = require("@playwright/test");

test.describe("Admin Navigation Links", () => {
  let page;

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage;

    // Navigate to admin login
    await page.goto("http://localhost:8000/admin/login.php");

    // Login with admin credentials
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');

    // Wait for redirect to dashboard with timeout
    await page.waitForURL("**/admin/index.php", { timeout: 10000 });
  });

  test("should have API link on dashboard page", async () => {
    console.log("[admin-navigation] start - testing dashboard API link");

    // Ensure we're on dashboard
    await page.goto("http://localhost:8000/admin/index.php");
    await page.waitForSelector("nav.admin-nav");

    // Check that API link exists
    const apiLink = page.locator('nav.admin-nav a[href="api.php"]');
    await expect(apiLink).toBeVisible();
    await expect(apiLink).toHaveText("API");

    // Click API link and verify navigation
    await apiLink.click();
    await page.waitForURL("**/admin/api.php");
    await expect(page.locator("h2")).toContainText("API");

    console.log("[admin-navigation] end - dashboard API link test passed");
  });

  test("should have API link on posts page", async () => {
    console.log("[admin-navigation] start - testing posts API link");

    // Navigate to posts page
    await page.goto("http://localhost:8000/admin/posts.php");
    await page.waitForSelector("nav.admin-nav");

    // Check that API link exists
    const apiLink = page.locator('nav.admin-nav a[href="api.php"]');
    await expect(apiLink).toBeVisible();
    await expect(apiLink).toHaveText("API");

    // Click API link and verify navigation
    await apiLink.click();
    await page.waitForURL("**/admin/api.php");
    await expect(page.locator("h2")).toContainText("API");

    console.log("[admin-navigation] end - posts API link test passed");
  });

  test("should have API link on media page", async () => {
    console.log("[admin-navigation] start - testing media API link");

    // Navigate to media page
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForSelector("nav.admin-nav");

    // Check that API link exists
    const apiLink = page.locator('nav.admin-nav a[href="api.php"]');
    await expect(apiLink).toBeVisible();
    await expect(apiLink).toHaveText("API");

    // Click API link and verify navigation
    await apiLink.click();
    await page.waitForURL("**/admin/api.php");
    await expect(page.locator("h2")).toContainText("API");

    console.log("[admin-navigation] end - media API link test passed");
  });

  test("should have API link on profile page", async () => {
    console.log("[admin-navigation] start - testing profile API link");

    // Navigate to profile page
    await page.goto("http://localhost:8000/admin/profile.php");
    await page.waitForSelector("nav.admin-nav");

    // Check that API link exists
    const apiLink = page.locator('nav.admin-nav a[href="api.php"]');
    await expect(apiLink).toBeVisible();
    await expect(apiLink).toHaveText("API");

    // Click API link and verify navigation
    await apiLink.click();
    await page.waitForURL("**/admin/api.php");
    await expect(page.locator("h2")).toContainText("API");

    console.log("[admin-navigation] end - profile API link test passed");
  });

  test("should have API link on session-debug page", async () => {
    console.log("[admin-navigation] start - testing session-debug API link");

    // Navigate to session-debug page
    await page.goto("http://localhost:8000/admin/session-debug.php");
    await page.waitForSelector("nav.admin-nav");

    // Check that API link exists
    const apiLink = page.locator('nav.admin-nav a[href="api.php"]');
    await expect(apiLink).toBeVisible();
    await expect(apiLink).toHaveText("API");

    // Check that Media link also exists (was also missing)
    const mediaLink = page.locator('nav.admin-nav a[href="media.php"]');
    await expect(mediaLink).toBeVisible();
    await expect(mediaLink).toHaveText("Media");

    // Click API link and verify navigation
    await apiLink.click();
    await page.waitForURL("**/admin/api.php");
    await expect(page.locator("h2")).toContainText("API");

    console.log("[admin-navigation] end - session-debug API link test passed");
  });

  test("should have consistent navigation structure across all pages", async () => {
    console.log(
      "[admin-navigation] start - testing consistent navigation structure",
    );

    const adminPages = [
      { url: "http://localhost:8000/admin/index.php", name: "Dashboard" },
      { url: "http://localhost:8000/admin/posts.php", name: "Posts" },
      { url: "http://localhost:8000/admin/media.php", name: "Media" },
      { url: "/admin/profile.php", name: "Profile" },
      { url: "/admin/api.php", name: "API" },
      { url: "/admin/session-debug.php", name: "Session Debug" },
    ];

    const expectedNavItems = [
      { href: "index.php", text: "Dashboard" },
      { href: "posts.php", text: "Posts" },
      { href: "media.php", text: "Media" },
      { href: "profile.php", text: "Profile" },
      { href: "api.php", text: "API" },
      { href: "../index.php", text: "View Site" },
      { href: "logout.php", text: "Logout" },
    ];

    for (const adminPage of adminPages) {
      console.log(
        `[admin-navigation] checking navigation on ${adminPage.name} page`,
      );

      await page.goto(adminPage.url);
      await page.waitForSelector("nav.admin-nav");

      // Check each expected navigation item
      for (const navItem of expectedNavItems) {
        const link = page.locator(`nav.admin-nav a[href="${navItem.href}"]`);
        await expect(link).toBeVisible();
        await expect(link).toHaveText(navItem.text);
      }

      console.log(
        `[admin-navigation] navigation structure verified on ${adminPage.name} page`,
      );
    }

    console.log(
      "[admin-navigation] end - consistent navigation structure test passed",
    );
  });

  test("should navigate between admin pages using navigation menu", async () => {
    console.log("[admin-navigation] start - testing navigation between pages");

    // Start on dashboard
    await page.goto("/admin/index.php");
    await page.waitForSelector("nav.admin-nav");

    // Navigate to Posts
    await page.click('nav.admin-nav a[href="posts.php"]');
    await page.waitForURL("**/admin/posts.php");
    await expect(page.locator('nav.admin-nav a[href="posts.php"]')).toHaveClass(
      /active/,
    );

    // Navigate to Media
    await page.click('nav.admin-nav a[href="media.php"]');
    await page.waitForURL("**/admin/media.php");
    await expect(page.locator('nav.admin-nav a[href="media.php"]')).toHaveClass(
      /active/,
    );

    // Navigate to Profile
    await page.click('nav.admin-nav a[href="profile.php"]');
    await page.waitForURL("**/admin/profile.php");
    await expect(
      page.locator('nav.admin-nav a[href="profile.php"]'),
    ).toHaveClass(/active/);

    // Navigate to API
    await page.click('nav.admin-nav a[href="api.php"]');
    await page.waitForURL("**/admin/api.php");
    await expect(page.locator('nav.admin-nav a[href="api.php"]')).toHaveClass(
      /active/,
    );

    // Navigate back to Dashboard
    await page.click('nav.admin-nav a[href="index.php"]');
    await page.waitForURL("**/admin/index.php");
    await expect(page.locator('nav.admin-nav a[href="index.php"]')).toHaveClass(
      /active/,
    );

    console.log(
      "[admin-navigation] end - navigation between pages test passed",
    );
  });

  test("should display success message for API navigation fix", async () => {
    console.log("[admin-navigation] start - testing success message for fix");

    // Navigate to each previously problematic page and verify API link works
    const problematicPages = [
      "/admin/posts.php",
      "/admin/media.php",
      "/admin/session-debug.php",
    ];

    for (const pageUrl of problematicPages) {
      await page.goto(pageUrl);
      await page.waitForSelector("nav.admin-nav");

      // Check API link exists and is functional
      const apiLink = page.locator('nav.admin-nav a[href="api.php"]');
      await expect(apiLink).toBeVisible();

      // Click and verify navigation works
      await apiLink.click();
      await page.waitForURL("**/admin/api.php");
      await expect(page.locator("h2")).toContainText("API");

      console.log(`API navigation successfully fixed for ${pageUrl}`);
    }

    console.log(
      "[admin-navigation] end - API navigation fix verification passed",
    );
  });

  test.afterEach(async () => {
    // Clean up: logout if still logged in
    try {
      await page.goto("/admin/logout.php");
    } catch (error) {
      // Ignore logout errors during cleanup
    }
  });
});
