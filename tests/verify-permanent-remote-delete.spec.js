const { test, expect } = require("@playwright/test");

test.describe("Verify Permanent Remote Deletion", () => {
  test("should show delete buttons for remote images with permanent deletion warnings", async ({ page }) => {
    console.log("Verifying permanent remote deletion functionality...");

    // Set longer timeout
    test.setTimeout(60000);

    // Navigate to login page
    await page.goto("http://localhost:8000/admin/login.php");
    await page.waitForLoadState("networkidle");

    // Login
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Navigate to media page
    console.log("Navigating to media page...");
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForLoadState("networkidle");

    // Take screenshot for verification
    await page.screenshot({
      path: "test-results/permanent-remote-delete-verification.png",
      fullPage: true,
    });

    // Check page loads correctly
    const pageTitle = await page.locator("h2").textContent();
    console.log(`Page title: ${pageTitle}`);
    expect(pageTitle).toContain("Media Management");

    // Check for media items
    const allMediaItems = page.locator(".media-item");
    const totalItems = await allMediaItems.count();
    console.log(`Total media items found: ${totalItems}`);

    if (totalItems > 0) {
      // Check for remote items with delete buttons
      const remoteItems = page.locator('.media-item[data-type="remote"]');
      const remoteCount = await remoteItems.count();
      console.log(`Remote items found: ${remoteCount}`);

      if (remoteCount > 0) {
        // Check delete buttons for remote images
        const deleteRemoteButtons = remoteItems.locator(".delete-remote-btn");
        const deleteRemoteButtonCount = await deleteRemoteButtons.count();
        console.log(`Delete remote buttons found: ${deleteRemoteButtonCount}`);
        expect(deleteRemoteButtonCount).toBe(remoteCount);
        console.log("✅ Delete buttons present for all remote items");

        // Check first delete remote button properties
        const firstDeleteRemoteBtn = deleteRemoteButtons.first();
        const deleteTitle = await firstDeleteRemoteBtn.getAttribute("title");
        const deleteText = await firstDeleteRemoteBtn.textContent();
        console.log(`Delete remote button title: ${deleteTitle}`);
        console.log(`Delete remote button text: ${deleteText}`);

        expect(deleteTitle).toContain("Delete permanently from posts");
        expect(deleteText).toContain("Delete");
        expect(deleteText).toContain("🗑️");
        console.log("✅ Delete remote button has correct title and text");

        // Test confirmation dialog (but dismiss it)
        console.log("Testing delete remote button confirmation dialog...");
        const firstRemoteItem = remoteItems.first();
        const imageName = await firstRemoteItem.locator(".media-name").textContent();
        const deleteBtn = firstRemoteItem.locator(".delete-remote-btn");

        // Set up dialog handler to check content and dismiss
        page.on("dialog", async (dialog) => {
          console.log(`Dialog appeared: ${dialog.message()}`);
          expect(dialog.type()).toBe("confirm");

          const message = dialog.message();
          expect(message).toContain("permanently delete");
          expect(message).toContain("from all posts");
          expect(message).toContain("cannot be undone");

          console.log("✅ Confirmation dialog contains proper permanent deletion warnings");
          await dialog.dismiss();
        });

        await deleteBtn.click();
        await page.waitForTimeout(1000);

      } else {
        console.log("ℹ️ No remote images found to test");
      }

      // Check for local items with delete buttons (should still work)
      const localItems = page.locator('.media-item[data-type="local"]');
      const localCount = await localItems.count();
      console.log(`Local items found: ${localCount}`);

      if (localCount > 0) {
        const deleteLocalButtons = localItems.locator(".delete-btn");
        const deleteLocalButtonCount = await deleteLocalButtons.count();
        expect(deleteLocalButtonCount).toBe(localCount);
        console.log("✅ Delete buttons present for all local items");
      }

      // Verify no old "remove" buttons exist
      const oldRemoveButtons = page.locator(".remove-btn");
      const oldRemoveButtonCount = await oldRemoveButtons.count();
      expect(oldRemoveButtonCount).toBe(0);
      console.log("✅ No old 'remove' buttons found - successfully replaced with permanent delete");

      // Verify no "restore hidden" functionality remains
      const restoreButtons = page.locator("button").filter({ hasText: "Restore Hidden Images" });
      const restoreButtonCount = await restoreButtons.count();
      expect(restoreButtonCount).toBe(0);
      console.log("✅ No 'Restore Hidden Images' buttons found - temporary hiding removed");

      const hiddenStats = page.locator(".stat-hidden");
      const hiddenStatsCount = await hiddenStats.count();
      expect(hiddenStatsCount).toBe(0);
      console.log("✅ No hidden statistics displayed - session hiding removed");

      // Check statistics display
      const stats = await page.locator(".stat-item").allTextContents();
      console.log("Current statistics:");
      stats.forEach((stat, index) => {
        console.log(`  ${index + 1}: ${stat}`);
      });

      // Verify statistics show correct structure
      const totalFilesText = await page
        .locator(".stat-item")
        .filter({ hasText: "Total Files" })
        .locator(".stat-value")
        .textContent();
      const localFilesText = await page
        .locator(".stat-item")
        .filter({ hasText: "Local Files" })
        .locator(".stat-value")
        .textContent();
      const remoteImagesText = await page
        .locator(".stat-item")
        .filter({ hasText: "Remote Images" })
        .locator(".stat-value")
        .textContent();

      console.log(`Statistics verification:
        - Total Files: ${totalFilesText}
        - Local Files: ${localFilesText}
        - Remote Images: ${remoteImagesText}`);

      const totalFiles = parseInt(totalFilesText);
      const localFiles = parseInt(localFilesText);
      const remoteImages = parseInt(remoteImagesText);

      expect(totalFiles).toBe(localFiles + remoteImages);
      console.log("✅ Statistics calculation correct");

    } else {
      console.log("ℹ️ No media items found on the page");
    }

    console.log("✅ Permanent remote deletion verification completed successfully!");
  });

  test("should verify button styling differences between local and remote", async ({ page }) => {
    console.log("Testing button styling differences...");

    // Login
    await page.goto("http://localhost:8000/admin/login.php");
    await page.waitForLoadState("networkidle");
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Navigate to media page
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForLoadState("networkidle");

    // Check local delete buttons
    const localDeleteButtons = page.locator(".delete-btn");
    const localDeleteCount = await localDeleteButtons.count();
    console.log(`Local delete buttons found: ${localDeleteCount}`);

    if (localDeleteCount > 0) {
      const localBtn = localDeleteButtons.first();
      const localTitle = await localBtn.getAttribute("title");
      expect(localTitle).toContain("Delete file permanently");
      console.log("✅ Local delete button has correct title");
    }

    // Check remote delete buttons
    const remoteDeleteButtons = page.locator(".delete-remote-btn");
    const remoteDeleteCount = await remoteDeleteButtons.count();
    console.log(`Remote delete buttons found: ${remoteDeleteCount}`);

    if (remoteDeleteCount > 0) {
      const remoteBtn = remoteDeleteButtons.first();
      const remoteTitle = await remoteBtn.getAttribute("title");
      expect(remoteTitle).toContain("Delete permanently from posts");
      console.log("✅ Remote delete button has correct title");

      // Test hover effects
      await remoteBtn.hover();
      await page.waitForTimeout(500);

      const remoteHoverStyles = await remoteBtn.evaluate(el => {
        const styles = window.getComputedStyle(el);
        return {
          backgroundColor: styles.backgroundColor,
          borderColor: styles.borderColor,
          color: styles.color
        };
      });

      console.log("Remote delete button hover styles:", remoteHoverStyles);
      console.log("✅ Remote delete button hover effect verified");
    }

    console.log("✅ Button styling verification completed!");
  });
});
