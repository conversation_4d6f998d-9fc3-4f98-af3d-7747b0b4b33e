const { test, expect } = require("@playwright/test");

test.describe("Verify Delete and Remove Buttons", () => {
  test("should display delete and remove buttons on media page", async ({ page }) => {
    console.log("Verifying buttons are present and functional...");

    // Set longer timeout
    test.setTimeout(60000);

    // Navigate to login page
    await page.goto("http://localhost:8000/admin/login.php");
    await page.waitForLoadState("networkidle");

    // Login
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Navigate to media page
    console.log("Navigating to media page...");
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForLoadState("networkidle");

    // Take initial screenshot
    await page.screenshot({
      path: "test-results/media-buttons-initial.png",
      fullPage: true,
    });

    // Check for page title
    const pageTitle = await page.locator("h2").textContent();
    console.log(`Page title: ${pageTitle}`);
    expect(pageTitle).toContain("Media Management");

    // Check for media items
    const allMediaItems = page.locator(".media-item");
    const totalItems = await allMediaItems.count();
    console.log(`Total media items found: ${totalItems}`);

    if (totalItems > 0) {
      // Check for local items with delete buttons
      const localItems = page.locator('.media-item[data-type="local"]');
      const localCount = await localItems.count();
      console.log(`Local items found: ${localCount}`);

      if (localCount > 0) {
        const deleteButtons = localItems.locator(".delete-btn");
        const deleteButtonCount = await deleteButtons.count();
        console.log(`Delete buttons found: ${deleteButtonCount}`);
        expect(deleteButtonCount).toBe(localCount);
        console.log("✅ Delete buttons present for all local items");

        // Check first delete button properties
        const firstDeleteBtn = deleteButtons.first();
        const deleteTitle = await firstDeleteBtn.getAttribute("title");
        const deleteText = await firstDeleteBtn.textContent();
        console.log(`Delete button title: ${deleteTitle}`);
        console.log(`Delete button text: ${deleteText}`);
        expect(deleteTitle).toContain("Delete file permanently");
        expect(deleteText).toContain("Delete");
      }

      // Check for remote items with remove buttons
      const remoteItems = page.locator('.media-item[data-type="remote"]');
      const remoteCount = await remoteItems.count();
      console.log(`Remote items found: ${remoteCount}`);

      if (remoteCount > 0) {
        const removeButtons = remoteItems.locator(".remove-btn");
        const removeButtonCount = await removeButtons.count();
        console.log(`Remove buttons found: ${removeButtonCount}`);
        expect(removeButtonCount).toBe(remoteCount);
        console.log("✅ Remove buttons present for all remote items");

        // Check first remove button properties
        const firstRemoveBtn = removeButtons.first();
        const removeTitle = await firstRemoveBtn.getAttribute("title");
        const removeText = await firstRemoveBtn.textContent();
        console.log(`Remove button title: ${removeTitle}`);
        console.log(`Remove button text: ${removeText}`);
        expect(removeTitle).toContain("Remove from list");
        expect(removeText).toContain("Remove");
      }

      // Test clicking a delete button (but dismiss the dialog)
      if (localCount > 0) {
        console.log("Testing delete button click...");
        const firstLocalItem = localItems.first();
        const fileName = await firstLocalItem.locator(".media-name").textContent();
        const deleteBtn = firstLocalItem.locator(".delete-btn");

        // Set up dialog handler to dismiss
        page.on("dialog", async (dialog) => {
          console.log(`Dialog appeared: ${dialog.message()}`);
          expect(dialog.type()).toBe("confirm");
          expect(dialog.message()).toContain("permanently delete");
          expect(dialog.message()).toContain(fileName.trim());
          await dialog.dismiss();
          console.log("✅ Delete confirmation dialog works correctly");
        });

        await deleteBtn.click();
        await page.waitForTimeout(1000);
      }

      // Test clicking a remove button (but dismiss the dialog)
      if (remoteCount > 0) {
        console.log("Testing remove button click...");
        const firstRemoteItem = remoteItems.first();
        const imageName = await firstRemoteItem.locator(".media-name").textContent();
        const removeBtn = firstRemoteItem.locator(".remove-btn");

        // Set up dialog handler to dismiss
        page.on("dialog", async (dialog) => {
          console.log(`Dialog appeared: ${dialog.message()}`);
          expect(dialog.type()).toBe("confirm");
          expect(dialog.message()).toContain("Remove");
          expect(dialog.message()).toContain("Restore Hidden Images");
          await dialog.dismiss();
          console.log("✅ Remove confirmation dialog works correctly");
        });

        await removeBtn.click();
        await page.waitForTimeout(1000);
      }

      // Check statistics
      const stats = await page.locator(".stat-item").allTextContents();
      console.log("Statistics found:");
      stats.forEach((stat, index) => {
        console.log(`  ${index + 1}: ${stat}`);
      });

      // Check for hidden images indicator
      const hiddenStat = page.locator(".stat-hidden");
      const hiddenCount = await hiddenStat.count();
      if (hiddenCount > 0) {
        const hiddenText = await hiddenStat.textContent();
        console.log(`Hidden images indicator: ${hiddenText}`);

        // Check for restore button
        const restoreBtn = page.locator("button").filter({ hasText: "Restore Hidden Images" });
        const restoreBtnCount = await restoreBtn.count();
        if (restoreBtnCount > 0) {
          console.log("✅ Restore Hidden Images button found");
        }
      } else {
        console.log("No hidden images currently");
      }

    } else {
      console.log("⚠️ No media items found on the page");
    }

    // Take final screenshot
    await page.screenshot({
      path: "test-results/media-buttons-final.png",
      fullPage: true,
    });

    console.log("✅ Button verification test completed successfully!");
  });

  test("should verify button styling and layout", async ({ page }) => {
    console.log("Testing button styling and layout...");

    // Login
    await page.goto("http://localhost:8000/admin/login.php");
    await page.waitForLoadState("networkidle");
    await page.fill('input[name="username"]', "admin");
    await page.fill('input[name="password"]', "admin");
    await page.click('button[type="submit"]');
    await page.waitForLoadState("networkidle");

    // Navigate to media page
    await page.goto("http://localhost:8000/admin/media.php");
    await page.waitForLoadState("networkidle");

    // Check button styling
    const deleteButtons = page.locator(".delete-btn");
    const removeButtons = page.locator(".remove-btn");

    if ((await deleteButtons.count()) > 0) {
      const deleteBtn = deleteButtons.first();
      const deleteStyles = await deleteBtn.evaluate((el) => {
        const styles = window.getComputedStyle(el);
        return {
          backgroundColor: styles.backgroundColor,
          color: styles.color,
          border: styles.border,
          borderRadius: styles.borderRadius,
        };
      });
      console.log("Delete button styles:", deleteStyles);
      console.log("✅ Delete button styling verified");
    }

    if ((await removeButtons.count()) > 0) {
      const removeBtn = removeButtons.first();
      const removeStyles = await removeBtn.evaluate((el) => {
        const styles = window.getComputedStyle(el);
        return {
          backgroundColor: styles.backgroundColor,
          color: styles.color,
          border: styles.border,
          borderRadius: styles.borderRadius,
        };
      });
      console.log("Remove button styles:", removeStyles);
      console.log("✅ Remove button styling verified");
    }

    // Check hover effects by simulating hover
    if ((await deleteButtons.count()) > 0) {
      await deleteButtons.first().hover();
      await page.waitForTimeout(500);
      console.log("✅ Delete button hover effect tested");
    }

    if ((await removeButtons.count()) > 0) {
      await removeButtons.first().hover();
      await page.waitForTimeout(500);
      console.log("✅ Remove button hover effect tested");
    }

    console.log("✅ Button styling verification completed!");
  });
});
