const { test, expect } = require('@playwright/test');

test.describe('Mandatory Fields Simple Test', () => {
    test.beforeEach(async ({ page }) => {
        // Set longer timeout for this test suite
        test.setTimeout(60000);
        
        // Navigate to login page
        await page.goto('/admin/login.php');
        await page.waitForLoadState('networkidle');
        
        // Login with correct credentials
        await page.fill('#username', 'admin');
        await page.fill('#password', 'admin');
        await page.click('button[type="submit"]');
        
        // Wait for redirect - be flexible about the destination
        await page.waitForTimeout(2000);
        
        // Navigate directly to new post page
        await page.goto('/admin/posts.php?action=new');
        await page.waitForLoadState('networkidle');
    });

    test('description field should be text input with required attribute', async ({ page }) => {
        console.log('[test-mandatory-simple] Testing description field type and attributes');
        
        const descriptionField = page.locator('#description');
        
        // Check that it exists
        await expect(descriptionField).toBeVisible();
        
        // Check that it's an input element (not textarea)
        const tagName = await descriptionField.evaluate(el => el.tagName.toLowerCase());
        expect(tagName).toBe('input');
        
        // Check that it's type="text"
        const inputType = await descriptionField.getAttribute('type');
        expect(inputType).toBe('text');
        
        // Check that it has required attribute
        const isRequired = await descriptionField.getAttribute('required');
        expect(isRequired).not.toBeNull();
        
        // Check maxlength attribute
        const maxLength = await descriptionField.getAttribute('maxlength');
        expect(maxLength).toBe('500');
        
        console.log('[test-mandatory-simple] ✅ Description field is correctly configured as required text input');
    });

    test('required field labels should have asterisks', async ({ page }) => {
        console.log('[test-mandatory-simple] Testing required field labels have asterisks');
        
        // Check title label
        const titleLabel = page.locator('label[for="title"]');
        await expect(titleLabel).toContainText('*');
        
        // Check description label  
        const descriptionLabel = page.locator('label[for="description"]');
        await expect(descriptionLabel).toContainText('*');
        
        // Check thumbnail label
        const thumbnailLabel = page.locator('label').filter({ hasText: 'Thumbnail Image *' });
        await expect(thumbnailLabel).toBeVisible();
        
        console.log('[test-mandatory-simple] ✅ All required field labels show asterisks');
    });

    test('character counter should work with text input', async ({ page }) => {
        console.log('[test-mandatory-simple] Testing character counter functionality');
        
        const descriptionField = page.locator('#description');
        const counter = page.locator('#description-counter');
        
        // Initially should show 0/500
        await expect(counter).toContainText('0/500 characters');
        
        // Type some text
        const testText = 'This is a test description for the post';
        await descriptionField.fill(testText);
        
        // Counter should update
        const expectedCount = `${testText.length}/500 characters`;
        await expect(counter).toContainText(expectedCount);
        
        console.log('[test-mandatory-simple] ✅ Character counter working correctly');
    });

    test('form validation should prevent submission without description', async ({ page }) => {
        console.log('[test-mandatory-simple] Testing form validates missing description');
        
        // Fill other required fields but leave description empty
        await page.fill('#title', 'Test Post Title');
        await page.fill('#content', 'This is test content for the post.');
        await page.selectOption('#category', 'tech');
        
        // Add thumbnail URL
        await page.click('#url-tab-btn');
        await page.waitForTimeout(500);
        await page.fill('#thumbnail_url', 'https://picsum.photos/800/600');
        
        // Set up dialog handler for validation alert
        let alertTriggered = false;
        page.on('dialog', async dialog => {
            console.log('[test-mandatory-simple] Alert triggered:', dialog.message());
            alertTriggered = true;
            expect(dialog.message()).toContain('Description is required');
            await dialog.accept();
        });
        
        // Try to submit without description
        await page.click('button[type="submit"]');
        await page.waitForTimeout(1000);
        
        // Verify alert was triggered
        expect(alertTriggered).toBe(true);
        
        console.log('[test-mandatory-simple] ✅ Form correctly validates missing description');
    });

    test('form validation should prevent submission without thumbnail', async ({ page }) => {
        console.log('[test-mandatory-simple] Testing form validates missing thumbnail');
        
        // Fill all fields except thumbnail
        await page.fill('#title', 'Test Post Title');
        await page.fill('#description', 'This is a test description');
        await page.fill('#content', 'This is test content for the post.');
        await page.selectOption('#category', 'tech');
        
        // Set up dialog handler for validation alert
        let alertTriggered = false;
        page.on('dialog', async dialog => {
            console.log('[test-mandatory-simple] Alert triggered:', dialog.message());
            alertTriggered = true;
            expect(dialog.message()).toContain('Thumbnail is required');
            await dialog.accept();
        });
        
        // Try to submit without thumbnail
        await page.click('button[type="submit"]');
        await page.waitForTimeout(1000);
        
        // Verify alert was triggered
        expect(alertTriggered).toBe(true);
        
        console.log('[test-mandatory-simple] ✅ Form correctly validates missing thumbnail');
    });

    test('successful post creation with all required fields', async ({ page }) => {
        console.log('[test-mandatory-simple] Testing successful post creation');
        
        // Fill all required fields
        const postTitle = `Test Post ${Date.now()}`;
        await page.fill('#title', postTitle);
        await page.fill('#description', 'This is a test description for the post');
        await page.fill('#content', 'This is test content for the post with all required fields filled.');
        await page.selectOption('#category', 'tech');
        
        // Add thumbnail URL
        await page.click('#url-tab-btn');
        await page.waitForTimeout(500);
        await page.fill('#thumbnail_url', 'https://picsum.photos/800/600');
        
        // Submit the form
        await page.click('button[type="submit"]');
        
        // Wait for redirect or success
        await page.waitForTimeout(3000);
        
        // Should redirect to posts list or show success
        const currentUrl = page.url();
        const isOnPostsList = currentUrl.includes('/admin/posts.php');
        const isOnSamePage = currentUrl.includes('action=new');
        
        // Either we're on the posts list (successful redirect) or still on the form (with success message)
        expect(isOnPostsList || isOnSamePage).toBe(true);
        
        console.log('[test-mandatory-simple] ✅ Post creation completed successfully');
    });
});