<?php
/**
 * Security Utilities Class
 * Handles CSRF protection, input validation, and security headers
 */

class Security {
    
    /**
     * Generate CSRF token
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Validate CSRF token
     */
    public static function validateCSRFToken($token) {
        if (!isset($_SESSION['csrf_token'])) {
            return false;
        }
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Sanitize input data
     */
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'sanitizeInput'], $input);
        }
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate email format
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validate string length
     */
    public static function validateLength($string, $min = 1, $max = 255) {
        $length = strlen($string);
        return $length >= $min && $length <= $max;
    }
    
    /**
     * Generate secure random string
     */
    public static function generateRandomString($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }
    
    /**
     * Set security headers
     */
    public static function setSecurityHeaders() {
        // Prevent XSS attacks
        header('X-XSS-Protection: 1; mode=block');

        // Prevent MIME type sniffing
        header('X-Content-Type-Options: nosniff');

        // Prevent clickjacking
        header('X-Frame-Options: DENY');

        // Strict transport security (if using HTTPS)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }

        // Content Security Policy
        header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://unpkg.com; style-src 'self' 'unsafe-inline' https://unpkg.com; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';");

        // Referrer Policy
        header('Referrer-Policy: strict-origin-when-cross-origin');

        // Remove server signature headers
        header_remove('Server');
        header_remove('X-Powered-By');
    }

    /**
     * Check if file access is allowed
     */
    public static function isFileAccessAllowed($filePath) {
        $fileName = basename($filePath);
        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        // Check blocked extensions
        if (Config::isBlockedExtension($fileExtension)) {
            return false;
        }

        // Check blocked directories
        if (Config::isBlockedDirectory($filePath)) {
            return false;
        }

        return true;
    }

    /**
     * Check if request is for a potentially dangerous file upload
     */
    public static function isExecutableFile($fileName) {
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        return Config::isExecutableExtension($extension);
    }

    /**
     * Force HTTPS redirect if configured
     */
    public static function enforceHTTPS($force = false) {
        if ($force && (!isset($_SERVER['HTTPS']) || $_SERVER['HTTPS'] !== 'on')) {
            $redirectURL = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
            header("Location: $redirectURL", true, 301);
            exit;
        }
    }
    
    /**
     * Get client IP address
     */
    public static function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Handle comma-separated IPs (X-Forwarded-For)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Rate limiting for login attempts
     */
    public static function checkRateLimit($ip, $maxAttempts = 5, $timeWindow = 900) { // 15 minutes
        $db = Database::getInstance();
        
        // Clean old attempts
        $db->query(
            "DELETE FROM login_attempts WHERE attempted_at < datetime('now', '-{$timeWindow} seconds')"
        );
        
        // Count recent failed attempts from this IP
        $attempts = $db->fetchOne(
            "SELECT COUNT(*) as count FROM login_attempts 
             WHERE ip_address = ? AND success = 0 AND attempted_at > datetime('now', '-{$timeWindow} seconds')",
            [$ip]
        );
        
        return ($attempts['count'] ?? 0) < $maxAttempts;
    }
    
    /**
     * Log login attempt
     */
    public static function logLoginAttempt($ip, $username = null, $success = false) {
        $db = Database::getInstance();
        $db->query(
            "INSERT INTO login_attempts (ip_address, username, success) VALUES (?, ?, ?)",
            [$ip, $username, $success ? 1 : 0]
        );
    }
    
    /**
     * Escape output for HTML display
     */
    public static function escape($string) {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate password strength
     */
    public static function validatePassword($password) {
        // Minimum 8 characters, at least one letter and one number
        return strlen($password) >= 8 && 
               preg_match('/[A-Za-z]/', $password) && 
               preg_match('/[0-9]/', $password);
    }
}
?>
