<?php
/**
 * Image Upload and Processing Class
 * Handles image uploads, resizing, and cropping for post thumbnails
 */

class ImageUpload {
    private $uploadDir;
    private $allowedTypes;
    private $maxFileSize;
    private $maxWidth;
    private $maxHeight;
    
    public function __construct() {
        $this->uploadDir = PUBLIC_PATH . '/uploads/thumbnails/';
        $this->allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $this->maxFileSize = 5 * 1024 * 1024; // 5MB
        $this->maxWidth = 1920;
        $this->maxHeight = 1080;
        
        // Create upload directory if it doesn't exist
        $this->createUploadDirectory();
    }
    
    /**
     * Create upload directory structure
     */
    private function createUploadDirectory() {
        if (!is_dir($this->uploadDir)) {
            if (!mkdir($this->uploadDir, 0755, true)) {
                throw new Exception('Failed to create upload directory');
            }
        }
        
        // Check directory is writable
        if (!is_writable($this->uploadDir)) {
            throw new Exception('Upload directory is not writable');
        }
        
        // Create .htaccess to prevent PHP execution in upload directory
        $htaccessPath = $this->uploadDir . '.htaccess';
        if (!file_exists($htaccessPath)) {
            file_put_contents($htaccessPath, "Options -ExecCGI\nAddHandler cgi-script .php .pl .py .jsp .asp .sh .cgi\n");
        }
    }
    
    /**
     * Upload and process image
     */
    public function uploadImage($file, $cropData = null) {
        try {
            // Validate file upload
            $this->validateUpload($file);
            
            // Generate unique filename
            $originalName = pathinfo($file['name'], PATHINFO_FILENAME);
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $filename = $this->generateUniqueFilename($originalName, $extension);
            $filepath = $this->uploadDir . $filename;
            
            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                throw new Exception('Failed to move uploaded file.');
            }
            
            // Process image (resize if too large)
            $this->processImage($filepath);
            
            // Apply cropping if provided
            if ($cropData) {
                $this->cropImage($filepath, $cropData);
            }
            
            // Return the URL path
            return '/uploads/thumbnails/' . $filename;
            
        } catch (Exception $e) {
            // Clean up file if it was moved
            if (isset($filepath) && file_exists($filepath)) {
                unlink($filepath);
            }
            throw $e;
        }
    }
    
    /**
     * Validate file upload
     */
    private function validateUpload($file) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            switch ($file['error']) {
                case UPLOAD_ERR_INI_SIZE:
                    throw new Exception('File is too large. Maximum allowed size: ' . ini_get('upload_max_filesize') . '. File size: ' . round($file['size'] / 1024 / 1024, 2) . 'MB');
                case UPLOAD_ERR_FORM_SIZE:
                    throw new Exception('File exceeds the maximum allowed size set in the form.');
                case UPLOAD_ERR_PARTIAL:
                    throw new Exception('File upload was interrupted.');
                case UPLOAD_ERR_NO_FILE:
                    throw new Exception('No file was uploaded.');
                default:
                    throw new Exception('File upload failed with error code: ' . $file['error']);
            }
        }
        
        // Check file size
        if ($file['size'] > $this->maxFileSize) {
            $fileSizeMB = round($file['size'] / 1024 / 1024, 2);
            $maxSizeMB = ($this->maxFileSize / 1024 / 1024);
            throw new Exception('File size (' . $fileSizeMB . 'MB) exceeds maximum allowed size of ' . $maxSizeMB . 'MB. Current PHP upload_max_filesize: ' . ini_get('upload_max_filesize'));
        }
        
        // Check MIME type
        $mimeType = mime_content_type($file['tmp_name']);
        if (!in_array($mimeType, $this->allowedTypes)) {
            throw new Exception('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.');
        }
        
        // Additional security: check file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        if (!in_array($extension, $allowedExtensions)) {
            throw new Exception('Invalid file extension.');
        }
        
        // Verify it's actually an image
        $imageInfo = getimagesize($file['tmp_name']);
        if (!$imageInfo) {
            throw new Exception('File is not a valid image.');
        }
    }
    
    /**
     * Generate unique filename
     */
    private function generateUniqueFilename($originalName, $extension) {
        $safeName = preg_replace('/[^a-zA-Z0-9_-]/', '', $originalName);
        $safeName = substr($safeName, 0, 50); // Limit length
        
        if (empty($safeName)) {
            $safeName = 'image';
        }
        
        // Generate unique filename with existence check
        $maxAttempts = 100;
        $attempts = 0;
        
        do {
            $timestamp = microtime(true);
            $timestampStr = sprintf('%.3f', $timestamp);
            $timestampStr = str_replace('.', '', $timestampStr);
            $random = bin2hex(random_bytes(6)); // Increased random bytes for better uniqueness
            
            $filename = $safeName . '_' . $timestampStr . '_' . $random . '.' . $extension;
            $filepath = $this->uploadDir . $filename;
            
            $attempts++;
            
        } while (file_exists($filepath) && $attempts < $maxAttempts);
        
        if ($attempts >= $maxAttempts) {
            throw new Exception('Unable to generate unique filename after ' . $maxAttempts . ' attempts');
        }
        
        return $filename;
    }
    
    /**
     * Process image (resize if too large)
     */
    private function processImage($filepath) {
        $imageInfo = getimagesize($filepath);
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        $mimeType = $imageInfo['mime'];
        
        // Check if resizing is needed
        if ($width <= $this->maxWidth && $height <= $this->maxHeight) {
            return; // No resizing needed
        }
        
        // Calculate new dimensions
        $ratio = min($this->maxWidth / $width, $this->maxHeight / $height);
        $newWidth = round($width * $ratio);
        $newHeight = round($height * $ratio);
        
        // Create image resource from file
        $sourceImage = $this->createImageFromFile($filepath, $mimeType);
        if (!$sourceImage) {
            throw new Exception('Failed to create image resource.');
        }
        
        // Create new image
        $newImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($mimeType === 'image/png' || $mimeType === 'image/gif') {
            imagealphablending($newImage, false);
            imagesavealpha($newImage, true);
            $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
            imagefilledrectangle($newImage, 0, 0, $newWidth, $newHeight, $transparent);
        }
        
        // Resize image
        imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
        
        // Save resized image
        $this->saveImage($newImage, $filepath, $mimeType);
        
        // Clean up memory
        imagedestroy($sourceImage);
        imagedestroy($newImage);
    }
    
    /**
     * Crop image based on crop data
     */
    private function cropImage($filepath, $cropData) {
        $imageInfo = getimagesize($filepath);
        $mimeType = $imageInfo['mime'];
        
        // Validate crop data
        $x = max(0, intval($cropData['x']));
        $y = max(0, intval($cropData['y']));
        $width = max(1, intval($cropData['width']));
        $height = max(1, intval($cropData['height']));
        
        // Create image resource
        $sourceImage = $this->createImageFromFile($filepath, $mimeType);
        if (!$sourceImage) {
            throw new Exception('Failed to create image resource for cropping.');
        }
        
        // Create cropped image
        $croppedImage = imagecreatetruecolor($width, $height);
        
        // Preserve transparency
        if ($mimeType === 'image/png' || $mimeType === 'image/gif') {
            imagealphablending($croppedImage, false);
            imagesavealpha($croppedImage, true);
            $transparent = imagecolorallocatealpha($croppedImage, 255, 255, 255, 127);
            imagefilledrectangle($croppedImage, 0, 0, $width, $height, $transparent);
        }
        
        // Copy cropped portion
        imagecopy($croppedImage, $sourceImage, 0, 0, $x, $y, $width, $height);
        
        // Save cropped image
        $this->saveImage($croppedImage, $filepath, $mimeType);
        
        // Clean up memory
        imagedestroy($sourceImage);
        imagedestroy($croppedImage);
    }
    
    /**
     * Create image resource from file
     */
    private function createImageFromFile($filepath, $mimeType) {
        switch ($mimeType) {
            case 'image/jpeg':
                return imagecreatefromjpeg($filepath);
            case 'image/png':
                return imagecreatefrompng($filepath);
            case 'image/gif':
                return imagecreatefromgif($filepath);
            case 'image/webp':
                return imagecreatefromwebp($filepath);
            default:
                return false;
        }
    }
    
    /**
     * Save image to file
     */
    private function saveImage($imageResource, $filepath, $mimeType, $quality = 85) {
        switch ($mimeType) {
            case 'image/jpeg':
                return imagejpeg($imageResource, $filepath, $quality);
            case 'image/png':
                // PNG quality is 0-9, convert from 0-100
                $pngQuality = round((100 - $quality) / 10);
                return imagepng($imageResource, $filepath, $pngQuality);
            case 'image/gif':
                return imagegif($imageResource, $filepath);
            case 'image/webp':
                return imagewebp($imageResource, $filepath, $quality);
            default:
                return false;
        }
    }
    
    /**
     * Delete uploaded image
     */
    public function deleteImage($imagePath) {
        // Security check: ensure path is within upload directory
        $realUploadDir = realpath($this->uploadDir);
        $realImagePath = realpath(PUBLIC_PATH . $imagePath);
        
        if ($realImagePath && strpos($realImagePath, $realUploadDir) === 0) {
            if (file_exists($realImagePath)) {
                return unlink($realImagePath);
            }
        }
        
        return false;
    }
    
    /**
     * Get image dimensions
     */
    public function getImageDimensions($imagePath) {
        $fullPath = PUBLIC_PATH . $imagePath;
        if (file_exists($fullPath)) {
            $imageInfo = getimagesize($fullPath);
            if ($imageInfo) {
                return [
                    'width' => $imageInfo[0],
                    'height' => $imageInfo[1],
                    'mime' => $imageInfo['mime']
                ];
            }
        }
        return false;
    }
    
    /**
     * Check if GD extension is available
     */
    public static function isGdAvailable() {
        return extension_loaded('gd');
    }
    
    /**
     * Get supported image formats
     */
    public static function getSupportedFormats() {
        if (!self::isGdAvailable()) {
            return [];
        }
        
        $formats = [];
        if (imagetypes() & IMG_JPEG) $formats[] = 'JPEG';
        if (imagetypes() & IMG_PNG) $formats[] = 'PNG';
        if (imagetypes() & IMG_GIF) $formats[] = 'GIF';
        if (function_exists('imagewebp')) $formats[] = 'WebP';
        
        return $formats;
    }
    
    /**
     * Validate crop data
     */
    public function validateCropData($cropData, $imageWidth, $imageHeight) {
        if (!is_array($cropData)) {
            return false;
        }
        
        $required = ['x', 'y', 'width', 'height'];
        foreach ($required as $field) {
            if (!isset($cropData[$field]) || !is_numeric($cropData[$field])) {
                return false;
            }
        }
        
        $x = intval($cropData['x']);
        $y = intval($cropData['y']);
        $width = intval($cropData['width']);
        $height = intval($cropData['height']);
        
        // Check bounds
        if ($x < 0 || $y < 0 || $width <= 0 || $height <= 0) {
            return false;
        }
        
        if ($x + $width > $imageWidth || $y + $height > $imageHeight) {
            return false;
        }
        
        return true;
    }
}
?>