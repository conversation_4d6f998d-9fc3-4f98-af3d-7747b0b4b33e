<?php
/**
 * Authentication Class
 * Handles user authentication, password management, and account security
 */

class Auth
{
    private $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Authenticate user with username and password
     */
    public function login($username, $password)
    {
        $ip = Security::getClientIP();

        // Check rate limiting
        if (!Security::checkRateLimit($ip)) {
            Security::logLoginAttempt($ip, $username, false);
            throw new Exception(
                "Too many login attempts. Please try again later."
            );
        }

        // Get user from database
        $user = $this->db->fetchOne("SELECT * FROM users WHERE username = ?", [
            $username,
        ]);

        if (!$user) {
            Security::logLoginAttempt($ip, $username, false);
            throw new Exception("Invalid username or password.");
        }

        // Check if account is locked
        if (
            $user["locked_until"] &&
            strtotime($user["locked_until"]) > time()
        ) {
            Security::logLoginAttempt($ip, $username, false);
            throw new Exception(
                "Account is temporarily locked. Please try again later."
            );
        }

        // Verify password
        if (!password_verify($password, $user["password_hash"])) {
            // Increment failed attempts
            $this->incrementFailedAttempts($user["id"]);
            Security::logLoginAttempt($ip, $username, false);
            throw new Exception("Invalid username or password.");
        }

        // Reset failed attempts on successful login
        $this->resetFailedAttempts($user["id"]);

        // Update last login
        $this->db->query(
            "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
            [$user["id"]]
        );

        // Log successful attempt
        Security::logLoginAttempt($ip, $username, true);

        // Start session
        Session::login($user["id"]);

        return $user;
    }

    /**
     * Logout current user
     */
    public function logout()
    {
        Session::logout();
    }

    /**
     * Get current authenticated user
     */
    public function getCurrentUser()
    {
        $userId = Session::getUserId();
        if (!$userId) {
            return null;
        }

        return $this->db->fetchOne(
            "SELECT id, username, email, created_at, last_login, api_token FROM users WHERE id = ?",
            [$userId]
        );
    }

    /**
     * Check if user is authenticated
     */
    public function isAuthenticated()
    {
        return Session::isLoggedIn();
    }

    /**
     * Change user password
     */
    public function changePassword($userId, $currentPassword, $newPassword)
    {
        // Get current user
        $user = $this->db->fetchOne(
            "SELECT password_hash FROM users WHERE id = ?",
            [$userId]
        );

        if (!$user) {
            throw new Exception("User not found.");
        }

        // Verify current password
        if (!password_verify($currentPassword, $user["password_hash"])) {
            throw new Exception("Current password is incorrect.");
        }

        // Validate new password
        if (!Security::validatePassword($newPassword)) {
            throw new Exception(
                "New password must be at least 8 characters long and contain both letters and numbers."
            );
        }

        // Hash new password
        $newPasswordHash = password_hash($newPassword, PASSWORD_ARGON2ID);

        // Update password
        $this->db->query("UPDATE users SET password_hash = ? WHERE id = ?", [
            $newPasswordHash,
            $userId,
        ]);

        return true;
    }

    /**
     * Increment failed login attempts
     */
    private function incrementFailedAttempts($userId)
    {
        $this->db->query(
            "UPDATE users SET failed_login_attempts = failed_login_attempts + 1 WHERE id = ?",
            [$userId]
        );

        // Lock account after 5 failed attempts for 30 minutes
        $user = $this->db->fetchOne(
            "SELECT failed_login_attempts FROM users WHERE id = ?",
            [$userId]
        );

        if ($user["failed_login_attempts"] >= 5) {
            $lockUntil = date("Y-m-d H:i:s", time() + 1800); // 30 minutes
            $this->db->query("UPDATE users SET locked_until = ? WHERE id = ?", [
                $lockUntil,
                $userId,
            ]);
        }
    }

    /**
     * Reset failed login attempts
     */
    private function resetFailedAttempts($userId)
    {
        $this->db->query(
            "UPDATE users SET failed_login_attempts = 0, locked_until = NULL WHERE id = ?",
            [$userId]
        );
    }

    /**
     * Require authentication (redirect if not authenticated)
     */
    public function requireAuth($redirectUrl = null)
    {
        if (!$this->isAuthenticated()) {
            // Default redirect URL based on current location
            if ($redirectUrl === null) {
                $currentPath = $_SERVER["REQUEST_URI"];
                if (strpos($currentPath, "/admin/") !== false) {
                    $redirectUrl = "login.php"; // Relative to admin directory
                } else {
                    $redirectUrl = "/admin/login.php"; // Absolute path
                }
            }

            // Clean any output buffer before redirect
            while (ob_get_level()) {
                ob_end_clean();
            }

            header("Location: " . $redirectUrl);
            exit();
        }
    }

    /**
     * Create new user (for future expansion)
     */
    public function createUser($username, $password, $email = null)
    {
        // Validate input
        if (!Security::validateLength($username, 3, 50)) {
            throw new Exception(
                "Username must be between 3 and 50 characters."
            );
        }

        if (!Security::validatePassword($password)) {
            throw new Exception(
                "Password must be at least 8 characters long and contain both letters and numbers."
            );
        }

        if ($email && !Security::validateEmail($email)) {
            throw new Exception("Invalid email address.");
        }

        // Check if username already exists
        $existing = $this->db->fetchOne(
            "SELECT id FROM users WHERE username = ?",
            [$username]
        );

        if ($existing) {
            throw new Exception("Username already exists.");
        }

        // Hash password
        $passwordHash = password_hash($password, PASSWORD_ARGON2ID);

        // Insert user
        $this->db->query(
            "INSERT INTO users (username, password_hash, email) VALUES (?, ?, ?)",
            [$username, $passwordHash, $email]
        );

        return $this->db->lastInsertId();
    }

    /**
     * Generate a new API token for a user
     */
    public function generateApiToken($userId)
    {
        $token = bin2hex(random_bytes(32)); // 64 character hex string

        $this->db->query("UPDATE users SET api_token = ? WHERE id = ?", [
            $token,
            $userId,
        ]);

        return $token;
    }

    /**
     * Validate API token and return user if valid
     */
    public function validateApiToken($token)
    {
        if (empty($token)) {
            return false;
        }

        $user = $this->db->fetchOne(
            "SELECT id, username FROM users WHERE api_token = ? AND username = 'admin'",
            [$token]
        );

        return $user ?: false;
    }

    /**
     * Get user by API token
     */
    public function getUserByApiToken($token)
    {
        return $this->db->fetchOne(
            "SELECT id, username, email FROM users WHERE api_token = ?",
            [$token]
        );
    }
}
?>
