<?php
/**
 * Simple YAML Parser for Scraper Configurations
 * Basic YAML parsing functionality for news scraper configurations
 */

// Prevent direct access
if (!defined('CMS_INIT')) {
    die('Direct access not allowed');
}

class YamlParser {
    
    /**
     * Parse a YAML string into an associative array
     */
    public static function parse($yamlString) {
        echo '[YamlParser::parse] start' . PHP_EOL;
        
        if (empty($yamlString)) {
            echo '[YamlParser::parse] end - empty input' . PHP_EOL;
            return [];
        }
        
        $result = [];
        $lines = explode("\n", $yamlString);
        $currentKey = null;
        $currentValue = '';
        $inMultiline = false;
        $indentLevel = 0;
        $currentSection = null;
        
        foreach ($lines as $lineNum => $line) {
            $trimmedLine = trim($line);
            
            // Skip empty lines and comments
            if (empty($trimmedLine) || strpos($trimmedLine, '#') === 0) {
                continue;
            }
            
            // Calculate indentation
            $indent = strlen($line) - strlen(ltrim($line));
            
            // Handle multiline values (|)
            if ($inMultiline) {
                if ($indent > $indentLevel) {
                    $currentValue .= "\n" . substr($line, $indentLevel + 2);
                    continue;
                } else {
                    // End of multiline
                    $result[$currentKey] = trim($currentValue);
                    $inMultiline = false;
                    $currentValue = '';
                }
            }
            
            // Parse key-value pairs
            if (strpos($trimmedLine, ':') !== false) {
                list($key, $value) = explode(':', $trimmedLine, 2);
                $key = trim($key);
                $value = trim($value);
                
                // Remove quotes from values
                $value = trim($value, '"\'');
                
                // Handle multiline indicator
                if ($value === '|') {
                    $inMultiline = true;
                    $currentKey = $key;
                    $indentLevel = $indent;
                    $currentValue = '';
                    continue;
                }
                
                // Handle nested objects (like selectors)
                if (empty($value) && $indent === 0) {
                    $currentSection = $key;
                    $result[$key] = [];
                    continue;
                }
                
                // Handle nested values
                if ($currentSection && $indent > 0) {
                    $result[$currentSection][$key] = $value;
                } else {
                    $result[$key] = $value;
                }
            }
            
            // Handle array items (starting with -)
            if (strpos($trimmedLine, '- ') === 0) {
                $value = trim(substr($trimmedLine, 2));
                
                // Handle array items with key-value pairs
                if (strpos($value, ':') !== false) {
                    list($itemKey, $itemValue) = explode(':', $value, 2);
                    $itemKey = trim($itemKey);
                    $itemValue = trim($itemValue, '"\'');
                    
                    if (!isset($result[$currentSection])) {
                        $result[$currentSection] = [];
                    }
                    
                    // Create array if it doesn't exist
                    if (!is_array($result[$currentSection])) {
                        $result[$currentSection] = [];
                    }
                    
                    // Add to array
                    $lastIndex = count($result[$currentSection]);
                    if (!isset($result[$currentSection][$lastIndex])) {
                        $result[$currentSection][$lastIndex] = [];
                    }
                    $result[$currentSection][$lastIndex][$itemKey] = $itemValue;
                } else {
                    // Simple array item
                    if (!isset($result[$currentSection])) {
                        $result[$currentSection] = [];
                    }
                    $result[$currentSection][] = trim($value, '"\'');
                }
            }
        }
        
        // Handle final multiline value
        if ($inMultiline && !empty($currentValue)) {
            $result[$currentKey] = trim($currentValue);
        }
        
        echo '[YamlParser::parse] end - parsed ' . count($result) . ' keys' . PHP_EOL;
        return $result;
    }
    
    /**
     * Parse a YAML file
     */
    public static function parseFile($filePath) {
        echo '[YamlParser::parseFile] start - ' . $filePath . PHP_EOL;
        
        if (!file_exists($filePath)) {
            throw new Exception("YAML file not found: " . $filePath);
        }
        
        $content = file_get_contents($filePath);
        if ($content === false) {
            throw new Exception("Failed to read YAML file: " . $filePath);
        }
        
        $result = self::parse($content);
        
        echo '[YamlParser::parseFile] end - successfully parsed file' . PHP_EOL;
        return $result;
    }
    
    /**
     * Validate a scraper configuration array
     */
    public static function validateScraperConfig($config) {
        echo '[YamlParser::validateScraperConfig] start' . PHP_EOL;
        
        $required = ['site_name', 'source_url', 'selectors'];
        $errors = [];
        
        foreach ($required as $field) {
            if (!isset($config[$field]) || empty($config[$field])) {
                $errors[] = "Missing required field: " . $field;
            }
        }
        
        // Validate selectors
        if (isset($config['selectors'])) {
            $requiredSelectors = ['title', 'article_url'];
            foreach ($requiredSelectors as $selector) {
                if (!isset($config['selectors'][$selector]) || empty($config['selectors'][$selector])) {
                    $errors[] = "Missing required selector: " . $selector;
                }
            }
        }
        
        // Validate URL format
        if (isset($config['source_url']) && !filter_var($config['source_url'], FILTER_VALIDATE_URL)) {
            $errors[] = "Invalid URL format for source_url";
        }
        
        echo '[YamlParser::validateScraperConfig] end - ' . count($errors) . ' errors found' . PHP_EOL;
        return $errors;
    }
}
?>
