<?php
/**
 * Response Class
 * Handles HTTP responses, compression, and caching
 */

class Response {
    private static $headers = [];
    private static $compressionEnabled = true;
    
    /**
     * Set a header
     */
    public static function setHeader($name, $value) {
        self::$headers[$name] = $value;
        header("$name: $value");
    }
    
    /**
     * Enable or disable compression
     */
    public static function setCompression($enabled) {
        self::$compressionEnabled = $enabled;
    }
    
    /**
     * Start output buffering with compression if enabled
     */
    public static function startOutput() {
        if (self::$compressionEnabled && self::canCompress()) {
            ob_start([self::class, 'compressOutput']);
        } else {
            ob_start();
        }
    }
    
    /**
     * Check if compression is supported
     */
    private static function canCompress() {
        return function_exists('gzencode') && 
               !ini_get('zlib.output_compression') &&
               strpos($_SERVER['HTTP_ACCEPT_ENCODING'] ?? '', 'gzip') !== false;
    }
    
    /**
     * Compress output callback
     */
    public static function compressOutput($buffer) {
        // Don't compress if this is a redirect response
        $statusCode = http_response_code();
        if ($statusCode >= 300 && $statusCode < 400) {
            return $buffer;
        }

        // Only compress if content is large enough and is compressible
        if (strlen($buffer) < 1024) {
            return $buffer;
        }

        $contentType = '';
        foreach (headers_list() as $header) {
            if (stripos($header, 'content-type:') === 0) {
                $contentType = strtolower($header);
                break;
            }
        }

        // Compress text-based content types
        $compressibleTypes = ['text/', 'application/javascript', 'application/json', 'application/xml'];
        $shouldCompress = false;

        foreach ($compressibleTypes as $type) {
            if (strpos($contentType, $type) !== false) {
                $shouldCompress = true;
                break;
            }
        }

        if ($shouldCompress && !headers_sent()) {
            header('Content-Encoding: gzip');
            header('Vary: Accept-Encoding');
            return gzencode($buffer);
        }

        return $buffer;
    }
    
    /**
     * Set cache headers for dynamic content
     */
    public static function setCacheHeaders($maxAge = 0, $public = false) {
        if ($maxAge > 0) {
            $cacheControl = $public ? 'public' : 'private';
            self::setHeader('Cache-Control', "$cacheControl, max-age=$maxAge");
            self::setHeader('Expires', gmdate('D, d M Y H:i:s', time() + $maxAge) . ' GMT');
        } else {
            // No cache
            self::setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
            self::setHeader('Pragma', 'no-cache');
            self::setHeader('Expires', '0');
        }
    }
    
    /**
     * Set content type
     */
    public static function setContentType($type, $charset = 'UTF-8') {
        self::setHeader('Content-Type', "$type; charset=$charset");
    }
    
    /**
     * Send JSON response
     */
    public static function json($data, $statusCode = 200) {
        http_response_code($statusCode);
        self::setContentType('application/json');
        self::setCacheHeaders(0); // Don't cache JSON responses by default
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }
    
    /**
     * Send redirect response
     */
    public static function redirect($url, $permanent = false) {
        $statusCode = $permanent ? 301 : 302;
        http_response_code($statusCode);
        header("Location: $url");
        exit;
    }
    
    /**
     * Send file download response
     */
    public static function download($filePath, $fileName = null, $mimeType = null) {
        if (!file_exists($filePath)) {
            http_response_code(404);
            echo "File not found";
            exit;
        }
        
        $fileName = $fileName ?: basename($filePath);
        $mimeType = $mimeType ?: self::getMimeType($filePath);
        
        self::setHeader('Content-Type', $mimeType);
        self::setHeader('Content-Disposition', 'attachment; filename="' . $fileName . '"');
        self::setHeader('Content-Length', filesize($filePath));
        self::setHeader('Cache-Control', 'no-cache');
        
        readfile($filePath);
        exit;
    }
    
    /**
     * Get MIME type for file
     */
    private static function getMimeType($filePath) {
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        
        $mimeTypes = [
            'txt' => 'text/plain',
            'html' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
            'json' => 'application/json',
            'xml' => 'application/xml',
            'pdf' => 'application/pdf',
            'zip' => 'application/zip',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon',
            'mp3' => 'audio/mpeg',
            'mp4' => 'video/mp4',
            'avi' => 'video/x-msvideo',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        
        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
    
    /**
     * End output and send response
     */
    public static function send() {
        if (ob_get_level()) {
            ob_end_flush();
        }
    }
    
    /**
     * Set security headers for file uploads
     */
    public static function setFileUploadHeaders() {
        self::setHeader('X-Content-Type-Options', 'nosniff');
        self::setHeader('Content-Disposition', 'attachment');
    }
    
    /**
     * Handle conditional requests (ETag, Last-Modified)
     */
    public static function handleConditionalRequest($lastModified, $etag = null) {
        $lastModifiedHeader = gmdate('D, d M Y H:i:s', $lastModified) . ' GMT';
        self::setHeader('Last-Modified', $lastModifiedHeader);
        
        if ($etag) {
            self::setHeader('ETag', '"' . $etag . '"');
        }
        
        // Check If-Modified-Since
        if (isset($_SERVER['HTTP_IF_MODIFIED_SINCE'])) {
            $ifModifiedSince = strtotime($_SERVER['HTTP_IF_MODIFIED_SINCE']);
            if ($ifModifiedSince >= $lastModified) {
                http_response_code(304);
                exit;
            }
        }
        
        // Check If-None-Match (ETag)
        if ($etag && isset($_SERVER['HTTP_IF_NONE_MATCH'])) {
            $ifNoneMatch = trim($_SERVER['HTTP_IF_NONE_MATCH'], '"');
            if ($ifNoneMatch === $etag) {
                http_response_code(304);
                exit;
            }
        }
    }
}
?>
