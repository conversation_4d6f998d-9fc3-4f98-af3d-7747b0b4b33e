#!/usr/bin/env php
<?php
/**
 * Corrected Migration Script: Clean Up Old Test Posts
 *
 * This script removes test posts created before the THISISATEST naming convention
 * was implemented. It uses EXACT matches to avoid false positives and protect
 * legitimate content.
 *
 * Usage: php migrate-old-test-posts-corrected.php
 */

// Set error reporting
error_reporting(E_ALL);
ini_set("display_errors", 1);

// Ensure this is running from command line
if (php_sapi_name() !== "cli") {
    echo "This script must be run from the command line.\n";
    exit(1);
}

echo "🔄 Corrected Old Test Posts Migration Script\n";
echo "============================================\n\n";

try {
    // Connect to database
    $dbPath = __DIR__ . "/database/cms.db";
    if (!file_exists($dbPath)) {
        throw new Exception("Database file not found: " . $dbPath);
    }

    $pdo = new PDO("sqlite:" . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "📊 Scanning for old test posts...\n";

    // Define EXACT old test patterns - NO WILDCARDS to prevent false positives
    $exactTestPatterns = [
        "Test from Browser Interface",
        "API Test Post for Verification",
        "Test Post via API",
        "API Test Post for Cleanup",
        "Test Post for Cleanup 1",  // Specific exact match instead of wildcard
        "Test Post for Cleanup 2",  // Additional specific matches if needed
        "Test Post for Cleanup 3",
        "Image Download Test",
        "Gaming Post Test",
        "Film Review: Test Movie"
    ];

    // Define additional patterns that were commonly used in tests
    $additionalTestPatterns = [
        "Test Post Should Be Deleted",
        "Complete API Test with All Fields",
        "Final API Test with Thumbnail and Description",
        "New Technology Breakthrough",
        "My Daily Thoughts",
        "Breaking: API Integration Complete"
    ];

    // Combine all patterns
    $allTestPatterns = array_merge($exactTestPatterns, $additionalTestPatterns);

    echo "📋 Exact patterns to check for:\n";
    foreach ($allTestPatterns as $i => $pattern) {
        echo "   " . ($i + 1) . ". '$pattern'\n";
    }
    echo "\n";

    // Build WHERE clause with exact matches only
    $whereConditions = [];
    $params = [];

    foreach ($allTestPatterns as $pattern) {
        $whereConditions[] = "title = ?";
        $params[] = $pattern;
    }

    // Add condition to exclude posts that already follow new convention
    $whereConditions[] = "title NOT LIKE 'THISISATEST%'";

    $whereClause = "(" . implode(' OR ', array_slice($whereConditions, 0, -1)) . ") AND " . end($whereConditions);

    $selectQuery = "SELECT id, title, created_at FROM posts WHERE $whereClause ORDER BY id";

    $stmt = $pdo->prepare($selectQuery);
    $stmt->execute($params);
    $oldTestPosts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($oldTestPosts)) {
        echo "✅ No old test posts found with exact pattern matching.\n";

        echo "\n🔍 Checking for any posts with suspicious patterns:\n";

        // Check for posts that might be test posts but don't match exact patterns
        $suspiciousPatterns = [
            "title LIKE '%Test%'",
            "title LIKE '%API%'",
            "title LIKE '%test%'",
            "title LIKE '%Testing%'"
        ];

        $suspiciousQuery = "SELECT id, title, created_at FROM posts WHERE (" .
                          implode(' OR ', $suspiciousPatterns) .
                          ") AND title NOT LIKE 'THISISATEST%' ORDER BY id";

        $suspiciousStmt = $pdo->prepare($suspiciousQuery);
        $suspiciousStmt->execute();
        $suspiciousPosts = $suspiciousStmt->fetchAll(PDO::FETCH_ASSOC);

        if (!empty($suspiciousPosts)) {
            echo "⚠️  Found " . count($suspiciousPosts) . " posts with test-like patterns:\n";
            foreach ($suspiciousPosts as $post) {
                echo "   📝 ID {$post['id']}: {$post['title']}\n";
                echo "      Created: {$post['created_at']}\n";
                echo "      ❓ Review manually - could be legitimate content\n\n";
            }
        } else {
            echo "✅ No suspicious patterns found.\n";
        }

        exit(0);
    }

    echo "🔍 Found " . count($oldTestPosts) . " old test posts with EXACT pattern matching:\n\n";

    // Display found old test posts
    foreach ($oldTestPosts as $post) {
        echo "   📝 ID {$post['id']}: {$post['title']}\n";
        echo "      Created: {$post['created_at']}\n\n";
    }

    // Ask for confirmation
    echo "⚠️  SAFE MIGRATION:\n";
    echo "   These posts match EXACT test patterns used before THISISATEST convention.\n";
    echo "   Using precise matching to prevent false positives.\n\n";
    echo "   Continue with deletion of these " . count($oldTestPosts) . " confirmed test posts? (y/N): ";

    $handle = fopen("php://stdin", "r");
    $confirmation = trim(fgets($handle));
    fclose($handle);

    if (strtolower($confirmation) !== 'y' && strtolower($confirmation) !== 'yes') {
        echo "❌ Migration cancelled.\n";
        echo "💡 Note: Run again when ready to clean up these exact test posts.\n";
        exit(0);
    }

    echo "\n🗑️  Safely migrating (deleting) exact test posts...\n";

    // Delete old test posts
    $pdo->beginTransaction();
    $deletedCount = 0;
    $errors = [];

    foreach ($oldTestPosts as $post) {
        try {
            $deleteStmt = $pdo->prepare('DELETE FROM posts WHERE id = ? AND title = ?');
            // Double-check with both ID and title for extra safety
            if ($deleteStmt->execute([$post['id'], $post['title']])) {
                $deletedCount++;
                echo "   ✅ Deleted ID {$post['id']}: " . substr($post['title'], 0, 60) . "...\n";
            } else {
                $errors[] = "Failed to delete ID {$post['id']}";
                echo "   ❌ Failed to delete ID {$post['id']}\n";
            }
        } catch (Exception $e) {
            $errors[] = "Error deleting ID {$post['id']}: " . $e->getMessage();
            echo "   ❌ Error deleting ID {$post['id']}: " . $e->getMessage() . "\n";
        }
    }

    if (empty($errors)) {
        $pdo->commit();
        echo "\n✅ All deletions successful. Transaction committed.\n";
    } else {
        $pdo->rollback();
        echo "\n❌ Some deletions failed. Transaction rolled back.\n";
        echo "Errors encountered:\n";
        foreach ($errors as $error) {
            echo "   - $error\n";
        }
        exit(1);
    }

    // Log migration activity
    $logMessage = date('Y-m-d H:i:s') . " - CORRECTED Migration: Cleaned up $deletedCount old test posts using exact pattern matching\n";
    $logPath = __DIR__ . '/logs/test-cleanup.log';

    // Create logs directory if it doesn't exist
    $logsDir = dirname($logPath);
    if (!is_dir($logsDir)) {
        mkdir($logsDir, 0755, true);
    }

    file_put_contents($logPath, $logMessage, FILE_APPEND | LOCK_EX);

    echo "\n🎉 Safe migration completed successfully!\n";
    echo "📊 Deleted $deletedCount confirmed test posts.\n";
    echo "📝 Activity logged to: logs/test-cleanup.log\n\n";

    // Verify migration
    echo "🔍 Verifying migration results...\n";

    $verifyStmt = $pdo->prepare($selectQuery);
    $verifyStmt->execute($params);
    $remainingOldPosts = $verifyStmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($remainingOldPosts)) {
        echo "✅ Verification: All targeted test posts have been successfully removed.\n";
    } else {
        echo "⚠️  Warning: " . count($remainingOldPosts) . " targeted posts still remain:\n";
        foreach ($remainingOldPosts as $post) {
            echo "   - ID {$post['id']}: {$post['title']}\n";
        }
    }

    // Check for any THISISATEST posts
    $newConventionStmt = $pdo->prepare("SELECT COUNT(*) FROM posts WHERE title LIKE 'THISISATEST%'");
    $newConventionStmt->execute();
    $newConventionCount = $newConventionStmt->fetchColumn();

    // Check total posts
    $totalStmt = $pdo->prepare("SELECT COUNT(*) FROM posts");
    $totalStmt->execute();
    $totalCount = $totalStmt->fetchColumn();

    echo "\n📊 Final database status:\n";
    echo "   - Total posts: $totalCount\n";
    echo "   - Old test posts remaining: " . count($remainingOldPosts) . "\n";
    echo "   - New convention (THISISATEST) posts: $newConventionCount\n";

    if (count($remainingOldPosts) == 0) {
        echo "\n🌟 Safe migration complete! Database is ready for THISISATEST convention.\n";
        echo "\n📋 Next steps:\n";
        echo "   1. All future test posts MUST start with 'THISISATEST'\n";
        echo "   2. Use 'php cleanup-test-posts.php' for routine cleanup\n";
        echo "   3. Use '/test-cleanup.php' API endpoint for automated cleanup\n";
        echo "   4. The cleanup system now uses only exact pattern matching for safety\n";
    }

} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    error_log("Corrected Migration Error: " . $e->getMessage());
    exit(1);
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    error_log("Corrected Migration Error: " . $e->getMessage());
    exit(1);
}

echo "\nCorrected migration complete! 🚀\n";
echo "✅ No false positives possible with exact pattern matching.\n";
?>
