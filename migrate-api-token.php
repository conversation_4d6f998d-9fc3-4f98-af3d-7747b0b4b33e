<?php
/**
 * Database Migration: Add API Token Column
 * Adds api_token column to users table for REST API authentication
 */

// Set error reporting
error_reporting(E_ALL);
ini_set("display_errors", 1);

echo "Starting API Token Migration...\n";

try {
    // Connect to database
    $pdo = new PDO("sqlite:database/cms.db");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Check if api_token column already exists
    $stmt = $pdo->query("PRAGMA table_info(users)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $hasApiToken = false;
    foreach ($columns as $column) {
        if ($column["name"] === "api_token") {
            $hasApiToken = true;
            break;
        }
    }

    if ($hasApiToken) {
        echo "✓ api_token column already exists\n";
    } else {
        // Add api_token column (without UNIQUE constraint first)
        $pdo->exec("ALTER TABLE users ADD COLUMN api_token VARCHAR(64)");
        echo "✓ Added api_token column to users table\n";

        // Create unique index on api_token
        $pdo->exec(
            "CREATE UNIQUE INDEX idx_users_api_token ON users(api_token) WHERE api_token IS NOT NULL"
        );
        echo "✓ Created unique index on api_token column\n";
    }

    // Generate API tokens for existing admin users
    $stmt = $pdo->prepare(
        "SELECT id, username, api_token FROM users WHERE username = 'admin'"
    );
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($users as $user) {
        if (empty($user["api_token"])) {
            // Generate secure token
            $token = bin2hex(random_bytes(32)); // 64 character hex string

            $updateStmt = $pdo->prepare(
                "UPDATE users SET api_token = ? WHERE id = ?"
            );
            $updateStmt->execute([$token, $user["id"]]);

            echo "✓ Generated API token for user: " . $user["username"] . "\n";
            echo "  Token: " . $token . "\n";
        } else {
            echo "✓ User " . $user["username"] . " already has API token\n";
        }
    }

    echo "\n🎉 Migration completed successfully!\n";
    echo "Admin users now have API tokens for REST API access.\n";
} catch (PDOException $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
