<?php
/**
 * Migration Script: Add Description Column to Posts Table
 * Adds a description field for SEO meta tags and post previews
 */

// Prevent direct access
if (!defined('CMS_INIT')) {
    define('CMS_INIT', true);
    require_once 'src/includes/init.php';
}

echo "Starting migration: Add description column to posts table\n";

try {
    $db = Database::getInstance();
    $pdo = $db->getPdo();
    
    // Check if description column already exists
    $result = $pdo->query("PRAGMA table_info(posts)");
    $columns = $result->fetchAll(PDO::FETCH_ASSOC);
    
    $hasDescription = false;
    foreach ($columns as $column) {
        if ($column['name'] === 'description') {
            $hasDescription = true;
            break;
        }
    }
    
    if ($hasDescription) {
        echo "Description column already exists. Migration skipped.\n";
        exit;
    }
    
    // Begin transaction
    $pdo->beginTransaction();
    
    // Add description column
    $pdo->exec("ALTER TABLE posts ADD COLUMN description TEXT DEFAULT NULL");
    echo "✓ Added description column to posts table\n";
    
    // Generate placeholder descriptions for existing posts
    // Use ~200 character lorem ipsum text
    $loremIpsum = "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco.";
    
    // Get all existing posts
    $existingPosts = $pdo->query("SELECT id, title FROM posts WHERE description IS NULL")->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($existingPosts)) {
        $updateStmt = $pdo->prepare("UPDATE posts SET description = ? WHERE id = ?");
        
        foreach ($existingPosts as $post) {
            $updateStmt->execute([$loremIpsum, $post['id']]);
        }
        
        echo "✓ Added placeholder descriptions to " . count($existingPosts) . " existing posts\n";
    }
    
    // Commit transaction
    $pdo->commit();
    
    echo "Migration completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Update your posts to add proper descriptions\n";
    echo "2. The description field will be used for:\n";
    echo "   - SEO meta descriptions\n";
    echo "   - Social media previews (Facebook, Twitter)\n";
    echo "   - Post previews on the main page\n";
    
} catch (Exception $e) {
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollback();
    }
    
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>