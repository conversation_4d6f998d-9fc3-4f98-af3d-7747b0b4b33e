<!DOCTYPE html>
<html><head><title>Session Persistence Test</title><style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
.container { max-width: 1000px; margin: 0 auto; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.success { color: green; font-weight: bold; }
.info { color: blue; font-weight: bold; }
.box { margin: 15px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
.highlight { background-color: #fff3cd; padding: 10px; margin: 10px 0; border: 1px solid #ffeaa7; border-radius: 3px; }
.critical { background-color: #f8d7da; border-color: #f5c6cb; }
button { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
.btn-primary { background: #007bff; color: white; }
.btn-success { background: #28a745; color: white; }
.btn-danger { background: #dc3545; color: white; }
.btn-warning { background: #ffc107; color: black; }
.btn-secondary { background: #6c757d; color: white; }
</style></head><body>
<div class='container'><h1>🔍 Session Persistence Test</h1><p>This test will help identify exactly why your sessions are expiring early.</p><div class='box'><h2>📊 Current Session Status</h2><p><strong>Current Time:</strong> 2025-06-09 17:16:33</p><p class='error'>❌ No active session</p></div><div class='box'><h2>⚙️ Session Configuration</h2><table><tr><th>Setting</th><th>Value</th><th>Status</th></tr><tr><td>App Session Lifetime</td><td>7200s (2h)</td><td>Target lifetime</td></tr><tr><td>PHP gc_maxlifetime</td><td>7200s (2h)</td><td>✅ OK</td></tr><tr><td>PHP cookie_lifetime</td><td>7200s</td><td>✅ Match</td></tr><tr><td>Regenerate Interval</td><td>1800s (30m)</td><td>ID regeneration frequency</td></tr></table></div><div class='box'><h2>🧪 Test Actions</h2><form method='post' style='display: inline-block; margin-right: 20px;'><h3>Create Session:</h3><button type='submit' name='action' value='create_test_session' class='btn-success'>🧪 Create Test Session (Memory Only)</button><button type='submit' name='action' value='create_full_session' class='btn-primary'>💾 Create Full Session (With Database)</button><button type='button' onclick='window.location.reload()' class='btn-secondary'>🔄 Refresh Page</button></form></div><div class='box'><h2>📋 Testing Protocol</h2><h3>To Test Session Persistence:</h3><ol><li><strong>Create a session</strong> (either type)</li><li><strong>Note the creation time</strong> displayed above</li><li><strong>Wait 15-20 minutes</strong> then refresh this page</li><li><strong>Check if session still exists:</strong><ul><li>If session is gone → Check which component failed</li><li>If session exists → Continue waiting until it fails</li></ul></li><li><strong>Use 'Ping Session'</strong> to simulate user activity</li></ol><h3>To Test Session Regeneration Issues:</h3><ol><li>Create a full session</li><li>Wait until 'Next Regeneration' shows 'DUE NOW'</li><li>Refresh the page (this will trigger regeneration)</li><li>Check if session survives the regeneration</li></ol></div><div class='box'><h2>📡 Real-time Monitor</h2><p>This section will auto-update every 30 seconds when enabled:</p><button id='monitorBtn' onclick='toggleMonitor()' class='btn-success'>▶️ Start Real-time Monitoring</button><div id='monitorStatus' style='margin: 10px 0; font-weight: bold;'>Monitor: Stopped</div><script>let monitoring = false;let monitorInterval;function toggleMonitor() {  const btn = document.getElementById('monitorBtn');  const status = document.getElementById('monitorStatus');    if (monitoring) {    monitoring = false;    clearInterval(monitorInterval);    btn.textContent = '▶️ Start Real-time Monitoring';    btn.className = 'btn-success';    status.textContent = 'Monitor: Stopped';    status.style.color = 'red';  } else {    monitoring = true;    btn.textContent = '⏸️ Stop Real-time Monitoring';    btn.className = 'btn-warning';    status.textContent = 'Monitor: Active (refreshing every 30s)';    status.style.color = 'green';        monitorInterval = setInterval(function() {      if (monitoring) {        window.location.reload();      }    }, 30000);  }}</script></div><div class='box'><h2>🔧 Debug Information</h2><details><summary>Click to expand technical details</summary><h4>PHP Session Info:</h4><pre>Session ID: 8fe7df50b6651dbefa6702c982e56481
Session Status: 2 (1=disabled, 2=none, 3=active)
Session Save Path: default
Session Save Handler: files
</pre><h4>All Session Data:</h4><pre>last_regeneration = 1749489393
</pre><h4>Cookie Information:</h4><pre>No relevant cookies found
</pre></details></div><hr><p><small>Last updated: 2025-06-09 17:16:33 | Session timeout configured for 2h</small></p></div></body></html>