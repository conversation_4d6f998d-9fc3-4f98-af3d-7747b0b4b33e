# News Scraper Administration Page Implementation Plan

## Overview
Create a comprehensive news scraper administration system with YAML-based configuration, shared admin navigation, and web scraping capabilities using undetected_chromedriver.

## Architecture Diagrams

### System Architecture
```mermaid
graph TB
    A[Admin Interface] --> B[Scraper Page]
    B --> C[YAML Config Manager]
    B --> D[Web Scraper Engine]
    C --> E[news_src/ Directory]
    E --> F[YAML Config Files]
    D --> G[undetected_chromedriver]
    D --> H[Article Extraction]
    H --> I[CMS Database]
    
    subgraph "Admin Navigation"
        J[Shared Header]
        K[Shared Footer]
        L[Navigation Component]
    end
    
    A --> J
    A --> K
    A --> L
```

### Database Schema (if needed)
```mermaid
erDiagram
    SCRAPER_CONFIGS {
        id INTEGER PK
        filename VARCHAR
        site_name VARCHAR
        source_url VARCHAR
        created_at DATETIME
        updated_at DATETIME
        status VARCHAR
    }
    
    SCRAPED_ARTICLES {
        id INTEGER PK
        config_id INTEGER FK
        title VARCHAR
        url VARCHAR
        thumbnail VARCHAR
        scraped_at DATETIME
        imported BOOLEAN
    }
```

## Implementation Checklist

### Phase 1: Admin Interface Refactoring
- [ ] Create shared admin header component (`public/admin/includes/header.php`)
- [ ] Create shared admin footer component (`public/admin/includes/footer.php`)
- [ ] Create navigation helper function with active page detection
- [ ] Refactor all existing admin pages to use shared components
- [ ] Add "Scraper" navigation link to all admin pages
- [ ] Test navigation consistency across all admin pages

### Phase 2: YAML Configuration System
- [ ] Create YAML parser utility class (`src/classes/YamlParser.php`)
- [ ] Create scraper configuration manager (`src/classes/ScraperConfig.php`)
- [ ] Implement YAML file validation and error handling
- [ ] Create sample OpenAI configuration file (`news_src/openai.yaml`)
- [ ] Test YAML parsing and validation

### Phase 3: Web Scraping Engine
- [ ] Install undetected_chromedriver via package manager
- [ ] Create web scraper class (`src/classes/WebScraper.php`)
- [ ] Implement CSS selector-based content extraction
- [ ] Add error handling and retry mechanisms
- [ ] Create scraping result data structures
- [ ] Test scraping with OpenAI news page

### Phase 4: Scraper Admin Page
- [ ] Create main scraper page (`public/admin/scraper.php`)
- [ ] Implement configuration file listing
- [ ] Add configuration file content display
- [ ] Create configuration file upload/edit functionality
- [ ] Add scraping test functionality
- [ ] Implement scraping results display
- [ ] Add success/error messaging

### Phase 5: OpenAI News Analysis
- [ ] Analyze OpenAI news page structure
- [ ] Identify CSS selectors for articles
- [ ] Create OpenAI YAML configuration
- [ ] Test scraping functionality
- [ ] Validate extracted data

### Phase 6: Testing & Documentation
- [ ] Create Playwright tests for scraper functionality
- [ ] Test admin navigation consistency
- [ ] Test YAML configuration management
- [ ] Test web scraping functionality
- [ ] Update FUNCTIONS.md with new functions
- [ ] Create user documentation

## Technical Requirements

### Security & Best Practices
- Use prepared statements for any database operations
- Implement proper input validation and sanitization
- Use CSRF protection for forms
- Follow PSR coding standards
- Implement proper error logging

### Server-Agnostic Implementation
- Avoid Apache-specific .htaccess dependencies
- Use PHP-based routing and security headers
- Implement cross-platform file operations
- Use relative paths for includes

### MVC Architecture
- Separate business logic from presentation
- Use controller pattern for admin actions
- Implement model classes for data management
- Keep views focused on presentation

### Functional Programming
- Use pure functions where possible
- Minimize side effects
- Implement immutable data structures where applicable
- Use function composition for complex operations

## File Structure
```
public/admin/
├── includes/
│   ├── header.php          # Shared admin header
│   ├── footer.php          # Shared admin footer
│   └── navigation.php      # Navigation helper
├── scraper.php             # Main scraper admin page
└── [existing files...]

src/classes/
├── YamlParser.php          # YAML parsing utility
├── ScraperConfig.php       # Configuration management
└── WebScraper.php          # Web scraping engine

news_src/
├── openai.yaml             # OpenAI configuration
└── [other site configs...]

tests/
└── scraper-functionality.spec.js  # Playwright tests
```

## Success Criteria
1. All admin pages use shared navigation components
2. Scraper page accessible from admin navigation
3. YAML configuration files can be managed through admin interface
4. Web scraping works with undetected_chromedriver
5. OpenAI news page can be successfully scraped
6. All functionality passes Playwright tests
7. Code follows security best practices and MVC architecture

## Next Steps
Start with Phase 1: Admin Interface Refactoring to establish the foundation for the scraper functionality.
